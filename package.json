{"name": "fincloud", "version": "14.2.2", "scripts": {"ng": "nx", "postinstall": "node npm-postinstall.js", "prestart": "shx cp -n ./apps/fincloud/proxy.conf.json.sample ./apps/fincloud/proxy.conf.json", "start": "cross-env NODE_OPTIONS=--max-old-space-size=8192 npx nx serve fincloud", "start:en": "cross-env NODE_OPTIONS=--max-old-space-size=8192 npx nx serve --project=fincloud --configuration=en", "build": "node --max_old_space_size=6115 node_modules/.bin/nx run-many --target=build --all --nxBail", "lint": "npx nx run-many --target=lint --quiet --all --nxBail", "lint:fix": "npx nx run-many --target=lint --quiet --fix --all", "i18n:extract": "nx extract-i18n fincloud --format=json --output-path apps/fincloud/src/locale", "generate-swagger:local": "ng-openapi-gen --input swagger.json --output libs/swagger-generator/{{swagger-service-name}}/src/lib/", "stylelint": "stylelint 'apps/fincloud/src/**/*.scss' 'libs/**/*.scss'"}, "private": true, "dependencies": {"-": "0.0.1", "@ag-grid-community/angular": "30.1.0", "@ag-grid-community/client-side-row-model": "30.1.0", "@ag-grid-community/styles": "30.1.0", "@ag-grid-enterprise/clipboard": "30.1.0", "@ag-grid-enterprise/core": "30.1.0", "@ag-grid-enterprise/excel-export": "30.1.0", "@ag-grid-enterprise/menu": "30.1.0", "@ag-grid-enterprise/range-selection": "30.1.0", "@angular-slider/ngx-slider": "17.0.2", "@angular/animations": "17.3.11", "@angular/cdk": "17.3.10", "@angular/common": "17.3.11", "@angular/compiler": "17.3.11", "@angular/core": "17.3.11", "@angular/forms": "17.3.11", "@angular/localize": "17.3.11", "@angular/platform-browser": "17.3.11", "@angular/platform-browser-dynamic": "17.3.11", "@angular/router": "17.3.11", "@azure/msal-angular": "3.0.19", "@azure/msal-browser": "3.16.0", "@ctrl/ngx-codemirror": "7.0.0", "@fincloud/ui": "0.0.643", "@fincloud/stylelint-plugin": "0.0.8", "@joint/layout-directed-graph": "^4.0.3", "@larscom/ngrx-store-storagesync": "14.2.0", "@ng-bootstrap/ng-bootstrap": "16.0.0", "@ng-select/ng-select": "12.0.7", "@ngrx/effects": "17.2.0", "@ngrx/entity": "17.2.0", "@ngrx/operators": "17.2.0", "@ngrx/router-store": "17.2.0", "@ngrx/store": "17.2.0", "@ngrx/store-devtools": "17.2.0", "@ngx-formly/core": "6.3.3", "@nx/angular": "19.0.8", "@popperjs/core": "2.11.8", "@sentry/angular": "8.48.0", "@sentry/webpack-plugin": "2.22.2", "@siemens/ngx-datatable": "22.4.1", "@stomp/rx-stomp": "2.0.0", "@swc/helpers": "0.5.2", "@swimlane/ngx-charts": "20.5.0", "@types/hot-formula-parser": "4.0.4", "@types/ineum": "216.0.3", "@types/lodash-es": "4.17.12", "@types/pdfjs-dist": "2.10.378", "@zip.js/zip.js": "2.7.45", "D": "1.0.0", "angular-cropperjs": "14.0.1", "angular-mentions": "1.5.0", "azure-maps-control": "3.3.0", "azure-maps-drawing-tools": "1.0.3", "azure-maps-rest": "2.1.1", "backoff-rxjs": "7.0.0", "bootstrap": "5.3.3", "codemirror": "5.65.12", "compare-versions": "6.1.0", "dagre": "0.8.5", "dayjs": "1.11.11", "echarts": "5.5.0", "flag-icons": "7.2.3", "glob": "10.4.1", "hot-formula-parser": "4.0.0", "jointjs-plus": "file:joint-plus.tgz", "jwt-decode": "4.0.0", "lefthook": "1.6.7", "lodash-es": "4.17.21", "material-icons": "1.13.2", "material-symbols": "0.20.0", "ng-circle-progress": "1.7.1", "ngrx-store-localstorage": "17.0.0", "ngx-currency": "17.0.0", "ngx-device-detector": "7.0.0", "ngx-drag-scroll": "17.0.1", "ngx-echarts": "17.2.0", "ngx-extended-pdf-viewer": "20.2.0", "ngx-infinite-scroll": "17.0.1", "ngx-permissions": "17.1.0", "ngx-scrollbar": "16.1.0", "ngx-spinner": "17.0.0", "ngx-ui-tour-ngx-bootstrap": "12.1.0", "ngx-webstorage": "13.0.1", "node-fetch": "3.3.2", "object-hash": "3.0.0", "patch-package": "8.0.0", "pdfjs-dist": "4.3.136", "postcss-scss": "4.0.9", "posthog-js": "1.150.1", "rxjs": "7.8.1", "screenfull": "6.0.2", "socket.io-client": "4.8.1", "ts-morph": "25.0.1", "ts-node": "10.9.1", "tslib": "2.5.0", "tus-js-client": "4.2.3", "type-check": "0.4.0", "uuid": "9.0.1", "zone.js": "0.14.6"}, "devDependencies": {"@angular-devkit/build-angular": "17.3.11", "@angular-devkit/core": "17.3.8", "@angular-devkit/schematics": "17.3.8", "@angular-eslint/eslint-plugin": "17.5.2", "@angular-eslint/eslint-plugin-template": "17.5.2", "@angular-eslint/schematics": "17.5.2", "@angular-eslint/template-parser": "17.5.2", "@angular/cli": "17.3.8", "@angular/compiler-cli": "17.3.11", "@angular/language-service": "17.3.11", "@fincloud/eslint-plugin-ns": "1.7.1", "@fincloud/lib-git-hooks": "4.0.5", "@ngrx/eslint-plugin": "17.2.0", "@ngx-formly/schematics": "5.12.7", "@nx/cypress": "20.6.2", "@nx/eslint": "19.0.8", "@nx/eslint-plugin": "19.0.8", "@nx/jest": "19.0.8", "@nx/js": "19.0.8", "@nx/web": "19.0.8", "@nx/webpack": "19.0.8", "@nx/workspace": "19.0.8", "@schematics/angular": "17.3.8", "@swc-node/register": "1.8.0", "@swc/cli": "0.3.12", "@swc/core": "1.4.13", "@types/d3": "7.4.3", "@types/dagre": "0.7.52", "@types/jest": "29.5.0", "@types/node": "20.14.2", "@types/sockjs-client": "1.5.4", "@types/uuid": "9.0.8", "@typescript-eslint/eslint-plugin": "7.12.0", "@typescript-eslint/parser": "7.12.0", "angular-cropperjs": "14.0.1", "autoprefixer": "10.4.19", "chalk": "5.3.0", "change-case": "5.4.4", "cross-env": "7.0.3", "cypress": "13.6.6", "dotenv": "16.4.5", "eslint": "8.57.0", "eslint-config-prettier": "9.1.0", "eslint-plugin-check-file": "2.8.0", "eslint-plugin-cypress": "2.15.2", "eslint-plugin-import": "2.29.0", "eslint-plugin-prettier": "5.2.1", "jasmine-core": "4.0.1", "jest": "29.5.0", "jest-environment-jsdom": "29.5.0", "jest-environment-node": "^29.4.1", "jest-preset-angular": "14.0.4", "jsonc-eslint-parser": "2.1.0", "ng-openapi-gen": "0.51.0", "ng-packagr": "17.3.0", "ng-swagger-gen": "2.3.1", "nx": "19.0.8", "per-env": "1.0.2", "postcss": "8.4.49", "postcss-import": "14.1.0", "postcss-preset-env": "7.5.0", "postcss-url": "10.1.3", "prettier": "3.3.3", "prettier-plugin-organize-imports": "4.0.0", "shx": "0.3.4", "stylelint": "16.10.0", "tailwindcss": "3.4.13", "tailwindcss-themer": "4.0.0", "ts-jest": "29.1.4", "ts-node": "10.9.1", "typescript": "5.4.5"}, "overrides": {"@jridgewell/gen-mapping": "0.3.5"}}