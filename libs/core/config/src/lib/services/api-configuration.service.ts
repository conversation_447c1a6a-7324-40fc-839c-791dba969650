import { Injectable } from '@angular/core';
import { ApiConfiguration as ApplicationApiConfiguration } from '@fincloud/swagger-generator/application';
import { ApiConfiguration as AuthorizationServerApiConfiguration } from '@fincloud/swagger-generator/authorization-server';
import { ApiConfiguration as BillingApiConfiguration } from '@fincloud/swagger-generator/billing';
import { ApiConfiguration as BusinessCaseManagerApiConfiguration } from '@fincloud/swagger-generator/business-case-manager';
import { ApiConfiguration as CommunicationApiConfiguration } from '@fincloud/swagger-generator/communication';
import { ApiConfiguration as CompanyApiConfiguration } from '@fincloud/swagger-generator/company';
import { ApiConfiguration as ContractManagementApiConfiguration } from '@fincloud/swagger-generator/contract-management';
import { ApiConfiguration as DashboardApiConfiguration } from '@fincloud/swagger-generator/dashboard';
import { ApiConfiguration as DemoSnapshotApiConfiguration } from '@fincloud/swagger-generator/demo';
import { ApiConfiguration as DocumentApiConfiguration } from '@fincloud/swagger-generator/document';
import { ApiConfiguration as DocumentForwardingApiConfiguration } from '@fincloud/swagger-generator/document-forwarding';
import { ApiConfiguration as DocumentGeneratorApiConfiguration } from '@fincloud/swagger-generator/document-generator';
import { ApiConfiguration as DocumentPreviewApiConfiguration } from '@fincloud/swagger-generator/document-preview';
import { ApiConfiguration as DocumentSigningConfiguration } from '@fincloud/swagger-generator/document-signing';
import { ApiConfiguration as ExchangeApiConfiguration } from '@fincloud/swagger-generator/exchange';
import { ApiConfiguration as ExternalIntegrationsConfig } from '@fincloud/swagger-generator/external-integrations';
import { ApiConfiguration as FeedbackApiConfiguration } from '@fincloud/swagger-generator/feedback';
import { ApiConfiguration as FinancingDetailsApiConfiguration } from '@fincloud/swagger-generator/financing-details';
import { ApiConfiguration as HandelsRegisterApiConfiguration } from '@fincloud/swagger-generator/handelsregister';
import { ApiConfiguration as InternalToolsConfiguration } from '@fincloud/swagger-generator/internal-tools';
import { ApiConfiguration as NeoGptApiConfiguration } from '@fincloud/swagger-generator/neo-gpt';
import { ApiConfiguration as NotificationApiConfiguration } from '@fincloud/swagger-generator/notification';
import { ApiConfiguration as PlatformNotificationSystemConfiguration } from '@fincloud/swagger-generator/platform-notification';
import { ApiConfiguration as CompanyPortalConfiguration } from '@fincloud/swagger-generator/portal';
import { TusDocumentUploadConfiguration } from './tus-document-upload-configuration.service';

@Injectable({
  providedIn: 'root',
})
export class ApiConfigurationService {
  constructor(
    private authorizationServerApiConfiguration: AuthorizationServerApiConfiguration,
    private exchangeApiConfig: ExchangeApiConfiguration,
    private feedbackApiConfig: FeedbackApiConfiguration,
    private businessCaseManagerApiConfig: BusinessCaseManagerApiConfiguration,
    private contractManagementApiConfig: ContractManagementApiConfiguration,
    private applicationApiConfig: ApplicationApiConfiguration,
    private communicationApiConfig: CommunicationApiConfiguration,
    private companyApiConfig: CompanyApiConfiguration,
    private documentApiConfig: DocumentApiConfiguration,
    private externalIntegrationsConfig: ExternalIntegrationsConfig,
    private notificationApiConfig: NotificationApiConfiguration,
    private documentPreviewApiConfig: DocumentPreviewApiConfiguration,
    private documentGeneratorApiConfig: DocumentGeneratorApiConfiguration,
    private billingApiConfig: BillingApiConfiguration,
    private handelsRegisterApiConfiguration: HandelsRegisterApiConfiguration,
    private documentSigningConfig: DocumentSigningConfiguration,
    private companyPortalConfig: CompanyPortalConfiguration,
    private documentForwardingConfig: DocumentForwardingApiConfiguration,
    private neoGptApiConfig: NeoGptApiConfiguration,
    private dashboardApiConfig: DashboardApiConfiguration,
    private platformNotificationSystemConfig: PlatformNotificationSystemConfiguration,
    private demoSnapshotApiConfig: DemoSnapshotApiConfiguration,
    private tusDocumentUploadConfig: TusDocumentUploadConfiguration,
    private financingDetailsApiConfiguration: FinancingDetailsApiConfiguration,
    private internalToolsConfiguration: InternalToolsConfiguration,
  ) {}

  setRootUrls() {
    this.authorizationServerApiConfiguration.rootUrl = this.getServiceRootUrl(
      'authorization-server',
    );
    this.exchangeApiConfig.rootUrl = this.getServiceRootUrl(
      'authorization-proxy',
    );
    this.feedbackApiConfig.rootUrl = this.getServiceRootUrl(
      'authorization-proxy',
    );
    this.documentForwardingConfig.rootUrl = this.getServiceRootUrl(
      'authorization-proxy',
    );
    this.handelsRegisterApiConfiguration.rootUrl = this.getServiceRootUrl(
      'authorization-proxy',
    );
    this.documentPreviewApiConfig.rootUrl = this.getServiceRootUrl(
      'authorization-proxy',
    );
    this.documentGeneratorApiConfig.rootUrl = this.getServiceRootUrl(
      'authorization-proxy',
    );

    this.businessCaseManagerApiConfig.rootUrl = this.getServiceRootUrl(
      'business-case-manager',
    );
    this.contractManagementApiConfig.rootUrl = this.getServiceRootUrl(
      'contract-management',
    );
    this.financingDetailsApiConfiguration.rootUrl =
      this.getServiceRootUrl('financing-details');
    this.applicationApiConfig.rootUrl = this.getServiceRootUrl('application');
    this.communicationApiConfig.rootUrl =
      this.getServiceRootUrl('communication');
    this.companyApiConfig.rootUrl = this.getServiceRootUrl('company');
    this.externalIntegrationsConfig.rootUrl = this.getServiceRootUrl(
      'external-integrations',
    );
    this.documentApiConfig.rootUrl = this.getServiceRootUrl('document');
    this.notificationApiConfig.rootUrl = this.getServiceRootUrl('notification');
    this.billingApiConfig.rootUrl = this.getServiceRootUrl('billing');
    this.documentSigningConfig.rootUrl =
      this.getServiceRootUrl('document-signing');
    this.companyPortalConfig.rootUrl = this.getServiceRootUrl('portal');
    this.neoGptApiConfig.rootUrl = this.getServiceRootUrl('neo-gpt');
    this.platformNotificationSystemConfig.rootUrl = this.getServiceRootUrl(
      'platform-notification',
    );
    this.dashboardApiConfig.rootUrl = this.getServiceRootUrl('dashboard');
    this.tusDocumentUploadConfig.rootUrl = this.getServiceRootUrl(
      'document/tus-upload',
    );
    this.demoSnapshotApiConfig.rootUrl = this.getServiceRootUrl('demo');
    this.internalToolsConfiguration.rootUrl =
      this.getServiceRootUrl('internal-tools');
  }

  public getServiceRootUrl(srvName: string): string {
    return `/api/${srvName}`;
  }
}
