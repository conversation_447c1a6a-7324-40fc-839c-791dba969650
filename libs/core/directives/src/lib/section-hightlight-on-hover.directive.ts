import { DOCUMENT } from '@angular/common';
import {
  Directive,
  ElementRef,
  HostListener,
  Inject,
  Input,
  Renderer2,
} from '@angular/core';

@Directive({
  selector: '[appSectionHightlightOnHover]',
})
export class SectionHightlightOnHoverDirective {
  @Input() key: string;
  @Input() set showHightlightOnHover(value: boolean) {
    this.hightlightOnHover = value;

    if (!this.hightlightOnHover) {
      // failsafe
      this.removeHighlightingComponents();
    }
  }
  @Input() set draggingActive(value: boolean) {
    this.isDraggingActive = value;

    // if the dragging has stopped, via drop, remove the directives bonus dom elements
    if (!this.isDraggingActive) {
      this.removeHighlightingComponents();
    }
  }
  @Input() extraPadding = false;

  directChild: HTMLElement;
  hightlightOnHover: boolean;
  isDraggingActive: boolean;

  private readonly extraPaddingClassList = [
    'tw-absolute',
    `tw-top-[-1.2rem]`,
    `tw-left-[-1.2rem]`,
    `tw-w-[calc(100%+2.4rem)]`,
    `tw-h-[calc(100%+2.4rem)]`,
    'tw-rounded-lg',
  ];

  private readonly defaultPaddingClassList = [
    'tw-absolute',
    `tw-top-0`,
    `tw-left-0`,
    `tw-w-full`,
    `tw-h-full`,
    'tw-rounded-lg',
  ];

  @HostListener('mouseenter', ['$event']) addHightlightingComponents() {
    if (!this.isDraggingActive) {
      return;
    }

    // responsible mainly for positioning and the border (since it's child has to be opacity 0.1 and the border is brightened otherwise)
    this.directChild = this.document.createElement('div');

    this.directChild.classList.add(
      ...(this.extraPadding
        ? this.extraPaddingClassList
        : this.defaultPaddingClassList),
    );

    if (this.hightlightOnHover) {
      this.directChild.classList.add(
        'tw-border-2',
        'tw-color-status-informative',
        'tw-border-color-border-default-interactive',
        'tw-overflow-hidden',
      );

      const backgroundChild = this.document.createElement('div');

      backgroundChild.classList.add(
        ...[
          'tw-bg-color-background-secondary-strong',
          'tw-h-full',
          'tw-opacity-10',
        ],
      );
      backgroundChild.id = this.key;

      this.renderer.appendChild(this.directChild, backgroundChild);
    } else {
      // If the section does not allow drop - leave the background transparent and show the cursow not allow
      // (just cursor wasn't enought because the fields were staying ruining the cursor back to normal, if moved over them in that group)
      this.directChild.classList.add('tw-cursor-not-allowed');
    }

    this.renderer.appendChild(this.elementRef.nativeElement, this.directChild);
  }

  @HostListener('mouseleave', ['$event']) removeHighlightingComponents() {
    if (this.directChild && this.elementRef) {
      this.renderer.removeChild(this.elementRef, this.directChild);
      this.directChild = null;
    }
  }

  constructor(
    private elementRef: ElementRef,
    private renderer: Renderer2,
    @Inject(DOCUMENT) private document: Document,
  ) {
    this.elementRef.nativeElement.classList.add('tw-relative');
  }
}
