import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { AnchorPathBuilderDirective } from './anchor-path-builder.directive';
import { CtrlClickDirective } from './ctrl-click.directive';
import { CurrencyMaskHelperDirective } from './currency-mask-helper.directive';
import { DragAndDropContainerDirective } from './drag-and-drop-container.directive';
import { ExpandDirective } from './expand.directive';
import { OverlayHighlightContainerDirective } from './overlay-highlight-container.directive';
import { SectionHightlightOnHoverDirective } from './section-hightlight-on-hover.directive';

@NgModule({
  declarations: [
    CtrlClickDirective,
    AnchorPathBuilderDirective,
    ExpandDirective,
    CurrencyMaskHelperDirective,
    SectionHightlightOnHoverDirective,
    DragAndDropContainerDirective,
    OverlayHighlightContainerDirective,
  ],
  imports: [CommonModule],
  exports: [
    CtrlClickDirective,
    AnchorPathBuilderDirective,
    ExpandDirective,
    CurrencyMaskHelperDirective,
    SectionHightlightOnHoverDirective,
    DragAndDropContainerDirective,
    OverlayHighlightContainerDirective,
  ],
})
export class NsCoreDirectivesModule {}
