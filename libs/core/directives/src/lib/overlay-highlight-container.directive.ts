import { Directive, HostBinding, Input } from '@angular/core';

@Directive({
  selector: '[appOverlayHighlightContainer]',
})
export class OverlayHighlightContainerDirective {
  @Input() shouldShowHighlight = false;
  @Input() canDrop = true;
  @Input() extraPadding = false;
  @Input() hasDashedBorder = false;

  @HostBinding('class') get hostClasses(): { [key: string]: boolean } {
    return {
      'tw-relative': true,
      'tw-border-solid': this.shouldShowHighlight,
      'tw-border-2': this.shouldShowHighlight,
      '!tw-border-color-border-default-interactive': this.shouldShowHighlight,
      'tw-overflow-hidden': this.shouldShowHighlight,
      'tw-border-dashed': this.hasDashedBorder,
      'tw-bg-color-transparency-secondary-minimal': this.shouldShowHighlight,
      'tw-rounded-lg': this.shouldShowHighlight,
      'tw-p-[1.2rem]': this.shouldShowHighlight && this.extraPadding,
      'tw-p-0': this.shouldShowHighlight && !this.extraPadding,
      'tw-cursor-not-allowed': !this.canDrop,
    };
  }
}
