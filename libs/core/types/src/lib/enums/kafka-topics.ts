export enum KafkaTopic {
  TOPIC_SRV_CONSORTIUM_MANAGER_INFORMATION_CREATED = 'srv-consortium-manager.information.created',
  TOPIC_SRV_CONSORTIUM_MANAGER_INFORMATION_UPDATED = 'srv-consortium-manager.information.updated',
  TOPIC_SRV_CONSORTIUM_MANAGER_INFORMATION_DELETED = 'srv-consortium-manager.information.deleted',
  TOPIC_SRV_CONSORTIUM_MANAGER_INFORMATION_ADDED = 'srv-consortium-manager.information.added',
  TOPIC_SRV_CONSORTIUM_MANAGER_INFORMATION_REMOVED = 'srv-consortium-manager.information.removed',
  TOPIC_SRV_CONSORTIUM_MANAGER_INFORMATION_CHANGED = 'srv-consortium-manager.information.changed',
  TOPIC_SRV_CONSORTIUM_MANAGER_INFORMATION_TAG_UPDATED = 'srv-consortium-manager.information.tag.updated',
  TOPIC_SRV_CONSORTIUM_MANAGER_CONSORTIUM_CREATED = 'srv-consortium-manager.consortium.created',
  TOPIC_SRV_CONSORTIUM_MANAGER_CONSORTIUM_UPDATED = 'srv-consortium-manager.consortium.updated',
  TOPIC_SRV_CONSORTIUM_MANAGER_CONSORTIUM_LEAD_CHANGE = 'srv-consortium-manager.consortium.lead-change',
  TOPIC_SRV_CONSORTIUM_MANAGER_CONSORTIUM_STATE_TRANSFER_CHANGED = 'srv-consortium-manager.consortium-state-transfer.changed',
  TOPIC_SRV_CONSORTIUM_MANAGER_PARTICIPANT_BANK_ADDED = 'srv-consortium-manager.participant-bank.added',
  TOPIC_SRV_CONSORTIUM_MANAGER_PARTICIPANT_BANK_REMOVED = 'srv-consortium-manager.participant-bank.removed',
  TOPIC_SRV_CONSORTIUM_MANAGER_PARTICIPANT_BANK_CHANGED = 'srv-consortium-manager.participant-bank.changed',
  TOPIC_SRV_CONSORTIUM_MANAGER_PARTICIPANT_USER_ADDED = 'srv-consortium-manager.participant-user.added',
  TOPIC_SRV_CONSORTIUM_MANAGER_PARTICIPANT_USER_REMOVED = 'srv-consortium-manager.participant-user.removed',
  TOPIC_SRV_CONSORTIUM_MANAGER_PARTICIPANT_USER_UPDATED = 'srv-consortium-manager.participant-user.updated',
  TOPIC_SRV_CONSORTIUM_MANAGER_TEMPLATE_CREATED = 'srv-consortium-manager.template.created',
  TOPIC_SRV_CONSORTIUM_MANAGER_TEMPLATE_UPDATE_TEMPLATE_VERSION = 'srv-consortium-manager.template.update-template-version',
  TOPIC_SRV_CONSORTIUM_MANAGER_TEMPLATE_UPDATED = 'srv-consortium-manager.template.updated',
  TOPIC_SRV_CONSORTIUM_MANAGER_FAQ_CREATED = 'srv-consortium-manager.faq.created',
  TOPIC_SRV_CONSORTIUM_MANAGER_FINANCIAL_STRUCTURE_CREATED = 'srv-consortium-manager.financial-structure.created',
  TOPIC_SRV_CONSORTIUM_MANAGER_FINANCIAL_STRUCTURE_UPDATED = 'srv-consortium-manager.financial-structure.updated',
  TOPIC_SRV_CONSORTIUM_MANAGER_FINANCIAL_STRUCTURE_ENABLED = 'srv-consortium-manager.financial-structure.enabled',
  TOPIC_SRV_CONSORTIUM_MANAGER_FINANCIAL_STRUCTURE_DISABLED = 'srv-consortium-manager.financial-structure.disabled',
  TOPIC_SRV_BUSINESS_CASE_MANAGER_INFORMATION_CREATED = 'srv-business-case-manager.information.created',
  TOPIC_SRV_BUSINESS_CASE_MANAGER_INFORMATION_UPDATED = 'srv-business-case-manager.information.updated',
  TOPIC_SRV_BUSINESS_CASE_MANAGER_INFORMATION_DELETED = 'srv-business-case-manager.information.deleted',
  TOPIC_SRV_BUSINESS_CASE_MANAGER_INFORMATION_ADDED = 'srv-business-case-manager.information.added',
  TOPIC_SRV_BUSINESS_CASE_MANAGER_INFORMATION_REMOVED = 'srv-business-case-manager.information.removed',
  TOPIC_SRV_BUSINESS_CASE_MANAGER_INFORMATION_CHANGED = 'srv-business-case-manager.information.changed',
  TOPIC_SRV_BUSINESS_CASE_MANAGER_INFORMATION_TAG_UPDATED = 'srv-business-case-manager.information.tag.updated',
  TOPIC_SRV_BUSINESS_CASE_MANAGER_BUSINESS_CASE_CREATED = 'srv-business-case-manager.business-case.created',
  TOPIC_SRV_BUSINESS_CASE_MANAGER_BUSINESS_CASE_UPDATED = 'srv-business-case-manager.business-case.updated',
  TOPIC_SRV_BUSINESS_CASE_MANAGER_BUSINESS_CASE_LEAD_CHANGE = 'srv-business-case-manager.business-case.lead-change',
  TOPIC_SRV_BUSINESS_CASE_MANAGER_BUSINESS_CASE_STATE_TRANSFER_CHANGED = 'srv-business-case-manager.business-case-state-transfer.changed',
  TOPIC_SRV_BUSINESS_CASE_MANAGER_PARTICIPANT_CUSTOMER_ADDED = 'srv-business-case-manager.participant-customer.added',
  TOPIC_SRV_BUSINESS_CASE_MANAGER_PARTICIPANT_CUSTOMER_REMOVED = 'srv-business-case-manager.participant-customer.removed',
  TOPIC_SRV_BUSINESS_CASE_MANAGER_PARTICIPANT_CUSTOMER_CHANGED = 'srv-business-case-manager.participant-customer.changed',
  TOPIC_SRV_BUSINESS_CASE_MANAGER_PARTICIPANT_USER_ADDED = 'srv-business-case-manager.participant-user.added',
  TOPIC_SRV_BUSINESS_CASE_MANAGER_PARTICIPANT_USER_REMOVED = 'srv-business-case-manager.participant-user.removed',
  TOPIC_SRV_BUSINESS_CASE_MANAGER_PARTICIPANT_USER_UPDATED = 'srv-business-case-manager.participant-user.updated',
  TOPIC_SRV_BUSINESS_CASE_MANAGER_PARTICIPANT_VISIBILITY_ON = 'srv-business-case-manager.participant-visibility.visibility-ON',
  TOPIC_SRV_BUSINESS_CASE_MANAGER_PARTICIPANT_VISIBILITY_OFF = 'srv-business-case-manager.participant-visibility.visibility-OFF',
  TOPIC_SRV_BUSINESS_CASE_MANAGER_TEMPLATE_CREATED = 'srv-business-case-manager.template.created',
  TOPIC_SRV_BUSINESS_CASE_MANAGER_TEMPLATE_UPDATE_TEMPLATE_VERSION = 'srv-business-case-manager.template.update-template-version',
  TOPIC_SRV_BUSINESS_CASE_MANAGER_TEMPLATE_UPDATED = 'srv-business-case-manager.template.updated',
  TOPIC_SRV_BUSINESS_CASE_MANAGER_FAQ_CREATED = 'srv-business-case-manager.faq.created',
  TOPIC_SRV_BUSINESS_CASE_MANAGER_FINANCIAL_STRUCTURE_CREATED = 'srv-business-case-manager.financial-structure.created',
  TOPIC_SRV_BUSINESS_CASE_MANAGER_FINANCIAL_STRUCTURE_UPDATED = 'srv-business-case-manager.financial-structure.updated',
  TOPIC_SRV_BUSINESS_CASE_MANAGER_FINANCIAL_STRUCTURE_ENABLED = 'srv-business-case-manager.financial-structure.enabled',
  TOPIC_SRV_BUSINESS_CASE_MANAGER_FINANCIAL_STRUCTURE_DISABLED = 'srv-business-case-manager.financial-structure.disabled',
  TOPIC_SRV_BUSINESS_CASE_MANAGER_DUPLICATE_BUSINESS_CASE_CREATED = 'srv-business-case-manager.duplicate-business-case.created',
  TOPIC_SRV_BUSINESS_CASE_MANAGER_CASE_PERMISSIONS_UPDATED = 'srv-business-case-manager.case-permissions.updated',
  TOPIC_SRV_BUSINESS_CASE_MANAGER_DEMO_BUSINESS_CASE_CREATED = 'srv-business-case-manager.demo-business-case.created',
  TOPIC_SRV_DOCUMENT_DOCUMENT_NEW = 'srv-document.document.new',
  TOPIC_SRV_NOTIFICATION_EMAIL = 'srv-notification.email',
  TOPIC_SRV_NOTIFICATION_INTERNAL = 'srv-notification.internal',
  TOPIC_SRV_NOTIFICATION_SILENT = 'srv-notification.silent',
  TOPIC_SRV_NOTIFICATION_CANCELED_EMAIL = 'srv-notification.canceled-email',
  TOPIC_SRV_NOTIFICATION_CHAT_ARCHIVED = 'srv-notification.chat-archived',
  TOPIC_SRV_COMPANY_COMPANY_CREATED = 'srv-company.company.created',
  TOPIC_SRV_COMPANY_COMPANY_UPDATED = 'srv-company.company.updated',
  TOPIC_SRV_COMPANY_COMPANY_DELETED = 'srv-company.company.deleted',
  TOPIC_SRV_APPLICATION_INVITATION_CREATED = 'srv-application.invitation.created',
  TOPIC_SRV_APPLICATION_INVITATION_DECLINED = 'srv-application.invitation.declined',
  TOPIC_SRV_APPLICATION_INVITATION_ACCEPTED = 'srv-application.invitation.accepted',
  TOPIC_SRV_APPLICATION_INVITATION_CANCELED = 'srv-application.invitation.canceled',
  TOPIC_SRV_APPLICATION_APPLICATION_CREATED = 'srv-application.application.created',
  TOPIC_SRV_APPLICATION_APPLICATION_DECLINED = 'srv-application.application.declined',
  TOPIC_SRV_APPLICATION_APPLICATION_ACCEPTED = 'srv-application.application.accepted',
  TOPIC_SRV_APPLICATION_APPLICATION_CANCELED = 'srv-application.application.canceled',
  TOPIC_SRV_APPLICATION_APPLICATION_SUBMITTED = 'srv-application.application.submitted',
  TOPIC_SRV_APPLICATION_INVITATION_VALID_NDA_SEND_EMAIL = 'srv-application.invitation.valid-nda-send-email',
  TOPIC_SRV_BANK_BANK_CREATED = 'srv-bank.bank.created',
  TOPIC_SRV_BANK_BANK_UPDATED = 'srv-bank.bank.updated',
  TOPIC_SRV_BANK_BANK_CREATION_FAILED = 'srv-bank.bank.creation-failed',
  TOPIC_SRV_BANK_CONTACT_PERSON_MISSING = 'srv-bank.contact-person.missing',
  TOPIC_SRV_CUSTOMER_CUSTOMER_CREATED = 'srv-customer.customer.created',
  TOPIC_SRV_CUSTOMER_CUSTOMER_UPDATED = 'srv-customer.customer.updated',
  TOPIC_SRV_CUSTOMER_CUSTOMER_CREATION_FAILED = 'srv-customer.customer.creation-failed',
  TOPIC_SRV_CUSTOMER_CONTACT_PERSON_MISSING = 'srv-customer.contact-person.missing',
  TOPIC_SRV_EXCHANGE_EXCHANGE_CONSORTIUM_UPDATED = 'srv-exchange.exchange-consortium.updated',
  TOPIC_SRV_USER_USER_CREATED = 'srv-user.user.created',
  TOPIC_SRV_USER_USER_UPDATED = 'srv-user.user.updated',
  TOPIC_SRV_USER_USER_ENABLED = 'srv-user.user.enabled',
  TOPIC_SRV_USER_USER_DISABLED = 'srv-user.user.disabled',
  TOPIC_SRV_DOCUMENT_SIGNING_CONTRACT_CREATED = 'srv-document-signing.contract.created',
  TOPIC_SRV_DOCUMENT_SIGNING_CONTRACT_EXPIRING = 'srv-document-signing.contract.expiring',
  TOPIC_SRV_DOCUMENT_SIGNING_CONTRACT_CODE_GENERATED = 'srv-document-signing.contract.code-generated',
  TOPIC_SRV_DOCUMENT_SIGNING_CONTRACT_SIGNED = 'srv-document-signing.contract.signed',
  TOPIC_SRV_DOCUMENT_SIGNING_CONTRACT_UPDATED = 'srv-document-signing.contract.updated',
  TOPIC_SRV_CONTRACT_MANAGEMENT_CONTRACT_CREATED = 'srv-contract-management.contract.created',
  TOPIC_SRV_CONTRACT_MANAGEMENT_CONTRACT_UPDATED = 'srv-contract-management.contract.updated',
  TOPIC_SRV_CONTRACT_MANAGEMENT_CONTRACT_EXPIRING = 'srv-contract-management.contract.expiring',
  TOPIC_SRV_CONTRACT_MANAGEMENT_CONTRACT_EXPIRED = 'srv-contract-management.contract.expired',
  TOPIC_SRV_CONTRACT_MANAGEMENT_CONTRACT_VOIDED = 'srv-contract-management.contract.voided',
  TOPIC_SRV_CONTRACT_MANAGEMENT_CONTRACT_SIGNED = 'srv-contract-management.contract.signed',
  TOPIC_SRV_CONTRACT_MANAGEMENT_CONTRACT_PARTIALLY_SIGNED = 'srv-contract-management.contract.partially-signed',
  TOPIC_SRV_COMMUNICATION_SEND_MESSAGE_TAG_SUBMITTED = 'srv-communication.send-message-tag.submitted',
  TOPIC_SRV_PORTAL_PORTAL_ACCESS_CREATED = 'srv-portal.portal-access.created',
  TOPIC_SRV_PORTAL_PORTAL_ACCESS_REMOVED = 'srv-portal.portal-access.removed',
  TOPIC_SRV_PORTAL_INPUT_REQUEST_CREATED = 'srv-portal.input-request.created',
  TOPIC_SRV_PORTAL_INPUT_REQUEST_REMOVED = 'srv-portal.input-request.removed',
  TOPIC_AUTHORIZATION_SERVER_USER_DELETED = 'authorization-server.user.deleted',
  TOPIC_AUTHORIZATION_SERVER_USER_DISABLED = 'authorization-server.user.disabled',
  TOPIC_AUTHORIZATION_SERVER_USER_UPDATED = 'authorization-server.user.updated',
}
