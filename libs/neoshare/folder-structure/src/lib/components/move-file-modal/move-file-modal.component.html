<fin-modal-header>
  <div i18n="@@folderStructure.folder.actions.move">Verschieben</div>

  <button fin-button-action [size]="finSize.L" fin-modal-close>
    <fin-icon name="close"></fin-icon>
  </button>
</fin-modal-header>
<fin-modal-slots-container>
  <fin-modal-slot
    [size]="finSize.S"
    class="tw-h-[52vh] tw-border-r tw-border-color-border-default-primary tw-border-solid"
  >
    <fin-scrollbar class="tw-h-full">
      <div class="tw-p-[2.4rem] tw-text-body-1-strong">
        <div
          class="tw-pb-[2.4rem] tw-px-[1.6rem] tw-text-color-text-primary"
          i18n="
            @@businessCase.participantAccess.rights.editAccess.text.groupVisibility"
        >
          Gruppen
        </div>
        <div class="tw-flex tw-flex-col tw-h-full tw-w-full tw-gap-[0.4rem]">
          @for (group of groups; track group.key; let groupIndex = $index) {
            <a
              class="tw-text-body-2-moderate"
              fin-menu-item
              [active]="targetFolderGroup.key === group.key"
              (click)="selectGroup(group)"
            >
              <ng-container finMenuItemTitle
                >{{ groupIndex + 1 }}. {{ group.value }}</ng-container
              >
            </a>
          }
        </div>
      </div>
    </fin-scrollbar>
  </fin-modal-slot>
  <fin-modal-slot [size]="finSize.L" class="tw-h-[52vh]">
    <ng-template [ngTemplateOutlet]="rightSlotData"></ng-template>
  </fin-modal-slot>
</fin-modal-slots-container>

<fin-modal-footer separator>
  <button
    fin-button
    fin-modal-close
    [appearance]="finButtonAppearance.SECONDARY"
    [size]="finSize.L"
    i18n="@@button.label.cancel"
  >
    Abbrechen
  </button>

  <button
    fin-button
    [appearance]="finButtonAppearance.PRIMARY"
    [size]="finSize.L"
    [showLoader]="getMoveFolderOrDocumentLoading$ | async"
    (click)="onSubmit()"
    i18n="@@button.label.moveHere"
  >
    Hierher verschieben
  </button>
</fin-modal-footer>

<ng-template #rightSlotData>
  <fin-scrollbar #folderSectionScrollbar>
    <div
      class="tw-flex tw-flex-col tw-p-[2.4rem]"
      [ngClass]="{
        'tw-h-full': !isCurrentFolderEmpty,
        'tw-h-[52vh]': isCurrentFolderEmpty,
      }"
    >
      <div
        class="tw-bg-color-surface-primary tw-pb-[1.6rem] tw-h-fit tw-flex tw-flex-col"
      >
        @if (breadcrumbs.length === 1) {
          <div
            class="tw-text-body-2-strong tw-text-color-text-primary"
            i18n="@@folderStructure.sectionTitle"
          >
            Dokumente
          </div>
        } @else {
          <fin-breadcrumb
            [items]="breadcrumbs"
            (breadcrumbClicked)="onBreadcrumbClick($event)"
          ></fin-breadcrumb>
        }
      </div>
      @if (isCurrentFolderEmpty) {
        <div class="tw-flex tw-justify-center tw-items-center tw-flex-1">
          <div class="tw-flex tw-flex-col tw-items-center tw-gap-[1.6rem]">
            <img src="assets/svg/svgEmptyFolder.svg" />
            <div
              class="tw-text-heading-3-strong tw-text-color-text-primary"
              i18n="@@folderStructure.emptyFolder"
            >
              Dieser Ordner ist leer
            </div>
          </div>
        </div>
      } @else {
        <div class="tw-flex tw-flex-col tw-gap-[2.4rem]">
          @if (currentFolder.children?.length) {
            <div
              class="tw-grid tw-grid-cols-[repeat(auto-fill,_minmax(28rem,_1fr))] tw-gap-[2.4rem]"
            >
              @for (
                subFolder of currentFolder.children;
                track subFolder.id;
                let index = $index
              ) {
                <fin-directory (click)="navigateToSubfolder(subFolder, index)">
                  <ng-container finSuffix>
                    <div
                      finTruncateText
                      [openDelay]="0"
                      class="tw-text-body-2-strong"
                    >
                      {{ subFolder.name }}
                    </div>
                    <!-- TODO (ALPHA-9940): Uncomment for Folder Structure V1.1
                     <div class="tw-flex tw-justify-between tw-items-center">
                        <div class="tw-text-body-3-moderate">
                          <ui-element-count
                            [count]="
                              (subFolder.children?.length ?? 0) +
                              (subFolder.fields?.length ?? 0)
                            "
                          >
                          </ui-element-count>
                        </div>
                      </div>
                      -->
                  </ng-container>
                </fin-directory>
              }
            </div>
          }
          <div
            class="tw-grid tw-grid-cols-[repeat(auto-fill,_minmax(28rem,_1fr))] tw-gap-[2.4rem]"
          >
            @for (
              document of currentFolderDocuments$ | async;
              track document.field.key
            ) {
              <ui-document-field
                [disabled]="true"
                [documentId]="document.information.value"
                [field]="document.field"
                [information]="document.information"
                [img$]="document.img$"
              >
              </ui-document-field>
            }
          </div>
        </div>
      }
    </div>
  </fin-scrollbar>
</ng-template>
