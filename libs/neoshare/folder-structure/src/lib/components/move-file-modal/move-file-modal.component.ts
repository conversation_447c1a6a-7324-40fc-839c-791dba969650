import {
  ChangeDetectionStrategy,
  Component,
  EventEmitter,
  Inject,
  OnInit,
  Output,
  ViewChild,
} from '@angular/core';
import { InformationUtils } from '@fincloud/core/business-case';
import { DocumentFieldManageHelperService } from '@fincloud/neoshare/document';
import { folderStructureFeature } from '@fincloud/state/folder-structure';
import {
  Folder,
  Group,
} from '@fincloud/swagger-generator/business-case-manager';
import { InformationRecord } from '@fincloud/swagger-generator/exchange';
import { FinBreadcrumbItem } from '@fincloud/ui/breadcrumb';
import { FinButtonAppearance } from '@fincloud/ui/button';
import { FIN_MODAL_DATA } from '@fincloud/ui/modal';
import { FinScrollbarComponent } from '@fincloud/ui/scrollbar';
import { FinSize } from '@fincloud/ui/types';
import { Store } from '@ngrx/store';
import { get, last } from 'lodash-es';
import { BehaviorSubject, map, of } from 'rxjs';

@Component({
  selector: 'ns-move-file-modal',
  templateUrl: './move-file-modal.component.html',
  styleUrl: './move-file-modal.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class MoveFileModalComponent implements OnInit {
  @Output() confirmMove = new EventEmitter<{
    sourceFolderGroup: Group;
    targetFolderId: string;
    targetFolderGroup: Group;
  }>();

  finSize = FinSize;
  finButtonAppearance = FinButtonAppearance;
  breadcrumbs: FinBreadcrumbItem[] = [];
  currentFolder: Folder;
  targetFolderGroup: Group;
  groups: Group[] = this.data.groupsOrdered;

  private currentFolder$$ = new BehaviorSubject<Folder>(null);

  currentFolderDocuments$ = this.currentFolder$$.pipe(
    map((currentFolder) =>
      this.getCurrentFolderDocuments(
        currentFolder,
        this.data.businessCaseInformation,
      ),
    ),
  );

  getMoveFolderOrDocumentLoading$ = this.store.select(
    folderStructureFeature.selectMoveFolderOrDocumentLoading,
  );

  @ViewChild('folderSectionScrollbar')
  folderSectionScrollbar: FinScrollbarComponent;

  get isCurrentFolderEmpty(): boolean {
    return (
      !this.currentFolder?.children?.length &&
      !this.currentFolder?.fields?.length
    );
  }

  constructor(
    private store: Store,
    private documentFieldManageHelperService: DocumentFieldManageHelperService,
    @Inject(FIN_MODAL_DATA)
    private data: {
      sourceGroupKey: string;
      businessCaseInformation: InformationRecord;
      groupsOrdered: Group[];
    },
  ) {}

  ngOnInit() {
    this.selectGroup(this.groups[0]);
  }

  navigateToSubfolder(folder: Folder, index: number): void {
    this.updateCurrentFolder(folder);
    this.updateCurrentFolderBreadcrumbs(index);
  }

  private updateCurrentFolderBreadcrumbs(subfolderIndex: number): void {
    // TODO: Move to helper (used in multiple places)
    const lastBreadcrumb = last(this.breadcrumbs);

    const newBreadcrumbPath = lastBreadcrumb.data.path
      ? `${lastBreadcrumb.data.path}.children[${subfolderIndex}]`
      : `children[${subfolderIndex}]`;

    const newBreadcrumb = {
      label: this.currentFolder.name,
      data: {
        folderId: this.currentFolder.id,
        path: newBreadcrumbPath,
      },
    };

    this.breadcrumbs = [...this.breadcrumbs, newBreadcrumb];
  }
  getCurrentFolderDocuments(
    currentFolder: Folder | null,
    information: InformationRecord,
  ) {
    return currentFolder?.fields?.map((documentKey) => {
      const info = information[documentKey as keyof InformationRecord];
      return {
        information: info,
        field: info.field,
        img$: this.getDocumentThumbnail(info.value),
      };
    });
  }

  onSubmit(): void {
    const folderGroup = this.groups.find(
      (group) => group.key === this.data.sourceGroupKey,
    );
    this.confirmMove.emit({
      sourceFolderGroup: folderGroup,
      targetFolderId: this.currentFolder.id,
      targetFolderGroup: this.targetFolderGroup,
    });
  }

  onBreadcrumbClick(selectedBreadcrumb: FinBreadcrumbItem): void {
    // TODO: Move to helper (used in multiple places)
    if (
      selectedBreadcrumb.data.folderId === this.targetFolderGroup.rootFolder.id
    ) {
      this.navigateToRootFolder();
      return;
    }

    const selectedBreadcrumbIndex = this.breadcrumbs.findIndex(
      (breadcrumb: FinBreadcrumbItem): boolean =>
        breadcrumb.data.folderId === selectedBreadcrumb.data.folderId,
    );

    this.updateCurrentFolder(
      get(this.targetFolderGroup.rootFolder, selectedBreadcrumb.data.path),
    );
    this.breadcrumbs = this.breadcrumbs.slice(0, selectedBreadcrumbIndex + 1);
  }

  private navigateToRootFolder(): void {
    // TODO: Move to helper (used in multiple places)
    this.updateCurrentFolder(this.targetFolderGroup.rootFolder);
    this.breadcrumbs = [
      {
        label: $localize`:@@folderStructure.sectionTitle:Dokumente`,
        data: {
          folderId: this.targetFolderGroup.rootFolder?.id,
          path: '',
        },
      },
    ];
  }

  selectGroup(group: Group): void {
    this.targetFolderGroup = group;
    this.navigateToRootFolder();
    void this.folderSectionScrollbar?.scrollTo({ top: 0 });
  }

  getDocumentThumbnail(documentId: string) {
    if (InformationUtils.isEmptyValue(documentId)) {
      return of(null);
    }

    return this.documentFieldManageHelperService.getDocumentThumbnail(
      documentId,
    );
  }

  updateCurrentFolder(selectedFolder: Folder): void {
    this.currentFolder = selectedFolder;
    this.currentFolder$$.next(selectedFolder);
  }
}
