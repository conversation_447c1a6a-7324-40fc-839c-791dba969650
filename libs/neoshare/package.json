{"name": "@fincloud/neoshare", "peerDependencies": {"@angular/common": "17.3.11", "@angular/core": "17.3.11", "@angular/forms": "17.3.11", "@angular/router": "17.3.11", "@fincloud/components": "0.0.1", "@fincloud/core": "0.0.1", "@fincloud/state": "0.0.1", "@fincloud/swagger-generator": "0.0.1", "@fincloud/types": "0.0.1", "@ngrx/store": "17.2.0", "@ngrx/operators": "17.2.0", "rxjs": "7.8.1", "@ngrx/entity": "17.2.0", "@ngx-formly/core": "6.3.3", "@ng-bootstrap/ng-bootstrap": "16.0.0", "ngx-scrollbar": "16.1.0", "@angular/platform-browser": "17.3.11", "@angular/animations": "17.3.11", "lodash-es": "4.17.21", "ngx-currency": "17.0.0", "ngx-permissions": "17.1.0", "ngx-ui-tour-ngx-bootstrap": "12.1.0", "pdfjs-dist": "4.3.136", "ngx-webstorage": "13.0.1", "@fincloud/ui": "0.0.643", "@angular/cdk": "17.3.10", "@fincloud/utils": "0.0.1"}, "sideEffects": false, "version": "0.0.1"}