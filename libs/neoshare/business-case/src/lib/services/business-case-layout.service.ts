import { Injectable, inject } from '@angular/core';
import {
  selectInvitationIsVisibleAndHeaderIsExpanded,
  selectIsBusinessCaseHeaderInView,
} from '@fincloud/state/business-case';
import { Store } from '@ngrx/store';
import { Observable, map, shareReplay, startWith, withLatestFrom } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class BusinessCaseLayoutService {
  private readonly store = inject(Store);

  private readonly APP_HEADER_HEIGHT = 5.6;
  private readonly APP_FOOTER_HEIGHT = 3.5;
  private readonly BUSINESS_CASE_TOP_PADDING = 2.5;
  private readonly INVITATION_MESSAGE_HEIGHT = 6.6;
  private readonly NORMAL_HEADER_HEIGHT = 13;
  private readonly COMPACT_HEADER_HEIGHT = 6.4;
  private readonly TABS_HEIGHT = 4.8;
  private readonly KPIS_TOOLBAR_HEIGHT = 5.467;
  private readonly INVITATIONS_TOOLBAR_HEIGHT = 4.933;
  private readonly ADMINISTRATION_TOOLBAR_HEIGHT = 8.53;
  private readonly DATA_ROOM_TOOLBAR_HEIGHT = 8;
  private readonly CADR_TOOLBAR_HEIGHT = 12.4;
  private readonly REFS_TOOLBAR_HEIGHT = 15.6;
  private readonly REFS_SHARED_TOOLBAR_HEIGHT = 13.6;

  readonly headerHeight$ = this.store
    .select(selectInvitationIsVisibleAndHeaderIsExpanded)
    .pipe(
      map(({ invitationMessageIsVisible, headerIsExpanded }) => {
        const invitationMessageHeight = invitationMessageIsVisible
          ? this.INVITATION_MESSAGE_HEIGHT
          : 0;
        const headerCardHeight = headerIsExpanded
          ? this.NORMAL_HEADER_HEIGHT
          : this.COMPACT_HEADER_HEIGHT;
        return (
          this.BUSINESS_CASE_TOP_PADDING +
          invitationMessageHeight +
          headerCardHeight
        );
      }),
      startWith(0),
      shareReplay({
        refCount: true,
        bufferSize: 1,
      }),
    );

  readonly commonOffset$ = this.headerHeight$.pipe(
    map(
      (headerHeight) =>
        headerHeight +
        this.APP_FOOTER_HEIGHT +
        this.APP_HEADER_HEIGHT +
        this.TABS_HEIGHT,
    ),
  );

  readonly sidePanelHeight$ = this.commonOffset$.pipe(
    map((offset) => `calc(100vh - ${offset + this.TABS_HEIGHT}rem)`),
  );

  readonly sidePanelHeightWithoutSecondaryTabs$ = this.commonOffset$.pipe(
    map((offset) => `calc(100vh - ${offset}rem)`),
  );

  readonly contentWrapperHeight$ = this.commonOffset$.pipe(
    map((offset) => `calc(100vh - ${offset + this.TABS_HEIGHT}rem)`),
  );

  readonly contentWrapperHeightWithoutSecondaryTabs$ = this.commonOffset$.pipe(
    map((offset) => `calc(100vh - ${offset}rem)`),
  );

  readonly participantsContentWrapperHeight$ = this.commonOffset$.pipe(
    map((offset) => `calc(100vh - ${offset + this.TABS_HEIGHT}rem)`),
  );

  readonly contentScrollHeightWithoutSecondaryTabs$ = this.commonOffset$.pipe(
    map((offset) => `calc(100vh - ${offset}rem)`),
  );

  readonly dataRoomContentWrapperHeight$ = this.commonOffset$.pipe(
    map(
      (offset) =>
        `calc(100vh - ${offset + this.TABS_HEIGHT + this.DATA_ROOM_TOOLBAR_HEIGHT}rem)`,
    ),
  );

  readonly refsContentWrapperHeight$ = this.commonOffset$.pipe(
    map(
      (offset) =>
        `calc(100vh - ${offset + this.TABS_HEIGHT + this.REFS_TOOLBAR_HEIGHT}rem)`,
    ),
  );

  readonly refsSharedContentWrapperHeight$ = this.commonOffset$.pipe(
    map(
      (offset) =>
        `calc(100vh - ${offset + this.TABS_HEIGHT + this.REFS_SHARED_TOOLBAR_HEIGHT}rem)`,
    ),
  );

  readonly cadrContentWrapperHeight$ = this.commonOffset$.pipe(
    map(
      (offset) =>
        `calc(100vh - ${offset + this.TABS_HEIGHT + this.CADR_TOOLBAR_HEIGHT}rem)`,
    ),
  );

  readonly kpisContentWrapperHeight$ = this.commonOffset$.pipe(
    map(
      (offset) =>
        `calc(100vh - ${offset + this.TABS_HEIGHT + this.KPIS_TOOLBAR_HEIGHT}rem)`,
    ),
  );

  readonly invitationsContentWrapperHeight$ = this.commonOffset$.pipe(
    map(
      (offset) =>
        `calc(100vh - ${offset + this.TABS_HEIGHT + this.INVITATIONS_TOOLBAR_HEIGHT}rem)`,
    ),
  );

  readonly administrationContentWrapperHeight$ = this.commonOffset$.pipe(
    map(
      (offset) =>
        `calc(100vh - ${offset + this.ADMINISTRATION_TOOLBAR_HEIGHT}rem)`,
    ),
  );

  contentHeightOf(scrollHeight$: Observable<string>): Observable<string> {
    return this.store.select(selectIsBusinessCaseHeaderInView).pipe(
      withLatestFrom(scrollHeight$),
      map(([headerIsExpanded, scrollHeight]) =>
        headerIsExpanded
          ? scrollHeight
          : `calc(${scrollHeight} + ${this.NORMAL_HEADER_HEIGHT - this.COMPACT_HEADER_HEIGHT}rem)`,
      ),
    );
  }
}
