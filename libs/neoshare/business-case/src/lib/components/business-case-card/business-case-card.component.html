@if (businessCaseInfo) {
  <div class="business-case-card">
    <div class="business-case-card__header">
      <span
        >{{ businessCaseInfo?.autoGeneratedBusinessCaseName }} | Online
        {{ businessCaseInfo?.date | timeAgo }}</span
      >
      <span i18n="@@businessCase.card1">Finanzierungsvolumen</span>
    </div>
    <div class="business-case-card__title align-items-start">
      <div>
        @if (leadPartner) {
          <ui-truncated-text
            class="leader-name heading2"
            widthCalculationExpression="28rem"
            >{{ leadPartner.name }}</ui-truncated-text
          >
        }
        @if (showCompany && businessCaseInfo?.company?.companyInfo?.legalName) {
          <ui-button-link-hover
            [label]="businessCaseInfo?.company?.companyInfo.legalName"
            appAnchorPathBuilder
            [anchorPathSegments]="[
              customerKey$ | async,
              'company-analysis',
              businessCaseInfo?.company?.id,
            ]"
            i18n-hoverTooltipText="@@businessCase.card.moreInformation"
            hoverTooltipText="Weitere Firmeninformationen"
            tooltipPlacement="bottom"
            hoverColor="dark"
            baseLinkUrl="company-analysis"
            [isActive]="businessCaseInfo?.company?.companyState === 'ACTIVE'"
            [id]="businessCaseInfo?.company?.id"
            [customerKey]="customerKey$ | async"
          ></ui-button-link-hover>
        }
      </div>
      <div class="d-flex flex-column align-items-end">
        <ui-monetary-expression
          [value]="businessCaseInfo?.financingVolume"
        ></ui-monetary-expression>
        <!-- TODO - hide this information  -->
        <!-- <ui-progress-bar
            *ngIf="showProgressBar"
            background="subtle"
            progressColor="secondary"
            [value]="businessCaseInfo.investedAmount"
            [total]="businessCaseInfo.financingVolume"
          size="large"></ui-progress-bar> -->
      </div>
    </div>
    <div class="business-case-card__footer">
      <div class="business-case-type-name-container">
        <ui-icon
          [name]="businessCaseTypeIcons[businessCaseInfo.businessCaseType]"
        ></ui-icon>
        <span class="business-case-type-name">{{
          businessCaseTypeLabels[businessCaseInfo.businessCaseType]
        }}</span>
        <div class="tags">
          @for (c of businessCaseInfo.characteristics; track c) {
            <ui-tag [label]="c" type="business-case"></ui-tag>
          }
        </div>
      </div>
      <ui-button-link
        appMetaKeyOrCtrlKey
        [path]="'business-case'"
        [id]="businessCaseInfo.id"
        (executeOnClickChange)="onMoreInfoClicked($event)"
        appAnchorPathBuilder
        [anchorPathSegments]="['business-case', businessCaseInfo.id]"
        i18n-label="@@businessCase.card.navigateToCase"
        label="Zum Fall"
        color="dark"
      ></ui-button-link>
    </div>
  </div>
}
