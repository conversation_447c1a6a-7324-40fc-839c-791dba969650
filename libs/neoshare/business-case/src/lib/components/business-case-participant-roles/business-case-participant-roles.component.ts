import {
  AfterViewInit,
  ChangeDetectorRef,
  Component,
  DestroyRef,
  Input,
  OnChanges,
  OnInit,
  ViewChild,
} from '@angular/core';
import { Router } from '@angular/router';
import {
  ParticipantCasePermissionSetEntity,
  ParticipantControllerService,
  TransferLeadershipControllerService,
} from '@fincloud/swagger-generator/business-case-manager';
import { ChatUser } from '@fincloud/swagger-generator/communication';
import {
  BusinessCaseParticipantCustomer,
  CadrShareObject,
  ExchangeBusinessCase,
} from '@fincloud/swagger-generator/exchange';

import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { ConfirmationModalComponent } from '@fincloud/components/modals';
import { FieldInputRequestState } from '@fincloud/core/business-case';
import { Toast } from '@fincloud/core/toast';
import { PortalActionsService } from '@fincloud/neoshare/services';
import {
  StateLibBusinessCasePageActions,
  StateLibParticipantsPageActions,
  selectAllParticipants,
  selectBusinessCase,
  selectBusinessCaseContentHeightDefault,
  selectBusinessCaseParticipantsPermissions,
  selectBusinessCaseSidePanelHeightDefault,
  selectBusinessCaseWrapperHeightDefault,
  selectCaseFieldsInputsRequests,
  selectCurrentlySelectedParticipant,
  selectCustomerNamesByKey,
  selectIsBusinessCaseActive,
  selectUsersById,
} from '@fincloud/state/business-case';
import { StateLibBusinessCaseRealEstatePageActions } from '@fincloud/state/business-case-real-estate';
import {
  StateLibSideNavigationsPageActions,
  sideNavigationsFeature,
} from '@fincloud/state/side-navigations';
import { selectUserCustomerKey } from '@fincloud/state/user';
import {
  Customer,
  User,
} from '@fincloud/swagger-generator/authorization-server';
import {
  BusinessCasePermission,
  BusinessCaseType,
  CustomerStatus,
  CustomerType,
  GroupVisibilityEnum,
} from '@fincloud/types/enums';
import { Dictionary } from '@fincloud/types/models';
import { FinParticipantType } from '@fincloud/ui/avatar-participants';
import { FinButtonAppearance, FinButtonShape } from '@fincloud/ui/button';
import { FinModalService } from '@fincloud/ui/modal';
import { FinToastService } from '@fincloud/ui/toast';
import { FinSize } from '@fincloud/ui/types';
import { Store } from '@ngrx/store';
import { keyBy, uniq } from 'lodash-es';
import { NgxPermissionsService } from 'ngx-permissions';
import { Observable, Subject, combineLatest, zip } from 'rxjs';
import { filter, shareReplay, switchMap, take, tap } from 'rxjs/operators';
import { BusinessCaseLayoutService } from '../../services/business-case-layout.service';
import { MAX_MESSAGE } from '../../utils/max-message';
import { MIN_MESSAGE } from '../../utils/min-message';
import { warningMessage } from '../../utils/warning-message';
import { BusinessCaseRolesComponent } from '../business-case-roles/business-case-roles.component';
import { ConfirmationDialogSvgComponent } from '../confirmation-dialog-svg/confirmation-dialog-svg.component';
import { ParticipationAmountRangeComponent } from '../participation-amount-range/participation-amount-range.component';

@Component({
  selector: 'app-business-case-participant-roles',
  templateUrl: './business-case-participant-roles.component.html',
  styleUrls: ['./business-case-participant-roles.component.scss'],
})
export class BusinessCaseParticipantRolesComponent
  implements OnInit, OnChanges, AfterViewInit
{
  @ViewChild('businessCaseRolesComponent')
  businessCaseRolesComponent: BusinessCaseRolesComponent;

  @Input() businessCase: ExchangeBusinessCase;
  @Input() readOnly = false;
  @Input() customerType: CustomerType;

  businessCaseType: 'REAL_ESTATE' | 'CORPORATE' | 'MISCELLANEOUS';
  allParticipants: BusinessCaseParticipantCustomer[];
  leadCustomer: BusinessCaseParticipantCustomer;

  customerKeyNames: Dictionary<Customer>;
  usersById: Dictionary<User>;
  partnersUsers: Record<string, ChatUser[]> = {};
  participantType = FinParticipantType;

  confirmationModalClass = 'confirmation-dialog';
  submitTransferButtonLabel = $localize`:@@businessCaseParticipantRoles.label.button.request:Finanzierungsvorhaben übergeben`;
  removeParticipantButtonLabel = $localize`:@@businessCaseParticipantRoles.label.button.remove:Teilnehmer entfernen`;
  participantRequirementsButton = $localize`:@@dashboard.collaboration.myPartners.participantRequirements:Teilnehmeranforderungen`;

  readonly finSize = FinSize;
  readonly finButtonShape = FinButtonShape;
  readonly finButtonAppearance = FinButtonAppearance;
  readonly currentCustomerKey$: Observable<string> = this.store.select(
    selectUserCustomerKey,
  );

  _customerNamesLoaded = new Subject();
  _userNamesLoaded = new Subject();

  loaded = false;

  isBusinessCaseTypeFinancing: boolean;
  isRealEstateCustomer: boolean;
  warningMessage: string;

  private participantPermissions: Record<
    string,
    ParticipantCasePermissionSetEntity
  >;
  customersByKeys: Dictionary<Customer>;

  readonly businessCasePermission = BusinessCasePermission;

  participantContextEntities$ = this.store
    .select(selectBusinessCaseParticipantsPermissions)
    .pipe(
      takeUntilDestroyed(this.destroyRef),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );

  participantsAndLeadersData$ = this.store.select(selectAllParticipants);

  currentlySelectedParticipant$ = this.store
    .select(selectCurrentlySelectedParticipant)
    .pipe(shareReplay({ refCount: true, bufferSize: 1 }));

  isBusinessCaseActive$ = this.store.select(selectIsBusinessCaseActive);

  readonly sidePanelHeight$ = this.store.select(
    selectBusinessCaseSidePanelHeightDefault,
  );

  readonly contentWrapperHeight$ = this.store.select(
    selectBusinessCaseWrapperHeightDefault,
  );

  readonly contentHeight$ = this.store.select(
    selectBusinessCaseContentHeightDefault,
  );

  readonly navigationsAreOpen$ = this.store.select(
    sideNavigationsFeature.selectNavigationAndChatAreOpen,
  );

  constructor(
    private destroyRef: DestroyRef,
    private store: Store,
    private finToastService: FinToastService,
    private transferLeadershipControllerService: TransferLeadershipControllerService,
    private participantControllerService: ParticipantControllerService,
    private cd: ChangeDetectorRef,
    private router: Router,
    private portalActionsService: PortalActionsService,
    private ngxPermissionsService: NgxPermissionsService,
    private finModalService: FinModalService,
    private businessCaseLayoutService: BusinessCaseLayoutService,
  ) {
    zip([this._customerNamesLoaded, this._userNamesLoaded])
      .pipe(take(1))
      .subscribe(() => {
        this.loaded = true;
        this.cd.detectChanges();
      });
  }

  ngOnChanges() {
    this.isRealEstateCustomer = this.customerType === CustomerType.REAL_ESTATE;
  }

  ngOnInit(): void {
    this.store
      .select(selectCustomerNamesByKey)
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((customers) => {
        this.customersByKeys = customers;
      });
    combineLatest([
      this.store
        .select(selectBusinessCase)
        .pipe(filter((businessCase) => !!businessCase)),
      this.store
        .select(selectUsersById)
        .pipe(filter((c) => !!Object.keys(c)?.length)),
    ])
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(([businessCase, usersById]) => {
        this.usersById = usersById;
        this.businessCase = businessCase;

        this.allParticipants = this.businessCase?.participants.filter(
          (participant) => !participant.lead,
        );
        this.updateWarningMessageForRangeContributions(this.allParticipants);

        this.leadCustomer = this.businessCase?.participants.find((p) => p.lead);

        this.partnersUsers = this.businessCase?.participants.reduce(
          (acc, curr) => {
            const participantUsersNames =
              this.usersById &&
              curr.users
                .filter((u) => this.usersById[u.userId])
                .map((u) => {
                  const userInfo = this.usersById[u.userId];
                  return {
                    academicTitle: userInfo.attributes?.academicTitle,
                    customerKey: userInfo?.customerKey,
                    department: userInfo.attributes?.department,
                    username: userInfo.username,
                    enabled: userInfo.enabled,
                    firstName: userInfo.firstName,
                    id: userInfo.id,
                    landlineNumber: userInfo.attributes?.landlineNumber,
                    lastName: userInfo.lastName,
                    mobileNumber: userInfo.attributes?.mobileNumber,
                    position: userInfo.attributes?.position,
                    salutation: userInfo.attributes?.salutation,
                    userId: userInfo.id,
                    email: userInfo.username,
                  } as ChatUser;
                });
            acc[curr.customerKey] = participantUsersNames;

            return acc;
          },
          {} as Record<string, ChatUser[]>,
        );

        this._userNamesLoaded.next(true);
      });

    this.store
      .select(selectBusinessCaseParticipantsPermissions)
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((permissions) => {
        this.participantPermissions = permissions;
      });

    this.store
      .select(selectCustomerNamesByKey)
      .pipe(
        filter((c) => !!Object.keys(c)?.length),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe((customerKeyNames) => {
        this.customerKeyNames = customerKeyNames;

        this._customerNamesLoaded.next(true);
      });

    this.isBusinessCaseTypeFinancing =
      this.businessCase?.businessCaseType === BusinessCaseType.FINANCING_CASE;
  }

  ngAfterViewInit(): void {
    this.businessCaseType = this.businessCase?.structuredFinancingConfiguration
      ?.financingStructureType as 'REAL_ESTATE' | 'CORPORATE' | 'MISCELLANEOUS';
  }

  trackByParticipantKey(_: number, elem: BusinessCaseParticipantCustomer) {
    return elem.customerKey;
  }

  updateParticipationAmount(
    totalParticipationAmount: number,
    customerKey: string,
    currentCustomerKey: string,
  ): void {
    if (currentCustomerKey === customerKey) {
      this.store.dispatch(
        StateLibBusinessCaseRealEstatePageActions.updateMyParticipationAmount({
          payload: {
            businessCaseId: this.businessCase.id,
            totalParticipationAmount,
          },
        }),
      );
      return;
    }

    this.store.dispatch(
      StateLibBusinessCasePageActions.updateCustomerParticipationAmount({
        payload: {
          customerKey,
          businessCaseId: this.businessCase.id,
          totalParticipationAmount,
        },
      }),
    );
  }

  requestTransferLeadership(newLeaderCustomerKey: string): void {
    const allGroupsVisibleToNewLeader =
      this.checkAllGroupsVisibleToNewLeader(newLeaderCustomerKey);

    const customerKeys = uniq(
      this.businessCase.company.companyTemplate?.shareObjects?.map(
        (so) => so.customerKey,
      ),
    );

    if (customerKeys?.length) {
      const customersExplicitShare = this.filterCustomersExplicitlyShared();

      const isCadrExplicitSharedNewLeader = customersExplicitShare.some(
        (customer) => customer.customerKey === newLeaderCustomerKey,
      );

      const otherCaseParticipantsExplicit =
        this.filterOtherCaseParticipantsExplicit(
          newLeaderCustomerKey,
          customersExplicitShare,
        );

      if (allGroupsVisibleToNewLeader) {
        this.openCaseTransferModal(
          newLeaderCustomerKey,
          isCadrExplicitSharedNewLeader,
          otherCaseParticipantsExplicit,
        );
      } else {
        this.finModalService
          .open<boolean, ConfirmationModalComponent>(
            ConfirmationModalComponent,
            {
              data: {
                title: $localize`:@@businessCase.caseTransfer.warningModal.text:Es gibt Gruppen im Data Room, welche aktuell für diesen Teilnehmer nicht sichtbar sind.`,
                message: $localize`:@@businessCase.caseTransfer.warningModal.description:Klicken Sie auf Weiter, wenn Sie sich sicher sind, dass Sie die Gruppen für den Teilnehmer sichtbar machen wollen und die Informationen mit ihm teilen möchten, sobald er Fallinhaber dieses Finanzierungsfalls ist. Klicken Sie auf Überprüfen, um die betreffenden Gruppen einzusehen.`,
                confirmButtonAppearance: FinButtonAppearance.PRIMARY,
                confirmLabel: $localize`:@@button.label.continue:Fortfahren`,
                cancelLabel: $localize`:@@button.label.review:Überprüfen`,
                svgIcon: 'svgCaseTransferWarningIcon',
                size: FinSize.L,
              },
              size: FinSize.S,
              disableClose: true,
            },
          )
          .afterClosed()
          .subscribe((resp) => {
            resp
              ? this.openCaseTransferModal(
                  newLeaderCustomerKey,
                  isCadrExplicitSharedNewLeader,
                  otherCaseParticipantsExplicit,
                )
              : this.businessCaseRolesComponent.triggerBusinessCaseParticipantsAccessRightsModal();
          });
      }
    }
  }

  private checkAllGroupsVisibleToNewLeader(
    newLeaderCustomerKey: string,
  ): boolean {
    const groups =
      this.businessCase.businessCaseTemplate?.template?.groupsOrdered || [];
    const allGroupsArePublic = groups.every(
      (group) =>
        group.groupVisibility?.visibility === GroupVisibilityEnum.PUBLIC,
    );
    const allNonPublicGroupsVisibleToNewLeader = groups
      .filter(
        (group) =>
          group.groupVisibility?.visibility !== GroupVisibilityEnum.PUBLIC,
      )
      .every((group) =>
        group.groupVisibility?.currentParticipantsWithVisibility?.includes(
          newLeaderCustomerKey,
        ),
      );

    return allGroupsArePublic || allNonPublicGroupsVisibleToNewLeader;
  }

  private filterCustomersExplicitlyShared(): CadrShareObject[] {
    const customersByKey = keyBy(this.customerKeyNames, 'key');
    return this.businessCase.company.companyTemplate?.shareObjects
      .filter((so) => so.type === 'EXPLICIT')
      .map((so) => {
        return {
          ...so,
          customerName: customersByKey[so.customerKey]?.name,
        };
      });
  }

  private filterOtherCaseParticipantsExplicit(
    newLeaderCustomerKey: string,
    customersExplicitShare: CadrShareObject[],
  ): boolean {
    const explicitShareCustomers = customersExplicitShare.filter(
      ({ customerKey }) =>
        customerKey !== newLeaderCustomerKey &&
        customerKey !== this.businessCase.leadCustomerKey,
    );
    const participants = this.businessCase.participants.filter(
      ({ customerKey }) =>
        customerKey !== newLeaderCustomerKey &&
        customerKey !== this.businessCase.leadCustomerKey,
    );
    return (
      explicitShareCustomers.length === participants.length &&
      explicitShareCustomers.every(({ customerKey }) =>
        participants.some(
          (participant) => participant.customerKey === customerKey,
        ),
      )
    );
  }

  openCaseTransferModal(
    customerKey: string,
    isCadrExplicitSharedNewLeader: boolean,
    otherCaseParticipantsExplicit: boolean,
  ) {
    this.finModalService
      .open<
        {
          newCaseOwner: boolean;
          otherCaseParticipants: boolean;
        },
        ConfirmationDialogSvgComponent
      >(ConfirmationDialogSvgComponent, {
        data: {
          customerName: this.customerKeyNames[customerKey].name,
          confirmationMessage: $localize`:@@businessCase.participantRoles.confirmationMessage:Möchten Sie wirklich den Finanzierungsfall an folgende Organisation übergeben?`,
          svgIcon: 'svgTransferLeadership',
          confirmLabel: $localize`:@@businessCase.participantRoles.confirmLabel:Übergeben`,
          cancelLabel: $localize`:@@businessCase.participantRoles.cancelLabel:Abbrechen`,
          transferLeadership: true,
          isCadrExplicitSharedNewLeader: isCadrExplicitSharedNewLeader,
          otherCaseParticipantsExplicit: otherCaseParticipantsExplicit,
        },
        size: FinSize.S,
        disableClose: true,
      })
      .afterClosed()
      .pipe(
        filter((result) => !!result),
        switchMap((result) => {
          return this.transferLeadershipControllerService.requestTransferLeadership(
            {
              businessCaseId: this.businessCase.id,
              newLeaderCustomerKey: customerKey,
              shouldShareCADRExplicitlyWithNewLeader: result.newCaseOwner,
              shouldShareCADRExplicitlyWithAllParticipants:
                result.otherCaseParticipants,
            },
          );
        }),
        tap({
          next: (businessCase) => {
            this.finToastService.show(Toast.success());
            this.store.dispatch(
              StateLibBusinessCasePageActions.loadBusinessCase({
                payload: this.businessCase.id,
              }),
            );
            void this.router.navigate([
              businessCase.leadCustomerKey,
              'business-case',
              this.businessCase.id,
            ]);
          },
          error: () => {
            this.finToastService.show(Toast.error());
          },
        }),
      )
      .subscribe();
  }

  removeParticipant(customerKey: string) {
    this.finModalService
      .open(ConfirmationDialogSvgComponent, {
        data: {
          customerName: this.customerKeyNames[customerKey].name,
          confirmationMessage: $localize`:@@businessCase.participantRoles.confirmationMessage.removeParticipant:Sind Sie sicher, dass Sie diesen Teilnehmer löschen möchten?`,
          svgIcon: 'svgRemoveParticipant',
          confirmLabel: $localize`:@@businessCase.participantRoles.confirmLabel.removeParticipant:Ja, ich bin sicher`,
          cancelLabel: $localize`:@@businessCase.participantRoles.cancelLabel.removeParticipant:Nein`,
        },
        size: FinSize.S,
        disableClose: true,
      })
      .afterClosed()
      .pipe(
        filter((result) => !!result),
        switchMap(() =>
          this.participantControllerService.deleteCustomerFromBusinessCase({
            customerKey: customerKey,
            businessCaseId: this.businessCase.id,
          }),
        ),
        tap({
          next: () => {
            this.finToastService.show(Toast.success());
            this.withdrawRequestedInputs(customerKey);
            this.store.dispatch(
              StateLibBusinessCasePageActions.loadBusinessCase({
                payload: this.businessCase.id,
              }),
            );
          },
          error: () => {
            this.finToastService.show(Toast.error());
          },
        }),
      )
      .subscribe();
  }

  isOwnershipRequested(participant: BusinessCaseParticipantCustomer): boolean {
    const confirmation = this.businessCase?.newLeadershipConfirmation;
    return (
      confirmation?.currentConfirmationState === 'REQUESTED' &&
      confirmation?.newLeaderCustomerKey === participant.customerKey
    );
  }

  combineBankAndSigner() {
    return `${
      this.customerKeyNames[this.leadCustomer.customerKey]?.name
    } ${$localize`:@@createBusinessCase.formField.label.signer:Nutzer`}`;
  }

  getTranslatedLabel(nameOfBank: string) {
    return `${nameOfBank} ${$localize`:@@createBusinessCase.formField.label.signer:Nutzer`}`;
  }

  private updateWarningMessageForRangeContributions(
    participants: BusinessCaseParticipantCustomer[],
  ) {
    const minAmount = this.businessCase.minParticipationAmount;
    const maxAmount = this.businessCase.maxParticipationAmount;

    const outOfRangeContributionsMin =
      participants?.filter((p) => p.totalParticipationAmount < minAmount)
        .length || 0;

    const outOfRangeContributionsMax =
      participants?.filter((p) => p.totalParticipationAmount > maxAmount)
        .length || 0;

    if (outOfRangeContributionsMin > 0 && outOfRangeContributionsMax > 0) {
      this.warningMessage = `${warningMessage(outOfRangeContributionsMin, MIN_MESSAGE)}; ${warningMessage(outOfRangeContributionsMax, MAX_MESSAGE)}`;
    } else if (outOfRangeContributionsMin > 0) {
      this.warningMessage = warningMessage(
        outOfRangeContributionsMin,
        MIN_MESSAGE,
      );
    } else if (outOfRangeContributionsMax > 0) {
      this.warningMessage = warningMessage(
        outOfRangeContributionsMax,
        MAX_MESSAGE,
      );
    } else {
      this.warningMessage = '';
    }
  }

  private withdrawRequestedInputs(customerKey: string) {
    this.store
      .select(selectCaseFieldsInputsRequests)
      .pipe(take(1))
      .subscribe((inputRequests) => {
        const customerRequestedInputRequests = inputRequests.filter(
          (c) =>
            c.customerKey === customerKey &&
            [
              FieldInputRequestState.REQUESTED,
              FieldInputRequestState.RE_REQUESTED,
              FieldInputRequestState.OPEN_FOR_MODIFICATION,
            ].includes(c.state as FieldInputRequestState),
        );
        if (!customerRequestedInputRequests?.length) {
          return;
        }

        customerRequestedInputRequests.forEach((inputRequest) => {
          this.portalActionsService.changePortalFieldInputRequest(
            FieldInputRequestState.WITHDRAWN,
            '',
            inputRequest.fieldKey,
            inputRequest.informationId,
            false,
            inputRequest.caseId,
            'CUSTOMER',
            [],
            customerKey,
          );

          this.portalActionsService.changePortalFieldVisibility(
            false,
            inputRequest.fieldKey,
            inputRequest.informationId,
            inputRequest.caseId,
            'CUSTOMER',
            customerKey,
          );
        });
      });
  }

  checkBusinessCasePermission(participantCustomerKey: string) {
    return (
      this.participantPermissions[
        participantCustomerKey
      ]?.permissions?.includes(BusinessCasePermission.BCP_00134) &&
      this.ngxPermissionsService.getPermission(
        BusinessCasePermission.BCP_00133,
      ) &&
      this.customersByKeys[participantCustomerKey]?.customerStatus !==
        CustomerStatus.GUEST &&
      this.customersByKeys[this.businessCase.leadCustomerKey]
        ?.customerStatus !== CustomerStatus.GUEST
    );
  }

  setSelectedParticipant(selectedParticipant: BusinessCaseParticipantCustomer) {
    this.store.dispatch(
      StateLibParticipantsPageActions.setSelectedParticipant({
        selectedParticipant,
      }),
    );
  }

  openParticipationAmountRangeModal() {
    this.finModalService.open(ParticipationAmountRangeComponent, {
      data: {
        businessCaseId: this.businessCase.id,
        minAmountToEdit: this.businessCase.minParticipationAmount,
        maxAmountToEdit: this.businessCase.maxParticipationAmount,
        readOnly: this.readOnly,
      },
      size: FinSize.S,
      disableClose: true,
    });
  }

  toggleNavigation(navigation: {
    isChatVisible: boolean;
    platformNavigationIsOpen: boolean;
  }) {
    if (navigation.isChatVisible && navigation.platformNavigationIsOpen) {
      this.store.dispatch(
        StateLibSideNavigationsPageActions.toggleOpenForPlatformNavigation(),
      );
    }
    this.store.dispatch(
      StateLibSideNavigationsPageActions.toggleOpenForPageNavigation(),
    );
  }
}
