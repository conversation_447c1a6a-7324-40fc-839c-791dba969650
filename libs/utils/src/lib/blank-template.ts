import { TemplateDto } from '@fincloud/swagger-generator/business-case-manager';

export const BLANK_TEMPLATE: TemplateDto = {
  name: 'VORLAGENNAME',
  versionDescription: 'VERSIONSBESCHREIBUNG',
  defaultFinancingProduct: 'FINANZIERUNGSPRODUKT',
  defaultIsPromoted: true,
  defaultIsPublic: true,
  excludeFromSearch: false,
  financingStructureType: 'MISCELLANEOUS',
  fields: [
    {
      key: 'financingTerm',
      isRequired: false,
      priority: 1,
      label: $localize`:@@dashboard.businessCase.templateFields.timeInMonths:Laufzeit in Monaten`,
      fieldType: 'MONTHS',
      portalVisibility: 'NOT_SET',
    },
    {
      key: 'financingPurpose',
      isRequired: false,
      priority: 1,
      label: $localize`:@@blankTemplate.financingPurpose:Finanzierungszweck`,
      fieldType: 'LONG_TEXT',
      portalVisibility: 'NOT_SET',
    },
    {
      key: 'financingProduct',
      isRequired: false,
      priority: 1,
      label: $localize`:@@blankTemplate.product:Produkt`,
      fieldType: 'SHORT_TEXT',
      portalVisibility: 'NOT_SET',
    },
    {
      key: 'disagio',
      isRequired: false,
      priority: 1,
      label: $localize`:@@characteristics.disagio:Disagio`,
      fieldType: 'PERCENT',
      portalVisibility: 'NOT_SET',
    },
    {
      key: 'interestRate',
      isRequired: false,
      priority: 1,
      label: $localize`:@@blankTemplate.interestRate:Zinssatz`,
      fieldType: 'PERCENT',
      portalVisibility: 'NOT_SET',
    },
    {
      key: 'interestRateComission',
      isRequired: false,
      priority: 1,
      label: $localize`:@@blankTemplate.interestRateConsortiumPartner:Zinsertrag Konsortialpartner`,
      fieldType: 'PERCENT',
      portalVisibility: 'NOT_SET',
    },
    {
      key: 'rating',
      isRequired: false,
      priority: 1,
      label: $localize`:@@blankTemplate.rating:Bewertung`,
      fieldType: 'SELECT',
      fieldMetaData: [
        '1A',
        '1B',
        '1C',
        '1D',
        '1E',
        '2A',
        '2B',
        '2C',
        '2D',
        '2E',
        '3A',
        '3B',
        '3C',
        '3D',
        '3E',
        '4A',
        '4B',
        '4C',
        '4D',
        '4E',
      ],
      portalVisibility: 'NOT_SET',
    },
    {
      key: 'companyId',
      isRequired: true,
      priority: 1,
      label: $localize`:@@dashboard.businessCase.dataRoom.tabs.company:Unternehmen`,
      fieldType: 'SHORT_TEXT',
      portalVisibility: 'NOT_SET',
    },
    {
      key: 'description',
      isRequired: false,
      priority: 1,
      label: $localize`:@@contractManagement.template.templateModal.description.label:Beschreibung`,
      fieldType: 'LONG_TEXT',
      portalVisibility: 'NOT_SET',
    },
    {
      key: 'investmentLocation',
      isRequired: false,
      priority: 1,
      label: $localize`:@@blankTemplate.investmentLocation:Investitionsstandort`,
      fieldType: 'LOCATION',
      portalVisibility: 'NOT_SET',
    },
  ],
  groupsOrdered: [
    {
      key: 'basisinformationen',
      value: $localize`:@@basicFacility.model.basisInfo:Basisinformationen`,
      portalVisibility: 'NOT_SET',
      fields: [
        'financingProduct',
        'financingPurpose',
        'disagio',
        'financingTerm',
        'interestRate',
        'interestRateComission',
        'rating',
        'description',
      ],
      groupVisibility: {
        visibility: 'PUBLIC',
        visibleForNewInterestedCustomers: false,
        visibleForNewParticipants: false,
      },
    },
    {
      key: 'customerInformation',
      value: $localize`:@@blankTemplate.customerInformation:Kundeninformationen`,
      portalVisibility: 'NOT_SET',
      fields: ['companyId', 'investmentLocation'],
      groupVisibility: {
        visibility: 'PUBLIC',
        visibleForNewInterestedCustomers: false,
        visibleForNewParticipants: false,
      },
    },
    {
      key: 'securities',
      value: $localize`:@@blankTemplate.securities:Sicherheiten`,
      portalVisibility: 'NOT_SET',
      fields: [],
      groupVisibility: {
        visibility: 'PRIVATE',
        visibleForNewInterestedCustomers: false,
        visibleForNewParticipants: false,
      },
    },
    {
      key: 'payoutCriteria',
      value: $localize`:@@blankTemplate.payoutCriteria:Auszahlungskriterien`,
      portalVisibility: 'NOT_SET',
      fields: [],
      groupVisibility: {
        visibility: 'PRIVATE',
        visibleForNewInterestedCustomers: false,
        visibleForNewParticipants: false,
      },
    },
  ],
};
