import { CustomerType } from '@fincloud/types/enums';

const BANK = $localize`:@@customer.type.bank:Bank`;
const FSP = $localize`:@@customer.type.fsp:Finanzdienstleister`;
const REAL_ESTATE = $localize`:@@customer.type.immo:Immo`;
const CORPORATE = $localize`:@@customer.type.corporate:Corporate`;

export const CUSTOMER_TYPE_LABELS: Record<string, string> = {
  [CustomerType.BANK]: BANK,
  [CustomerType.FSP]: FSP,
  [CustomerType.REAL_ESTATE]: REAL_ESTATE,
  [CustomerType.CORPORATE]: CORPORATE,
};
