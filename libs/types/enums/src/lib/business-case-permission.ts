export enum BusinessCasePermission {
  BCP_00000 = 'BCP-00000', // EMPTY STATE/PLACEHOLDER
  BCP_00001 = 'BCP-00001',
  BCP_00002 = 'BCP-00002',
  BCP_00003 = 'BCP-00003',
  BCP_00004 = 'BCP-00004',
  BCP_00005 = 'BCP-00005',
  BCP_00006 = 'BCP-00006',
  BCP_00007 = 'BCP-00007',
  BCP_00008 = 'BCP-00008',
  BCP_00009 = 'BCP-00009',
  BCP_00010 = 'BCP-00010',
  BCP_00011 = 'BCP-00011',
  BCP_00012 = 'BCP-00012',
  BCP_00013 = 'BCP-00013',
  BCP_00014 = 'BCP-00014',
  BCP_00015 = 'BCP-00015',
  BCP_00016 = 'BCP-00016',
  BCP_00017 = 'BCP-00017',
  BCP_00018 = 'BCP-00018',
  BCP_00019 = 'BCP-00019',
  BCP_00020 = 'BCP-00020',
  BCP_00021 = 'BCP-00021',
  BCP_00022 = 'BCP-00022',
  BCP_00024 = 'BCP-00024',
  BCP_00025 = 'BCP-00025',
  BCP_00027 = 'BCP-00027',
  BCP_00028 = 'BCP-00028',
  BCP_00029 = 'BCP-00029',
  BCP_00030 = 'BCP-00030',
  BCP_00031 = 'BCP-00031',
  BCP_00033 = 'BCP-00033',
  BCP_00034 = 'BCP-00034', // Complete and reactivate case Bank | Immo | Corp  | FSP
  BCP_00035 = 'BCP-00035',
  BCP_00036 = 'BCP-00036',
  BCP_00037 = 'BCP-00037',
  BCP_00038 = 'BCP-00038',
  BCP_00039 = 'BCP-00039',
  BCP_00040 = 'BCP-00040',
  BCP_00041 = 'BCP-00041',
  BCP_00042 = 'BCP-00042',
  BCP_00043 = 'BCP-00043',
  BCP_00044 = 'BCP-00044',
  BCP_00045 = 'BCP-00045',
  BCP_00046 = 'BCP-00046',
  BCP_00047 = 'BCP-00047',
  BCP_00048 = 'BCP-00048',
  BCP_00049 = 'BCP-00049',
  BCP_00050 = 'BCP-00050',
  BCP_00051 = 'BCP-00051',
  BCP_00052 = 'BCP-00052',
  BCP_00053 = 'BCP-00053',
  BCP_00054 = 'BCP-00054',
  BCP_00055 = 'BCP-00055',
  BCP_00056 = 'BCP-00056',
  BCP_00057 = 'BCP-00057',
  BCP_00058 = 'BCP-00058',
  BCP_00059 = 'BCP-00059',
  BCP_00060 = 'BCP-00060', // TODO
  BCP_00061 = 'BCP-00061',
  BCP_00062 = 'BCP-00062',
  BCP_00063 = 'BCP-00063',
  BCP_00064 = 'BCP-00064',
  BCP_00065 = 'BCP-00065',
  BCP_00066 = 'BCP-00066',
  BCP_00067 = 'BCP-00067',
  BCP_00068 = 'BCP-00068',
  BCP_00069 = 'BCP-00069',
  BCP_00070 = 'BCP-00070',
  BCP_00071 = 'BCP-00071',
  BCP_00072 = 'BCP-00072',
  BCP_00073 = 'BCP-00073',
  BCP_00123 = 'BCP-00123',
  BCP_00125 = 'BCP-00125',
  BCP_00130 = 'BCP-00130',
  BCP_00131 = 'BCP-00131',
  BCP_00132 = 'BCP-00132',
  BCP_00133 = 'BCP-00133',
  BCP_00134 = 'BCP-00134',
  BCP_00138 = 'BCP-00138',
  BCP_00139 = 'BCP-00139',
  BCP_00140 = 'BCP-00140',
  BCP_00141 = 'BCP-00141',
  BCP_00142 = 'BCP-00142',
  BCP_00143 = 'BCP-00143',
  BCP_00144 = 'BCP-00144',
  BCP_00146 = 'BCP-00146',
  BCP_00147 = 'BCP-00147',
  BCP_00148 = 'BCP-00148',
  BCP_00149 = 'BCP-00149', // Manage active case statuses Bank | Immo | Corp  | FSP
  BCP_00151 = 'BCP-00151',
  BCP_00152 = 'BCP-00152',
  BCP_00155 = 'BCP-00155',
  BCP_00156 = 'BCP-00156',
  BCP_00157 = 'BCP-00157',
  BCP_00159 = 'BCP-00159',
  BCP_00160 = 'BCP-00160', // access to Management summary from Funding case
  BCP_00163 = 'BCP-00163',
  BCP_00164 = 'BCP-00164',
  BCP_00165 = 'BCP-00165', // data export for financing real estate case
  BCP_00166 = 'BCP-00166', // data export for passing real estate case
}
