export enum Permission {
  PERM_0000 = 'PERM-0000', // EMPTY STATE/PLACEHOLDER
  PERM_0001 = 'PERM-0001', // Pages.Dashboard -> (tabs) MY CASES | INVITATIONS
  PERM_0002 = 'PERM-0002', // Pages.Dashboard -> (tabs) APPLICATIONS
  PERM_0004 = 'PERM-0004', // Pages.CompanyManagement
  PERM_0005 = 'PERM-0005',
  PERM_0006 = 'PERM-0006', // Pages.UserManagement
  PERM_0007 = 'PERM-0007', // Pages.CustomerMasterData - (tabs) COLLABORATION
  PERM_0008 = 'PERM-0008', // Pages.CustomerMasterData - (tabs) ORGANIZATION DETAILS
  PERM_0009 = 'PERM-0009', // Pages.TemplatesManagement
  PERM_0010 = 'PERM-0010', // Pages.CreateBusinessCase
  PERM_0011 = 'PERM-0011', // Pages.UserSettings
  PERM_0012 = 'PERM-0012', // Pages.AppsIntegration
  PERM_0013 = 'PERM-0013', // Pages.KeyAccountManagement - (tabs) customers
  PERM_0014 = 'PERM-0014', // Pages.KeyAccountManagement - (tabs) customers - can add
  PERM_0015 = 'PERM-0015', // Pages.KeyAccountManagement - (tabs) customers - can search
  PERM_0016 = 'PERM-0016', // Pages.KeyAccountManagement - (tabs) users
  PERM_0017 = 'PERM-0017', // Pages.KeyAccountManagement - (tabs) users - can add
  PERM_0018 = 'PERM-0018', // Pages.KeyAccountManagement - (tabs) users - can search
  PERM_0019 = 'PERM-0019', // Pages.KeyAccountManagement - (tabs) usage contracts
  PERM_0020 = 'PERM-0020', // Pages.KeyAccountManagement - (tabs) usage contracts - can add
  PERM_0021 = 'PERM-0021', // Pages.KeyAccountManagement - (tabs) usage contracts - can search
  PERM_0022 = 'PERM-0022', // Pages.KeyAccountManagement - (tabs) usage contracts - can preview
  PERM_0023 = 'PERM-0023', // Pages.KeyAccountManagement - (tabs) usage contracts - can download
  PERM_0024 = 'PERM-0024', // Pages.ContractManagement - (tabs) contractManagement
  PERM_0025 = 'PERM-0025', // Pages.ContractManagement - (tabs) contracts overview
  PERM_0026 = 'PERM-0026', // NOT APPLICABLE Pages.ContractManagement - (tabs) templates
  PERM_0027 = 'PERM-0027', // NOT APPLICABLE Pages.ContractManagement - (tabs) templates
  PERM_0028 = 'PERM-0028', // NOT APPLICABLE Pages.ContractManagement - (tabs) manage templates
  PERM_0029 = 'PERM-0029', // NOT APPLICABLE Pages.ContractManagement - (tabs) create templates
  PERM_0030 = 'PERM-0030', // NOT APPLICABLE Pages.ContractManagement - (tabs) archive templates
  PERM_0031 = 'PERM-0031', // NOT APPLICABLE Pages.ContractManagement - (tabs) duplicate templates
  PERM_0032 = 'PERM-0032', // Pages.SignatureBoard
  PERM_0033 = 'PERM-0033', // Pages.SignatureBoard
  PERM_0034 = 'PERM-0034', // Pages.SignatureBoard - review and sign
  PERM_0035 = 'PERM-0035', // Pages.SignatureBoard - preview
  PERM_0036 = 'PERM-0036', // Pages.SignatureBoard - download
  PERM_0037 = 'PERM-0037', // Company contact person dashboard
  PERM_0040 = 'PERM-0040', // Pages.Officers
  PERM_0041 = 'PERM-0041', // srv-notification - Receive email on case invitation
  PERM_0042 = 'PERM-0042', // Pages.BusinessCase - can access
  PERM_0043 = 'PERM-0043', // CADR - share
  PERM_0044 = 'PERM-0044', // CADR - edit
  PERM_0045 = 'PERM-0045', // Copy case
  PERM_0046 = 'PERM-0046', // Create financing case,
  PERM_0048 = 'PERM-0048', // Pages.Dashboard -> (tabs) BANK/ORGANIZATION (my cases filter button)
  PERM_0049 = 'PERM-0049', // Copy case PLATFORM_MANAGER
  PERM_0050 = 'PERM-0050', // Pages.BusinessCase - can access - PLATFORM_MANAGER
  PERM_0051 = 'PERM-0051', // Add my user account as participant to case
  PERM_0052 = 'PERM-0052', // Contracts - search/view/filter my organization
  PERM_0053 = 'PERM-0053', // Contracts - create/add signer/upload pdf/set placeholders
  PERM_0054 = 'PERM-0054', // Contracts - void/preview/signing status in organisation
  PERM_0055 = 'PERM-0055', // Contacts - download
  PERM_0056 = 'PERM-0056', // Contacts - void/preview/signing status created by me
  PERM_0057 = 'PERM-0057', // Contracts - download created by me
  PERM_0058 = 'PERM-0058', // Access to the details view of Corporate clients / Companies
  PERM_0059 = 'PERM-0059', // Access to the new dashboard page
  PERM_0062 = 'PERM-0062', // View notifications
  PERM_0063 = 'PERM-0063', // Access to the Security tab in Organisation data
  PERM_0064 = 'PERM-0064', //  Pages.CustomerMasterData - (tabs) KPI SETTINGS
}
