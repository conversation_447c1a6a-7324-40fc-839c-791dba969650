import { Folder } from '@fincloud/swagger-generator/business-case-manager';
import {
  BusinessCaseGroup,
  TemplateField,
} from '@fincloud/swagger-generator/exchange';
import { DataRoomGroupTemplateName } from '@fincloud/types/enums';
import { DocumentClassification } from './document-classification';

export type InboxDataRoomEntity = (
  | Folder
  | TemplateField
  | BusinessCaseGroup
) & {
  templateName?: DataRoomGroupTemplateName;
  children?: InboxDataRoomEntity[];
  rootFolder?: Folder;
  isRepresentation?: boolean;
  id?: string;
  key?: string;
  label?: string;
  value?: string;
  documentClassifications?: DocumentClassification[];
};
