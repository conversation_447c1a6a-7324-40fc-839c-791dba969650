import {
  BusinessCaseGroup,
  InformationRecord,
} from '@fincloud/swagger-generator/exchange';
import {
  DocumentPreviewContentType,
  SelectedInboxFileSource,
} from '@fincloud/types/enums';
import { BusinessCaseInboxDocument } from '../business-case-inbox-document';

export interface BusinessCaseInboxState {
  forwardingEmail: string;
  documents: BusinessCaseInboxDocument[];
  temporaryFiles: BusinessCaseInboxDocument[];
  isForwardingEmailCopied: boolean;
  selectedDocumentIds: string[];
  searchTerm: string;
  isLoading: boolean;
  showDocumentPreview: boolean;
  sortDirection: 'asc' | 'desc';
  openDocumentOnPage: number;
  selectedHighlightDocument: BusinessCaseInboxDocument;
  selectedInboxFileSource: SelectedInboxFileSource;
  isEditingDocument: boolean;
  dataRoomHash: string;
  updateSnapShotsAllowed: boolean;
  isSingleMove: boolean;
  isPdfExpanded: boolean;
  businessCaseGroupsOrderedSnapShot: BusinessCaseGroup[];
  businessCaseInformationSnapShot: InformationRecord;
  showApplyButtonLoader: boolean;
  documentPreviewData: {
    blob: Blob;
    documentType: DocumentPreviewContentType;
  };
}
