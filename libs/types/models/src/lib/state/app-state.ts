import { DocumentEntity } from '@fincloud/swagger-generator/document';
import { UsersState } from '../users-state';
import { AccountManagementState } from './account-management-state';
import { ApplicationsState } from './applications-state';
import { ContractsState } from './contracts-state';
import { CustomerState } from './customer-state';
import { EnvironmentState } from './environment-state';
import { GroupIntoViewState } from './group-into-view-state';
import { InvitationState } from './invitation-state';
import { NotificationSystemState } from './notification-system-state';
import { UserOnboardingState } from './user-onboarding-state';
import { UserSettingsState } from './user-settings-state';
import { UserState } from './user-state';

export interface AppState {
  userSettings: UserSettingsState;
  user: UserState;
  users: UsersState;
  customers: CustomerState;
  invitations: InvitationState;
  applications: ApplicationsState;
  inboxDocuments: DocumentEntity[];
  groupIntoView: GroupIntoViewState;
  userOnboarding: UserOnboardingState;
  accountManagement: AccountManagementState;
  contracts: ContractsState;
  systemNotifications: NotificationSystemState;
  environment: EnvironmentState;
}
