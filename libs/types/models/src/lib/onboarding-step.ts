import { IconName } from './icon-name';
import { OnboardingStepKeyPoints } from './onboarding-step-key-points';

export interface OnboardingStep {
  stepName: string;
  header: { title: string; subTitle: string };
  content?: Array<{ title: string; text: string; svgName?: IconName }>;
  imageSrc?: string;
  svgName?: IconName;
  nextStepName?: string;
  nextStepButtonLabel: string;
  isFinal?: boolean;
  cssClass?: string;
  mediaClass?: string;
  largerButton?: boolean;
  keyPoints?: OnboardingStepKeyPoints[];
}
