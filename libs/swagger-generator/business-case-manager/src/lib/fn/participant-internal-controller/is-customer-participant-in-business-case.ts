/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import { filter, map } from 'rxjs/operators';
import { StrictHttpResponse } from '../../strict-http-response';
import { RequestBuilder } from '../../request-builder';

import { IsParticipantResponse } from '../../models/is-participant-response';

export interface IsCustomerParticipantInBusinessCase$Params {
  businessCaseId: string;
  customerKey: string;
}

export function isCustomerParticipantInBusinessCase(http: HttpClient, rootUrl: string, params: IsCustomerParticipantInBusinessCase$Params, context?: HttpContext): Observable<StrictHttpResponse<IsParticipantResponse>> {
  const rb = new RequestBuilder(rootUrl, isCustomerParticipantInBusinessCase.PATH, 'get');
  if (params) {
    rb.path('businessCaseId', params.businessCaseId, {});
    rb.path('customerKey', params.customerKey, {});
  }

  return http.request(
    rb.build({ responseType: 'json', accept: 'application/json', context })
  ).pipe(
    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),
    map((r: HttpResponse<any>) => {
      return r as StrictHttpResponse<IsParticipantResponse>;
    })
  );
}

isCustomerParticipantInBusinessCase.PATH = '/internal/business-case/{businessCaseId}/is-customer-participant/{customerKey}';
