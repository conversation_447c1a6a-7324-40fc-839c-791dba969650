/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import { filter, map } from 'rxjs/operators';
import { StrictHttpResponse } from '../../strict-http-response';
import { RequestBuilder } from '../../request-builder';


export interface ReplaceSpecialCharactersInInformationsAndRevisions$Params {
  securityCode: string;
}

export function replaceSpecialCharactersInInformationsAndRevisions(http: HttpClient, rootUrl: string, params: ReplaceSpecialCharactersInInformationsAndRevisions$Params, context?: HttpContext): Observable<StrictHttpResponse<void>> {
  const rb = new RequestBuilder(rootUrl, replaceSpecialCharactersInInformationsAndRevisions.PATH, 'put');
  if (params) {
    rb.query('securityCode', params.securityCode, {});
  }

  return http.request(
    rb.build({ responseType: 'text', accept: '*/*', context })
  ).pipe(
    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),
    map((r: HttpResponse<any>) => {
      return (r as HttpResponse<any>).clone({ body: undefined }) as StrictHttpResponse<void>;
    })
  );
}

replaceSpecialCharactersInInformationsAndRevisions.PATH = '/migration/template/add-folder-structure/replace-special-characters-informations-and-revisions';
