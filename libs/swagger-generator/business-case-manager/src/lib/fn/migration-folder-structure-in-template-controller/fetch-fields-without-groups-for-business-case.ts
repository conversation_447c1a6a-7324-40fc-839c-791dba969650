/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import { filter, map } from 'rxjs/operators';
import { StrictHttpResponse } from '../../strict-http-response';
import { RequestBuilder } from '../../request-builder';

import { MigrationResult } from '../../models/migration-result';

export interface FetchFieldsWithoutGroupsForBusinessCase$Params {
  securityCode: string;
}

export function fetchFieldsWithoutGroupsForBusinessCase(http: HttpClient, rootUrl: string, params: FetchFieldsWithoutGroupsForBusinessCase$Params, context?: HttpContext): Observable<StrictHttpResponse<MigrationResult>> {
  const rb = new RequestBuilder(rootUrl, fetchFieldsWithoutGroupsForBusinessCase.PATH, 'get');
  if (params) {
    rb.query('securityCode', params.securityCode, {});
  }

  return http.request(
    rb.build({ responseType: 'json', accept: 'application/json', context })
  ).pipe(
    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),
    map((r: HttpResponse<any>) => {
      return r as StrictHttpResponse<MigrationResult>;
    })
  );
}

fetchFieldsWithoutGroupsForBusinessCase.PATH = '/migration/template/add-folder-structure/business-case-fetch-fields-without-groups';
