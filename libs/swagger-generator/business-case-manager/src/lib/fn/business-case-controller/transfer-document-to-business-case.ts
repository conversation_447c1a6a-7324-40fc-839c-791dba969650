/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import { filter, map } from 'rxjs/operators';
import { StrictHttpResponse } from '../../strict-http-response';
import { RequestBuilder } from '../../request-builder';

import { DocumentTransferRequest } from '../../models/document-transfer-request';

export interface TransferDocumentToBusinessCase$Params {
  businessCaseId: string;
      body: DocumentTransferRequest
}

export function transferDocumentToBusinessCase(http: HttpClient, rootUrl: string, params: TransferDocumentToBusinessCase$Params, context?: HttpContext): Observable<StrictHttpResponse<void>> {
  const rb = new RequestBuilder(rootUrl, transferDocumentToBusinessCase.PATH, 'post');
  if (params) {
    rb.path('businessCaseId', params.businessCaseId, {});
    rb.body(params.body, 'application/json');
  }

  return http.request(
    rb.build({ responseType: 'text', accept: '*/*', context })
  ).pipe(
    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),
    map((r: HttpResponse<any>) => {
      return (r as HttpResponse<any>).clone({ body: undefined }) as StrictHttpResponse<void>;
    })
  );
}

transferDocumentToBusinessCase.PATH = '/business-case/{businessCaseId}/documents/transfer';
