/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import { filter, map } from 'rxjs/operators';
import { StrictHttpResponse } from '../../strict-http-response';
import { RequestBuilder } from '../../request-builder';

import { DocumentClassificationResponse } from '../../models/document-classification-response';

export interface GetDocumentClassifications$Params {
  businessCaseId: string;
      body: Array<string>
}

export function getDocumentClassifications(http: HttpClient, rootUrl: string, params: GetDocumentClassifications$Params, context?: HttpContext): Observable<StrictHttpResponse<DocumentClassificationResponse>> {
  const rb = new RequestBuilder(rootUrl, getDocumentClassifications.PATH, 'post');
  if (params) {
    rb.query('businessCaseId', params.businessCaseId, {});
    rb.body(params.body, 'application/json');
  }

  return http.request(
    rb.build({ responseType: 'json', accept: 'application/json', context })
  ).pipe(
    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),
    map((r: HttpResponse<any>) => {
      return r as StrictHttpResponse<DocumentClassificationResponse>;
    })
  );
}

getDocumentClassifications.PATH = '/document-classification/classify-documents';
