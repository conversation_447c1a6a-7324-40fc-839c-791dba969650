/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import { filter, map } from 'rxjs/operators';
import { StrictHttpResponse } from '../../strict-http-response';
import { RequestBuilder } from '../../request-builder';

import { DocumentClassificationDto } from '../../models/document-classification-dto';

export interface GetDocumentClassification$Params {
  businessCaseId: string;
  documentId: string;
  dataRoomHash: string;
}

export function getDocumentClassification(http: HttpClient, rootUrl: string, params: GetDocumentClassification$Params, context?: HttpContext): Observable<StrictHttpResponse<DocumentClassificationDto>> {
  const rb = new RequestBuilder(rootUrl, getDocumentClassification.PATH, 'get');
  if (params) {
    rb.query('businessCaseId', params.businessCaseId, {});
    rb.query('documentId', params.documentId, {});
    rb.query('dataRoomHash', params.dataRoomHash, {});
  }

  return http.request(
    rb.build({ responseType: 'json', accept: 'application/json', context })
  ).pipe(
    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),
    map((r: HttpResponse<any>) => {
      return r as StrictHttpResponse<DocumentClassificationDto>;
    })
  );
}

getDocumentClassification.PATH = '/document-classification/classify-document';
