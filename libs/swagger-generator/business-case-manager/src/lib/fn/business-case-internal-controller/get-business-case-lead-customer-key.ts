/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import { filter, map } from 'rxjs/operators';
import { StrictHttpResponse } from '../../strict-http-response';
import { RequestBuilder } from '../../request-builder';


export interface GetBusinessCaseLeadCustomerKey$Params {
  businessCaseId: string;
}

export function getBusinessCaseLeadCustomerKey(http: HttpClient, rootUrl: string, params: GetBusinessCaseLeadCustomerKey$Params, context?: HttpContext): Observable<StrictHttpResponse<string>> {
  const rb = new RequestBuilder(rootUrl, getBusinessCaseLeadCustomerKey.PATH, 'get');
  if (params) {
    rb.path('businessCaseId', params.businessCaseId, {});
  }

  return http.request(
    rb.build({ responseType: 'json', accept: 'application/json', context })
  ).pipe(
    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),
    map((r: HttpResponse<any>) => {
      return r as StrictHttpResponse<string>;
    })
  );
}

getBusinessCaseLeadCustomerKey.PATH = '/internal/business-case/lead-customer-key/{businessCaseId}';
