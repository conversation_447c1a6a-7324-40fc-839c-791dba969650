/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { BaseService } from '../base-service';
import { ApiConfiguration } from '../api-configuration';
import { StrictHttpResponse } from '../strict-http-response';

import { addFieldToBusinessCase } from '../fn/business-case-controller/add-field-to-business-case';
import { AddFieldToBusinessCase$Params } from '../fn/business-case-controller/add-field-to-business-case';
import { addFieldValueForBusinessCase } from '../fn/business-case-controller/add-field-value-for-business-case';
import { AddFieldValueForBusinessCase$Params } from '../fn/business-case-controller/add-field-value-for-business-case';
import { BusinessCase } from '../models/business-case';
import { BusinessCaseResponse } from '../models/business-case-response';
import { BusinessCaseStateInformation } from '../models/business-case-state-information';
import { checkParticipationPerCompany } from '../fn/business-case-controller/check-participation-per-company';
import { CheckParticipationPerCompany$Params } from '../fn/business-case-controller/check-participation-per-company';
import { createBusinessCase } from '../fn/business-case-controller/create-business-case';
import { CreateBusinessCase$Params } from '../fn/business-case-controller/create-business-case';
import { CurrentLeadResponse } from '../models/current-lead-response';
import { deleteField } from '../fn/business-case-controller/delete-field';
import { DeleteField$Params } from '../fn/business-case-controller/delete-field';
import { deleteFieldByKey } from '../fn/business-case-controller/delete-field-by-key';
import { DeleteFieldByKey$Params } from '../fn/business-case-controller/delete-field-by-key';
import { editAndMergeBusinessCaseGroups } from '../fn/business-case-controller/edit-and-merge-business-case-groups';
import { EditAndMergeBusinessCaseGroups$Params } from '../fn/business-case-controller/edit-and-merge-business-case-groups';
import { editBusinessCaseField } from '../fn/business-case-controller/edit-business-case-field';
import { EditBusinessCaseField$Params } from '../fn/business-case-controller/edit-business-case-field';
import { editBusinessCaseGroups } from '../fn/business-case-controller/edit-business-case-groups';
import { EditBusinessCaseGroups$Params } from '../fn/business-case-controller/edit-business-case-groups';
import { FieldDto } from '../models/field-dto';
import { getBusinessCaseById } from '../fn/business-case-controller/get-business-case-by-id';
import { GetBusinessCaseById$Params } from '../fn/business-case-controller/get-business-case-by-id';
import { getBusinessCasesByCompanyId } from '../fn/business-case-controller/get-business-cases-by-company-id';
import { GetBusinessCasesByCompanyId$Params } from '../fn/business-case-controller/get-business-cases-by-company-id';
import { getClosedLeaderLeaderCaseStatePerceptions } from '../fn/business-case-controller/get-closed-leader-leader-case-state-perceptions';
import { GetClosedLeaderLeaderCaseStatePerceptions$Params } from '../fn/business-case-controller/get-closed-leader-leader-case-state-perceptions';
import { getLeaderLeaderCaseStatePerceptions } from '../fn/business-case-controller/get-leader-leader-case-state-perceptions';
import { GetLeaderLeaderCaseStatePerceptions$Params } from '../fn/business-case-controller/get-leader-leader-case-state-perceptions';
import { getMasterCaseLeader } from '../fn/business-case-controller/get-master-case-leader';
import { GetMasterCaseLeader$Params } from '../fn/business-case-controller/get-master-case-leader';
import { getMyBusinessCases } from '../fn/business-case-controller/get-my-business-cases';
import { GetMyBusinessCases$Params } from '../fn/business-case-controller/get-my-business-cases';
import { getOtherBusinessCasesForCompany } from '../fn/business-case-controller/get-other-business-cases-for-company';
import { GetOtherBusinessCasesForCompany$Params } from '../fn/business-case-controller/get-other-business-cases-for-company';
import { getStateInformation } from '../fn/business-case-controller/get-state-information';
import { GetStateInformation$Params } from '../fn/business-case-controller/get-state-information';
import { getSubCaseForMasterBusinessCaseId } from '../fn/business-case-controller/get-sub-case-for-master-business-case-id';
import { GetSubCaseForMasterBusinessCaseId$Params } from '../fn/business-case-controller/get-sub-case-for-master-business-case-id';
import { getUserParticipationInBusinessCases } from '../fn/business-case-controller/get-user-participation-in-business-cases';
import { GetUserParticipationInBusinessCases$Params } from '../fn/business-case-controller/get-user-participation-in-business-cases';
import { Information } from '../models/information';
import { linkCadr } from '../fn/business-case-controller/link-cadr';
import { LinkCadr$Params } from '../fn/business-case-controller/link-cadr';
import { participantParticipantCaseStatePerception } from '../fn/business-case-controller/participant-participant-case-state-perception';
import { ParticipantParticipantCaseStatePerception$Params } from '../fn/business-case-controller/participant-participant-case-state-perception';
import { participantStructurerCaseStatePerception } from '../fn/business-case-controller/participant-structurer-case-state-perception';
import { ParticipantStructurerCaseStatePerception$Params } from '../fn/business-case-controller/participant-structurer-case-state-perception';
import { reSynchronizeInformationEntry } from '../fn/business-case-controller/re-synchronize-information-entry';
import { ReSynchronizeInformationEntry$Params } from '../fn/business-case-controller/re-synchronize-information-entry';
import { reSynchronizeSubcase } from '../fn/business-case-controller/re-synchronize-subcase';
import { ReSynchronizeSubcase$Params } from '../fn/business-case-controller/re-synchronize-subcase';
import { setState } from '../fn/business-case-controller/set-state';
import { SetState$Params } from '../fn/business-case-controller/set-state';
import { transferDocumentToBusinessCase } from '../fn/business-case-controller/transfer-document-to-business-case';
import { TransferDocumentToBusinessCase$Params } from '../fn/business-case-controller/transfer-document-to-business-case';
import { updateBusinessCaseTemplateName } from '../fn/business-case-controller/update-business-case-template-name';
import { UpdateBusinessCaseTemplateName$Params } from '../fn/business-case-controller/update-business-case-template-name';
import { updateMinMaxParticipationAmount } from '../fn/business-case-controller/update-min-max-participation-amount';
import { UpdateMinMaxParticipationAmount$Params } from '../fn/business-case-controller/update-min-max-participation-amount';
import { UserParticipationInBusinessCaseResponse } from '../models/user-participation-in-business-case-response';

@Injectable({ providedIn: 'root' })
export class BusinessCaseControllerService extends BaseService {
  constructor(config: ApiConfiguration, http: HttpClient) {
    super(config, http);
  }

  /** Path part for operation `addFieldValueForBusinessCase()` */
  static readonly AddFieldValueForBusinessCasePath = '/business-case/{businessCaseId}/{fieldKey}/add-value';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `addFieldValueForBusinessCase()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  addFieldValueForBusinessCase$Response(params: AddFieldValueForBusinessCase$Params, context?: HttpContext): Observable<StrictHttpResponse<BusinessCase>> {
    return addFieldValueForBusinessCase(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `addFieldValueForBusinessCase$Response()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  addFieldValueForBusinessCase(params: AddFieldValueForBusinessCase$Params, context?: HttpContext): Observable<BusinessCase> {
    return this.addFieldValueForBusinessCase$Response(params, context).pipe(
      map((r: StrictHttpResponse<BusinessCase>): BusinessCase => r.body)
    );
  }

  /** Path part for operation `updateBusinessCaseTemplateName()` */
  static readonly UpdateBusinessCaseTemplateNamePath = '/business-case/{businessCaseId}/update-template-name';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `updateBusinessCaseTemplateName()` instead.
   *
   * This method doesn't expect any request body.
   */
  updateBusinessCaseTemplateName$Response(params: UpdateBusinessCaseTemplateName$Params, context?: HttpContext): Observable<StrictHttpResponse<BusinessCase>> {
    return updateBusinessCaseTemplateName(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `updateBusinessCaseTemplateName$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  updateBusinessCaseTemplateName(params: UpdateBusinessCaseTemplateName$Params, context?: HttpContext): Observable<BusinessCase> {
    return this.updateBusinessCaseTemplateName$Response(params, context).pipe(
      map((r: StrictHttpResponse<BusinessCase>): BusinessCase => r.body)
    );
  }

  /** Path part for operation `updateMinMaxParticipationAmount()` */
  static readonly UpdateMinMaxParticipationAmountPath = '/business-case/{businessCaseId}/update-min-max-participation-amount';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `updateMinMaxParticipationAmount()` instead.
   *
   * This method doesn't expect any request body.
   */
  updateMinMaxParticipationAmount$Response(params: UpdateMinMaxParticipationAmount$Params, context?: HttpContext): Observable<StrictHttpResponse<void>> {
    return updateMinMaxParticipationAmount(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `updateMinMaxParticipationAmount$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  updateMinMaxParticipationAmount(params: UpdateMinMaxParticipationAmount$Params, context?: HttpContext): Observable<void> {
    return this.updateMinMaxParticipationAmount$Response(params, context).pipe(
      map((r: StrictHttpResponse<void>): void => r.body)
    );
  }

  /** Path part for operation `linkCadr()` */
  static readonly LinkCadrPath = '/business-case/{businessCaseId}/link-cadr';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `linkCadr()` instead.
   *
   * This method doesn't expect any request body.
   */
  linkCadr$Response(params: LinkCadr$Params, context?: HttpContext): Observable<StrictHttpResponse<BusinessCase>> {
    return linkCadr(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `linkCadr$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  linkCadr(params: LinkCadr$Params, context?: HttpContext): Observable<BusinessCase> {
    return this.linkCadr$Response(params, context).pipe(
      map((r: StrictHttpResponse<BusinessCase>): BusinessCase => r.body)
    );
  }

  /** Path part for operation `editBusinessCaseGroups()` */
  static readonly EditBusinessCaseGroupsPath = '/business-case/{businessCaseId}/edit-groups';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `editBusinessCaseGroups()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  editBusinessCaseGroups$Response(params: EditBusinessCaseGroups$Params, context?: HttpContext): Observable<StrictHttpResponse<BusinessCase>> {
    return editBusinessCaseGroups(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `editBusinessCaseGroups$Response()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  editBusinessCaseGroups(params: EditBusinessCaseGroups$Params, context?: HttpContext): Observable<BusinessCase> {
    return this.editBusinessCaseGroups$Response(params, context).pipe(
      map((r: StrictHttpResponse<BusinessCase>): BusinessCase => r.body)
    );
  }

  /** Path part for operation `editBusinessCaseField()` */
  static readonly EditBusinessCaseFieldPath = '/business-case/{businessCaseId}/edit-field/{fieldKey}';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `editBusinessCaseField()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  editBusinessCaseField$Response(params: EditBusinessCaseField$Params, context?: HttpContext): Observable<StrictHttpResponse<FieldDto>> {
    return editBusinessCaseField(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `editBusinessCaseField$Response()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  editBusinessCaseField(params: EditBusinessCaseField$Params, context?: HttpContext): Observable<FieldDto> {
    return this.editBusinessCaseField$Response(params, context).pipe(
      map((r: StrictHttpResponse<FieldDto>): FieldDto => r.body)
    );
  }

  /** Path part for operation `addFieldToBusinessCase()` */
  static readonly AddFieldToBusinessCasePath = '/business-case/{businessCaseId}/add-field';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `addFieldToBusinessCase()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  addFieldToBusinessCase$Response(params: AddFieldToBusinessCase$Params, context?: HttpContext): Observable<StrictHttpResponse<BusinessCase>> {
    return addFieldToBusinessCase(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `addFieldToBusinessCase$Response()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  addFieldToBusinessCase(params: AddFieldToBusinessCase$Params, context?: HttpContext): Observable<BusinessCase> {
    return this.addFieldToBusinessCase$Response(params, context).pipe(
      map((r: StrictHttpResponse<BusinessCase>): BusinessCase => r.body)
    );
  }

  /** Path part for operation `createBusinessCase()` */
  static readonly CreateBusinessCasePath = '/business-case';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `createBusinessCase()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  createBusinessCase$Response(params: CreateBusinessCase$Params, context?: HttpContext): Observable<StrictHttpResponse<BusinessCaseResponse>> {
    return createBusinessCase(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `createBusinessCase$Response()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  createBusinessCase(params: CreateBusinessCase$Params, context?: HttpContext): Observable<BusinessCaseResponse> {
    return this.createBusinessCase$Response(params, context).pipe(
      map((r: StrictHttpResponse<BusinessCaseResponse>): BusinessCaseResponse => r.body)
    );
  }

  /** Path part for operation `getBusinessCaseById()` */
  static readonly GetBusinessCaseByIdPath = '/business-case/{businessCaseId}';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getBusinessCaseById()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  getBusinessCaseById$Response(params: GetBusinessCaseById$Params, context?: HttpContext): Observable<StrictHttpResponse<BusinessCase>> {
    return getBusinessCaseById(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getBusinessCaseById$Response()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  getBusinessCaseById(params: GetBusinessCaseById$Params, context?: HttpContext): Observable<BusinessCase> {
    return this.getBusinessCaseById$Response(params, context).pipe(
      map((r: StrictHttpResponse<BusinessCase>): BusinessCase => r.body)
    );
  }

  /** Path part for operation `setState()` */
  static readonly SetStatePath = '/business-case/{businessCaseId}/state/transfer/{stateTransfer}';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `setState()` instead.
   *
   * This method doesn't expect any request body.
   */
  setState$Response(params: SetState$Params, context?: HttpContext): Observable<StrictHttpResponse<BusinessCase>> {
    return setState(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `setState$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  setState(params: SetState$Params, context?: HttpContext): Observable<BusinessCase> {
    return this.setState$Response(params, context).pipe(
      map((r: StrictHttpResponse<BusinessCase>): BusinessCase => r.body)
    );
  }

  /** Path part for operation `reSynchronizeSubcase()` */
  static readonly ReSynchronizeSubcasePath = '/business-case/{businessCaseId}/re-synchronize';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `reSynchronizeSubcase()` instead.
   *
   * This method doesn't expect any request body.
   */
  reSynchronizeSubcase$Response(params: ReSynchronizeSubcase$Params, context?: HttpContext): Observable<StrictHttpResponse<void>> {
    return reSynchronizeSubcase(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `reSynchronizeSubcase$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  reSynchronizeSubcase(params: ReSynchronizeSubcase$Params, context?: HttpContext): Observable<void> {
    return this.reSynchronizeSubcase$Response(params, context).pipe(
      map((r: StrictHttpResponse<void>): void => r.body)
    );
  }

  /** Path part for operation `reSynchronizeInformationEntry()` */
  static readonly ReSynchronizeInformationEntryPath = '/business-case/{businessCaseId}/re-synchronize-information-entry/{informationKey}';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `reSynchronizeInformationEntry()` instead.
   *
   * This method doesn't expect any request body.
   */
  reSynchronizeInformationEntry$Response(params: ReSynchronizeInformationEntry$Params, context?: HttpContext): Observable<StrictHttpResponse<Information>> {
    return reSynchronizeInformationEntry(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `reSynchronizeInformationEntry$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  reSynchronizeInformationEntry(params: ReSynchronizeInformationEntry$Params, context?: HttpContext): Observable<Information> {
    return this.reSynchronizeInformationEntry$Response(params, context).pipe(
      map((r: StrictHttpResponse<Information>): Information => r.body)
    );
  }

  /** Path part for operation `transferDocumentToBusinessCase()` */
  static readonly TransferDocumentToBusinessCasePath = '/business-case/{businessCaseId}/documents/transfer';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `transferDocumentToBusinessCase()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  transferDocumentToBusinessCase$Response(params: TransferDocumentToBusinessCase$Params, context?: HttpContext): Observable<StrictHttpResponse<void>> {
    return transferDocumentToBusinessCase(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `transferDocumentToBusinessCase$Response()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  transferDocumentToBusinessCase(params: TransferDocumentToBusinessCase$Params, context?: HttpContext): Observable<void> {
    return this.transferDocumentToBusinessCase$Response(params, context).pipe(
      map((r: StrictHttpResponse<void>): void => r.body)
    );
  }

  /** Path part for operation `getUserParticipationInBusinessCases()` */
  static readonly GetUserParticipationInBusinessCasesPath = '/business-case/participation/{userId}';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getUserParticipationInBusinessCases()` instead.
   *
   * This method doesn't expect any request body.
   */
  getUserParticipationInBusinessCases$Response(params: GetUserParticipationInBusinessCases$Params, context?: HttpContext): Observable<StrictHttpResponse<UserParticipationInBusinessCaseResponse>> {
    return getUserParticipationInBusinessCases(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getUserParticipationInBusinessCases$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getUserParticipationInBusinessCases(params: GetUserParticipationInBusinessCases$Params, context?: HttpContext): Observable<UserParticipationInBusinessCaseResponse> {
    return this.getUserParticipationInBusinessCases$Response(params, context).pipe(
      map((r: StrictHttpResponse<UserParticipationInBusinessCaseResponse>): UserParticipationInBusinessCaseResponse => r.body)
    );
  }

  /** Path part for operation `deleteFieldByKey()` */
  static readonly DeleteFieldByKeyPath = '/business-case/hard-delete-field/{businessCaseId}';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `deleteFieldByKey()` instead.
   *
   * This method doesn't expect any request body.
   */
  deleteFieldByKey$Response(params: DeleteFieldByKey$Params, context?: HttpContext): Observable<StrictHttpResponse<BusinessCase>> {
    return deleteFieldByKey(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `deleteFieldByKey$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  deleteFieldByKey(params: DeleteFieldByKey$Params, context?: HttpContext): Observable<BusinessCase> {
    return this.deleteFieldByKey$Response(params, context).pipe(
      map((r: StrictHttpResponse<BusinessCase>): BusinessCase => r.body)
    );
  }

  /** Path part for operation `editAndMergeBusinessCaseGroups()` */
  static readonly EditAndMergeBusinessCaseGroupsPath = '/business-case/{businessCaseId}/edit-and-merge-groups';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `editAndMergeBusinessCaseGroups()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  editAndMergeBusinessCaseGroups$Response(params: EditAndMergeBusinessCaseGroups$Params, context?: HttpContext): Observable<StrictHttpResponse<BusinessCase>> {
    return editAndMergeBusinessCaseGroups(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `editAndMergeBusinessCaseGroups$Response()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  editAndMergeBusinessCaseGroups(params: EditAndMergeBusinessCaseGroups$Params, context?: HttpContext): Observable<BusinessCase> {
    return this.editAndMergeBusinessCaseGroups$Response(params, context).pipe(
      map((r: StrictHttpResponse<BusinessCase>): BusinessCase => r.body)
    );
  }

  /** Path part for operation `getOtherBusinessCasesForCompany()` */
  static readonly GetOtherBusinessCasesForCompanyPath = '/business-case/{companyId}/other-business-cases';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getOtherBusinessCasesForCompany()` instead.
   *
   * This method doesn't expect any request body.
   */
  getOtherBusinessCasesForCompany$Response(params: GetOtherBusinessCasesForCompany$Params, context?: HttpContext): Observable<StrictHttpResponse<Array<string>>> {
    return getOtherBusinessCasesForCompany(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getOtherBusinessCasesForCompany$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getOtherBusinessCasesForCompany(params: GetOtherBusinessCasesForCompany$Params, context?: HttpContext): Observable<Array<string>> {
    return this.getOtherBusinessCasesForCompany$Response(params, context).pipe(
      map((r: StrictHttpResponse<Array<string>>): Array<string> => r.body)
    );
  }

  /** Path part for operation `getSubCaseForMasterBusinessCaseId()` */
  static readonly GetSubCaseForMasterBusinessCaseIdPath = '/business-case/{businessCaseId}/get-sub-case';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getSubCaseForMasterBusinessCaseId()` instead.
   *
   * This method doesn't expect any request body.
   */
  getSubCaseForMasterBusinessCaseId$Response(params: GetSubCaseForMasterBusinessCaseId$Params, context?: HttpContext): Observable<StrictHttpResponse<BusinessCase>> {
    return getSubCaseForMasterBusinessCaseId(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getSubCaseForMasterBusinessCaseId$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getSubCaseForMasterBusinessCaseId(params: GetSubCaseForMasterBusinessCaseId$Params, context?: HttpContext): Observable<BusinessCase> {
    return this.getSubCaseForMasterBusinessCaseId$Response(params, context).pipe(
      map((r: StrictHttpResponse<BusinessCase>): BusinessCase => r.body)
    );
  }

  /** Path part for operation `getStateInformation()` */
  static readonly GetStateInformationPath = '/business-case/{businessCaseId}/get-state-info';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getStateInformation()` instead.
   *
   * This method doesn't expect any request body.
   */
  getStateInformation$Response(params: GetStateInformation$Params, context?: HttpContext): Observable<StrictHttpResponse<BusinessCaseStateInformation>> {
    return getStateInformation(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getStateInformation$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getStateInformation(params: GetStateInformation$Params, context?: HttpContext): Observable<BusinessCaseStateInformation> {
    return this.getStateInformation$Response(params, context).pipe(
      map((r: StrictHttpResponse<BusinessCaseStateInformation>): BusinessCaseStateInformation => r.body)
    );
  }

  /** Path part for operation `participantStructurerCaseStatePerception()` */
  static readonly ParticipantStructurerCaseStatePerceptionPath = '/business-case/participant-structurer-case-state-perception';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `participantStructurerCaseStatePerception()` instead.
   *
   * This method doesn't expect any request body.
   */
  participantStructurerCaseStatePerception$Response(params?: ParticipantStructurerCaseStatePerception$Params, context?: HttpContext): Observable<StrictHttpResponse<Array<'STRUCTURING_ORDER_ISSUED' | 'TERM_SHEET_ACCEPTED' | 'FINANCING_APPROVED' | 'FULL_EVALUATION_CARRIED_OUT' | 'FINANCING_REPAID'>>> {
    return participantStructurerCaseStatePerception(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `participantStructurerCaseStatePerception$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  participantStructurerCaseStatePerception(params?: ParticipantStructurerCaseStatePerception$Params, context?: HttpContext): Observable<Array<'STRUCTURING_ORDER_ISSUED' | 'TERM_SHEET_ACCEPTED' | 'FINANCING_APPROVED' | 'FULL_EVALUATION_CARRIED_OUT' | 'FINANCING_REPAID'>> {
    return this.participantStructurerCaseStatePerception$Response(params, context).pipe(
      map((r: StrictHttpResponse<Array<'STRUCTURING_ORDER_ISSUED' | 'TERM_SHEET_ACCEPTED' | 'FINANCING_APPROVED' | 'FULL_EVALUATION_CARRIED_OUT' | 'FINANCING_REPAID'>>): Array<'STRUCTURING_ORDER_ISSUED' | 'TERM_SHEET_ACCEPTED' | 'FINANCING_APPROVED' | 'FULL_EVALUATION_CARRIED_OUT' | 'FINANCING_REPAID'> => r.body)
    );
  }

  /** Path part for operation `participantParticipantCaseStatePerception()` */
  static readonly ParticipantParticipantCaseStatePerceptionPath = '/business-case/participant-participant-case-state-perception';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `participantParticipantCaseStatePerception()` instead.
   *
   * This method doesn't expect any request body.
   */
  participantParticipantCaseStatePerception$Response(params?: ParticipantParticipantCaseStatePerception$Params, context?: HttpContext): Observable<StrictHttpResponse<Array<'FINANCING_CASE_JOINED' | 'FINANCING_APPROVED' | 'FULL_EVALUATION_CARRIED_OUT' | 'FINANCING_REPAID'>>> {
    return participantParticipantCaseStatePerception(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `participantParticipantCaseStatePerception$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  participantParticipantCaseStatePerception(params?: ParticipantParticipantCaseStatePerception$Params, context?: HttpContext): Observable<Array<'FINANCING_CASE_JOINED' | 'FINANCING_APPROVED' | 'FULL_EVALUATION_CARRIED_OUT' | 'FINANCING_REPAID'>> {
    return this.participantParticipantCaseStatePerception$Response(params, context).pipe(
      map((r: StrictHttpResponse<Array<'FINANCING_CASE_JOINED' | 'FINANCING_APPROVED' | 'FULL_EVALUATION_CARRIED_OUT' | 'FINANCING_REPAID'>>): Array<'FINANCING_CASE_JOINED' | 'FINANCING_APPROVED' | 'FULL_EVALUATION_CARRIED_OUT' | 'FINANCING_REPAID'> => r.body)
    );
  }

  /** Path part for operation `getClosedLeaderLeaderCaseStatePerceptions()` */
  static readonly GetClosedLeaderLeaderCaseStatePerceptionsPath = '/business-case/leader-leader-closed-case-state-perception';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getClosedLeaderLeaderCaseStatePerceptions()` instead.
   *
   * This method doesn't expect any request body.
   */
  getClosedLeaderLeaderCaseStatePerceptions$Response(params?: GetClosedLeaderLeaderCaseStatePerceptions$Params, context?: HttpContext): Observable<StrictHttpResponse<Array<'FINANCING_CASE_CREATED' | 'FINANCING_APPLICATION_SUBMITTED' | 'STRUCTURING_ORDER_ISSUED' | 'TERM_SHEET_ACCEPTED' | 'FINANCING_APPROVED' | 'FULL_EVALUATION_CARRIED_OUT' | 'FINANCING_REPAID' | 'OBJECT_SOLD' | 'FINANCING_REJECTED' | 'FINANCING_APPLICATION_WITHDRAWN' | 'OTHER' | 'INCORRECT_ENTRY'>>> {
    return getClosedLeaderLeaderCaseStatePerceptions(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getClosedLeaderLeaderCaseStatePerceptions$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getClosedLeaderLeaderCaseStatePerceptions(params?: GetClosedLeaderLeaderCaseStatePerceptions$Params, context?: HttpContext): Observable<Array<'FINANCING_CASE_CREATED' | 'FINANCING_APPLICATION_SUBMITTED' | 'STRUCTURING_ORDER_ISSUED' | 'TERM_SHEET_ACCEPTED' | 'FINANCING_APPROVED' | 'FULL_EVALUATION_CARRIED_OUT' | 'FINANCING_REPAID' | 'OBJECT_SOLD' | 'FINANCING_REJECTED' | 'FINANCING_APPLICATION_WITHDRAWN' | 'OTHER' | 'INCORRECT_ENTRY'>> {
    return this.getClosedLeaderLeaderCaseStatePerceptions$Response(params, context).pipe(
      map((r: StrictHttpResponse<Array<'FINANCING_CASE_CREATED' | 'FINANCING_APPLICATION_SUBMITTED' | 'STRUCTURING_ORDER_ISSUED' | 'TERM_SHEET_ACCEPTED' | 'FINANCING_APPROVED' | 'FULL_EVALUATION_CARRIED_OUT' | 'FINANCING_REPAID' | 'OBJECT_SOLD' | 'FINANCING_REJECTED' | 'FINANCING_APPLICATION_WITHDRAWN' | 'OTHER' | 'INCORRECT_ENTRY'>>): Array<'FINANCING_CASE_CREATED' | 'FINANCING_APPLICATION_SUBMITTED' | 'STRUCTURING_ORDER_ISSUED' | 'TERM_SHEET_ACCEPTED' | 'FINANCING_APPROVED' | 'FULL_EVALUATION_CARRIED_OUT' | 'FINANCING_REPAID' | 'OBJECT_SOLD' | 'FINANCING_REJECTED' | 'FINANCING_APPLICATION_WITHDRAWN' | 'OTHER' | 'INCORRECT_ENTRY'> => r.body)
    );
  }

  /** Path part for operation `getLeaderLeaderCaseStatePerceptions()` */
  static readonly GetLeaderLeaderCaseStatePerceptionsPath = '/business-case/leader-leader-case-state-perception';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getLeaderLeaderCaseStatePerceptions()` instead.
   *
   * This method doesn't expect any request body.
   */
  getLeaderLeaderCaseStatePerceptions$Response(params?: GetLeaderLeaderCaseStatePerceptions$Params, context?: HttpContext): Observable<StrictHttpResponse<Array<'FINANCING_CASE_CREATED' | 'FINANCING_APPLICATION_SUBMITTED' | 'STRUCTURING_ORDER_ISSUED' | 'TERM_SHEET_ACCEPTED' | 'FINANCING_APPROVED' | 'FULL_EVALUATION_CARRIED_OUT' | 'FINANCING_REPAID' | 'OBJECT_SOLD' | 'FINANCING_REJECTED' | 'FINANCING_APPLICATION_WITHDRAWN' | 'OTHER' | 'INCORRECT_ENTRY'>>> {
    return getLeaderLeaderCaseStatePerceptions(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getLeaderLeaderCaseStatePerceptions$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getLeaderLeaderCaseStatePerceptions(params?: GetLeaderLeaderCaseStatePerceptions$Params, context?: HttpContext): Observable<Array<'FINANCING_CASE_CREATED' | 'FINANCING_APPLICATION_SUBMITTED' | 'STRUCTURING_ORDER_ISSUED' | 'TERM_SHEET_ACCEPTED' | 'FINANCING_APPROVED' | 'FULL_EVALUATION_CARRIED_OUT' | 'FINANCING_REPAID' | 'OBJECT_SOLD' | 'FINANCING_REJECTED' | 'FINANCING_APPLICATION_WITHDRAWN' | 'OTHER' | 'INCORRECT_ENTRY'>> {
    return this.getLeaderLeaderCaseStatePerceptions$Response(params, context).pipe(
      map((r: StrictHttpResponse<Array<'FINANCING_CASE_CREATED' | 'FINANCING_APPLICATION_SUBMITTED' | 'STRUCTURING_ORDER_ISSUED' | 'TERM_SHEET_ACCEPTED' | 'FINANCING_APPROVED' | 'FULL_EVALUATION_CARRIED_OUT' | 'FINANCING_REPAID' | 'OBJECT_SOLD' | 'FINANCING_REJECTED' | 'FINANCING_APPLICATION_WITHDRAWN' | 'OTHER' | 'INCORRECT_ENTRY'>>): Array<'FINANCING_CASE_CREATED' | 'FINANCING_APPLICATION_SUBMITTED' | 'STRUCTURING_ORDER_ISSUED' | 'TERM_SHEET_ACCEPTED' | 'FINANCING_APPROVED' | 'FULL_EVALUATION_CARRIED_OUT' | 'FINANCING_REPAID' | 'OBJECT_SOLD' | 'FINANCING_REJECTED' | 'FINANCING_APPLICATION_WITHDRAWN' | 'OTHER' | 'INCORRECT_ENTRY'> => r.body)
    );
  }

  /** Path part for operation `getMasterCaseLeader()` */
  static readonly GetMasterCaseLeaderPath = '/business-case/get-master-case-leader/{masterBusinessCaseId}';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getMasterCaseLeader()` instead.
   *
   * This method doesn't expect any request body.
   */
  getMasterCaseLeader$Response(params: GetMasterCaseLeader$Params, context?: HttpContext): Observable<StrictHttpResponse<CurrentLeadResponse>> {
    return getMasterCaseLeader(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getMasterCaseLeader$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getMasterCaseLeader(params: GetMasterCaseLeader$Params, context?: HttpContext): Observable<CurrentLeadResponse> {
    return this.getMasterCaseLeader$Response(params, context).pipe(
      map((r: StrictHttpResponse<CurrentLeadResponse>): CurrentLeadResponse => r.body)
    );
  }

  /** Path part for operation `checkParticipationPerCompany()` */
  static readonly CheckParticipationPerCompanyPath = '/business-case/check-participation-per-company';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `checkParticipationPerCompany()` instead.
   *
   * This method doesn't expect any request body.
   */
  checkParticipationPerCompany$Response(params: CheckParticipationPerCompany$Params, context?: HttpContext): Observable<StrictHttpResponse<boolean>> {
    return checkParticipationPerCompany(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `checkParticipationPerCompany$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  checkParticipationPerCompany(params: CheckParticipationPerCompany$Params, context?: HttpContext): Observable<boolean> {
    return this.checkParticipationPerCompany$Response(params, context).pipe(
      map((r: StrictHttpResponse<boolean>): boolean => r.body)
    );
  }

  /** Path part for operation `getBusinessCasesByCompanyId()` */
  static readonly GetBusinessCasesByCompanyIdPath = '/business-case/all/{companyId}';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getBusinessCasesByCompanyId()` instead.
   *
   * This method doesn't expect any request body.
   */
  getBusinessCasesByCompanyId$Response(params: GetBusinessCasesByCompanyId$Params, context?: HttpContext): Observable<StrictHttpResponse<Array<string>>> {
    return getBusinessCasesByCompanyId(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getBusinessCasesByCompanyId$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getBusinessCasesByCompanyId(params: GetBusinessCasesByCompanyId$Params, context?: HttpContext): Observable<Array<string>> {
    return this.getBusinessCasesByCompanyId$Response(params, context).pipe(
      map((r: StrictHttpResponse<Array<string>>): Array<string> => r.body)
    );
  }

  /** Path part for operation `getMyBusinessCases()` */
  static readonly GetMyBusinessCasesPath = '/business-case/all-participation';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getMyBusinessCases()` instead.
   *
   * This method doesn't expect any request body.
   */
  getMyBusinessCases$Response(params?: GetMyBusinessCases$Params, context?: HttpContext): Observable<StrictHttpResponse<Array<string>>> {
    return getMyBusinessCases(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getMyBusinessCases$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getMyBusinessCases(params?: GetMyBusinessCases$Params, context?: HttpContext): Observable<Array<string>> {
    return this.getMyBusinessCases$Response(params, context).pipe(
      map((r: StrictHttpResponse<Array<string>>): Array<string> => r.body)
    );
  }

  /** Path part for operation `deleteField()` */
  static readonly DeleteFieldPath = '/business-case/{businessCaseId}/delete-field/{fieldKey}';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `deleteField()` instead.
   *
   * This method doesn't expect any request body.
   */
  deleteField$Response(params: DeleteField$Params, context?: HttpContext): Observable<StrictHttpResponse<{
}>> {
    return deleteField(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `deleteField$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  deleteField(params: DeleteField$Params, context?: HttpContext): Observable<{
}> {
    return this.deleteField$Response(params, context).pipe(
      map((r: StrictHttpResponse<{
}>): {
} => r.body)
    );
  }

}
