/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { BaseService } from '../base-service';
import { ApiConfiguration } from '../api-configuration';
import { StrictHttpResponse } from '../strict-http-response';

import { addCustomerToBusinessCaseInternal } from '../fn/participant-internal-controller/add-customer-to-business-case-internal';
import { AddCustomerToBusinessCaseInternal$Params } from '../fn/participant-internal-controller/add-customer-to-business-case-internal';
import { isCustomerParticipantInBusinessCase } from '../fn/participant-internal-controller/is-customer-participant-in-business-case';
import { IsCustomerParticipantInBusinessCase$Params } from '../fn/participant-internal-controller/is-customer-participant-in-business-case';
import { IsParticipantResponse } from '../models/is-participant-response';
import { ParticipantCustomer } from '../models/participant-customer';

@Injectable({ providedIn: 'root' })
export class ParticipantInternalControllerService extends BaseService {
  constructor(config: ApiConfiguration, http: HttpClient) {
    super(config, http);
  }

  /** Path part for operation `addCustomerToBusinessCaseInternal()` */
  static readonly AddCustomerToBusinessCaseInternalPath = '/internal/business-case/{businessCaseId}/participant-customer/{customerKey}';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `addCustomerToBusinessCaseInternal()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  addCustomerToBusinessCaseInternal$Response(params: AddCustomerToBusinessCaseInternal$Params, context?: HttpContext): Observable<StrictHttpResponse<ParticipantCustomer>> {
    return addCustomerToBusinessCaseInternal(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `addCustomerToBusinessCaseInternal$Response()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  addCustomerToBusinessCaseInternal(params: AddCustomerToBusinessCaseInternal$Params, context?: HttpContext): Observable<ParticipantCustomer> {
    return this.addCustomerToBusinessCaseInternal$Response(params, context).pipe(
      map((r: StrictHttpResponse<ParticipantCustomer>): ParticipantCustomer => r.body)
    );
  }

  /** Path part for operation `isCustomerParticipantInBusinessCase()` */
  static readonly IsCustomerParticipantInBusinessCasePath = '/internal/business-case/{businessCaseId}/is-customer-participant/{customerKey}';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `isCustomerParticipantInBusinessCase()` instead.
   *
   * This method doesn't expect any request body.
   */
  isCustomerParticipantInBusinessCase$Response(params: IsCustomerParticipantInBusinessCase$Params, context?: HttpContext): Observable<StrictHttpResponse<IsParticipantResponse>> {
    return isCustomerParticipantInBusinessCase(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `isCustomerParticipantInBusinessCase$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  isCustomerParticipantInBusinessCase(params: IsCustomerParticipantInBusinessCase$Params, context?: HttpContext): Observable<IsParticipantResponse> {
    return this.isCustomerParticipantInBusinessCase$Response(params, context).pipe(
      map((r: StrictHttpResponse<IsParticipantResponse>): IsParticipantResponse => r.body)
    );
  }

}
