/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { BaseService } from '../base-service';
import { ApiConfiguration } from '../api-configuration';
import { StrictHttpResponse } from '../strict-http-response';

import { DocumentClassificationDto } from '../models/document-classification-dto';
import { DocumentClassificationResponse } from '../models/document-classification-response';
import { getDocumentClassification } from '../fn/document-classification-controller/get-document-classification';
import { GetDocumentClassification$Params } from '../fn/document-classification-controller/get-document-classification';
import { getDocumentClassifications } from '../fn/document-classification-controller/get-document-classifications';
import { GetDocumentClassifications$Params } from '../fn/document-classification-controller/get-document-classifications';

@Injectable({ providedIn: 'root' })
export class DocumentClassificationControllerService extends BaseService {
  constructor(config: ApiConfiguration, http: HttpClient) {
    super(config, http);
  }

  /** Path part for operation `getDocumentClassifications()` */
  static readonly GetDocumentClassificationsPath = '/document-classification/classify-documents';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getDocumentClassifications()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  getDocumentClassifications$Response(params: GetDocumentClassifications$Params, context?: HttpContext): Observable<StrictHttpResponse<DocumentClassificationResponse>> {
    return getDocumentClassifications(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getDocumentClassifications$Response()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  getDocumentClassifications(params: GetDocumentClassifications$Params, context?: HttpContext): Observable<DocumentClassificationResponse> {
    return this.getDocumentClassifications$Response(params, context).pipe(
      map((r: StrictHttpResponse<DocumentClassificationResponse>): DocumentClassificationResponse => r.body)
    );
  }

  /** Path part for operation `getDocumentClassification()` */
  static readonly GetDocumentClassificationPath = '/document-classification/classify-document';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getDocumentClassification()` instead.
   *
   * This method doesn't expect any request body.
   */
  getDocumentClassification$Response(params: GetDocumentClassification$Params, context?: HttpContext): Observable<StrictHttpResponse<DocumentClassificationDto>> {
    return getDocumentClassification(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getDocumentClassification$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getDocumentClassification(params: GetDocumentClassification$Params, context?: HttpContext): Observable<DocumentClassificationDto> {
    return this.getDocumentClassification$Response(params, context).pipe(
      map((r: StrictHttpResponse<DocumentClassificationDto>): DocumentClassificationDto => r.body)
    );
  }

}
