/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { BaseService } from '../base-service';
import { ApiConfiguration } from '../api-configuration';
import { StrictHttpResponse } from '../strict-http-response';

import { checkComplexFields } from '../fn/migration-folder-structure-in-template-controller/check-complex-fields';
import { CheckComplexFields$Params } from '../fn/migration-folder-structure-in-template-controller/check-complex-fields';
import { fetchFieldsWithoutGroupsForBusinessCase } from '../fn/migration-folder-structure-in-template-controller/fetch-fields-without-groups-for-business-case';
import { FetchFieldsWithoutGroupsForBusinessCase$Params } from '../fn/migration-folder-structure-in-template-controller/fetch-fields-without-groups-for-business-case';
import { fetchFieldsWithoutGroupsForCaseTemplate } from '../fn/migration-folder-structure-in-template-controller/fetch-fields-without-groups-for-case-template';
import { FetchFieldsWithoutGroupsForCaseTemplate$Params } from '../fn/migration-folder-structure-in-template-controller/fetch-fields-without-groups-for-case-template';
import { migrateFolderStructureInBusinessCase } from '../fn/migration-folder-structure-in-template-controller/migrate-folder-structure-in-business-case';
import { MigrateFolderStructureInBusinessCase$Params } from '../fn/migration-folder-structure-in-template-controller/migrate-folder-structure-in-business-case';
import { migrateFolderStructureInTemplate } from '../fn/migration-folder-structure-in-template-controller/migrate-folder-structure-in-template';
import { MigrateFolderStructureInTemplate$Params } from '../fn/migration-folder-structure-in-template-controller/migrate-folder-structure-in-template';
import { MigrationResult } from '../models/migration-result';
import { replaceSpecialCharactersInInformationsAndRevisions } from '../fn/migration-folder-structure-in-template-controller/replace-special-characters-in-informations-and-revisions';
import { ReplaceSpecialCharactersInInformationsAndRevisions$Params } from '../fn/migration-folder-structure-in-template-controller/replace-special-characters-in-informations-and-revisions';

@Injectable({ providedIn: 'root' })
export class MigrationFolderStructureInTemplateControllerService extends BaseService {
  constructor(config: ApiConfiguration, http: HttpClient) {
    super(config, http);
  }

  /** Path part for operation `migrateFolderStructureInTemplate()` */
  static readonly MigrateFolderStructureInTemplatePath = '/migration/template/add-folder-structure/template';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `migrateFolderStructureInTemplate()` instead.
   *
   * This method doesn't expect any request body.
   */
  migrateFolderStructureInTemplate$Response(params: MigrateFolderStructureInTemplate$Params, context?: HttpContext): Observable<StrictHttpResponse<MigrationResult>> {
    return migrateFolderStructureInTemplate(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `migrateFolderStructureInTemplate$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  migrateFolderStructureInTemplate(params: MigrateFolderStructureInTemplate$Params, context?: HttpContext): Observable<MigrationResult> {
    return this.migrateFolderStructureInTemplate$Response(params, context).pipe(
      map((r: StrictHttpResponse<MigrationResult>): MigrationResult => r.body)
    );
  }

  /** Path part for operation `replaceSpecialCharactersInInformationsAndRevisions()` */
  static readonly ReplaceSpecialCharactersInInformationsAndRevisionsPath = '/migration/template/add-folder-structure/replace-special-characters-informations-and-revisions';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `replaceSpecialCharactersInInformationsAndRevisions()` instead.
   *
   * This method doesn't expect any request body.
   */
  replaceSpecialCharactersInInformationsAndRevisions$Response(params: ReplaceSpecialCharactersInInformationsAndRevisions$Params, context?: HttpContext): Observable<StrictHttpResponse<void>> {
    return replaceSpecialCharactersInInformationsAndRevisions(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `replaceSpecialCharactersInInformationsAndRevisions$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  replaceSpecialCharactersInInformationsAndRevisions(params: ReplaceSpecialCharactersInInformationsAndRevisions$Params, context?: HttpContext): Observable<void> {
    return this.replaceSpecialCharactersInInformationsAndRevisions$Response(params, context).pipe(
      map((r: StrictHttpResponse<void>): void => r.body)
    );
  }

  /** Path part for operation `migrateFolderStructureInBusinessCase()` */
  static readonly MigrateFolderStructureInBusinessCasePath = '/migration/template/add-folder-structure/business-case-template';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `migrateFolderStructureInBusinessCase()` instead.
   *
   * This method doesn't expect any request body.
   */
  migrateFolderStructureInBusinessCase$Response(params: MigrateFolderStructureInBusinessCase$Params, context?: HttpContext): Observable<StrictHttpResponse<MigrationResult>> {
    return migrateFolderStructureInBusinessCase(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `migrateFolderStructureInBusinessCase$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  migrateFolderStructureInBusinessCase(params: MigrateFolderStructureInBusinessCase$Params, context?: HttpContext): Observable<MigrationResult> {
    return this.migrateFolderStructureInBusinessCase$Response(params, context).pipe(
      map((r: StrictHttpResponse<MigrationResult>): MigrationResult => r.body)
    );
  }

  /** Path part for operation `checkComplexFields()` */
  static readonly CheckComplexFieldsPath = '/migration/template/add-folder-structure/complex-fields';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `checkComplexFields()` instead.
   *
   * This method doesn't expect any request body.
   */
  checkComplexFields$Response(params: CheckComplexFields$Params, context?: HttpContext): Observable<StrictHttpResponse<MigrationResult>> {
    return checkComplexFields(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `checkComplexFields$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  checkComplexFields(params: CheckComplexFields$Params, context?: HttpContext): Observable<MigrationResult> {
    return this.checkComplexFields$Response(params, context).pipe(
      map((r: StrictHttpResponse<MigrationResult>): MigrationResult => r.body)
    );
  }

  /** Path part for operation `fetchFieldsWithoutGroupsForCaseTemplate()` */
  static readonly FetchFieldsWithoutGroupsForCaseTemplatePath = '/migration/template/add-folder-structure/case-template-fetch-fields-without-groups';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `fetchFieldsWithoutGroupsForCaseTemplate()` instead.
   *
   * This method doesn't expect any request body.
   */
  fetchFieldsWithoutGroupsForCaseTemplate$Response(params: FetchFieldsWithoutGroupsForCaseTemplate$Params, context?: HttpContext): Observable<StrictHttpResponse<MigrationResult>> {
    return fetchFieldsWithoutGroupsForCaseTemplate(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `fetchFieldsWithoutGroupsForCaseTemplate$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  fetchFieldsWithoutGroupsForCaseTemplate(params: FetchFieldsWithoutGroupsForCaseTemplate$Params, context?: HttpContext): Observable<MigrationResult> {
    return this.fetchFieldsWithoutGroupsForCaseTemplate$Response(params, context).pipe(
      map((r: StrictHttpResponse<MigrationResult>): MigrationResult => r.body)
    );
  }

  /** Path part for operation `fetchFieldsWithoutGroupsForBusinessCase()` */
  static readonly FetchFieldsWithoutGroupsForBusinessCasePath = '/migration/template/add-folder-structure/business-case-fetch-fields-without-groups';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `fetchFieldsWithoutGroupsForBusinessCase()` instead.
   *
   * This method doesn't expect any request body.
   */
  fetchFieldsWithoutGroupsForBusinessCase$Response(params: FetchFieldsWithoutGroupsForBusinessCase$Params, context?: HttpContext): Observable<StrictHttpResponse<MigrationResult>> {
    return fetchFieldsWithoutGroupsForBusinessCase(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `fetchFieldsWithoutGroupsForBusinessCase$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  fetchFieldsWithoutGroupsForBusinessCase(params: FetchFieldsWithoutGroupsForBusinessCase$Params, context?: HttpContext): Observable<MigrationResult> {
    return this.fetchFieldsWithoutGroupsForBusinessCase$Response(params, context).pipe(
      map((r: StrictHttpResponse<MigrationResult>): MigrationResult => r.body)
    );
  }

}
