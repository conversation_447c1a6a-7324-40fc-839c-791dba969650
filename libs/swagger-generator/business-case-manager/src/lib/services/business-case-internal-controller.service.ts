/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { BaseService } from '../base-service';
import { ApiConfiguration } from '../api-configuration';
import { StrictHttpResponse } from '../strict-http-response';

import { BusinessCase } from '../models/business-case';
import { changeParticipationType } from '../fn/business-case-internal-controller/change-participation-type';
import { ChangeParticipationType$Params } from '../fn/business-case-internal-controller/change-participation-type';
import { checkParticipationPerCompanyInternal } from '../fn/business-case-internal-controller/check-participation-per-company-internal';
import { CheckParticipationPerCompanyInternal$Params } from '../fn/business-case-internal-controller/check-participation-per-company-internal';
import { createSubCaseInternal } from '../fn/business-case-internal-controller/create-sub-case-internal';
import { CreateSubCaseInternal$Params } from '../fn/business-case-internal-controller/create-sub-case-internal';
import { deleteRevisionsForUsers } from '../fn/business-case-internal-controller/delete-revisions-for-users';
import { DeleteRevisionsForUsers$Params } from '../fn/business-case-internal-controller/delete-revisions-for-users';
import { getAllBusinessCaseIdsInternal } from '../fn/business-case-internal-controller/get-all-business-case-ids-internal';
import { GetAllBusinessCaseIdsInternal$Params } from '../fn/business-case-internal-controller/get-all-business-case-ids-internal';
import { getAllBusinessCasesInternal } from '../fn/business-case-internal-controller/get-all-business-cases-internal';
import { GetAllBusinessCasesInternal$Params } from '../fn/business-case-internal-controller/get-all-business-cases-internal';
import { getBusinessCaseByIdInternal } from '../fn/business-case-internal-controller/get-business-case-by-id-internal';
import { GetBusinessCaseByIdInternal$Params } from '../fn/business-case-internal-controller/get-business-case-by-id-internal';
import { getBusinessCaseByIdInternalWithSetupAuthentication } from '../fn/business-case-internal-controller/get-business-case-by-id-internal-with-setup-authentication';
import { GetBusinessCaseByIdInternalWithSetupAuthentication$Params } from '../fn/business-case-internal-controller/get-business-case-by-id-internal-with-setup-authentication';
import { getBusinessCaseByIdPortalInternal } from '../fn/business-case-internal-controller/get-business-case-by-id-portal-internal';
import { GetBusinessCaseByIdPortalInternal$Params } from '../fn/business-case-internal-controller/get-business-case-by-id-portal-internal';
import { getBusinessCaseLeadCustomerKey } from '../fn/business-case-internal-controller/get-business-case-lead-customer-key';
import { GetBusinessCaseLeadCustomerKey$Params } from '../fn/business-case-internal-controller/get-business-case-lead-customer-key';
import { getBusinessCasesByCompanyIdInternalInternal } from '../fn/business-case-internal-controller/get-business-cases-by-company-id-internal-internal';
import { GetBusinessCasesByCompanyIdInternalInternal$Params } from '../fn/business-case-internal-controller/get-business-cases-by-company-id-internal-internal';
import { getLastModifiedPagedBusinessCases } from '../fn/business-case-internal-controller/get-last-modified-paged-business-cases';
import { GetLastModifiedPagedBusinessCases$Params } from '../fn/business-case-internal-controller/get-last-modified-paged-business-cases';
import { getOtherBusinessCasesForCompanyInternal } from '../fn/business-case-internal-controller/get-other-business-cases-for-company-internal';
import { GetOtherBusinessCasesForCompanyInternal$Params } from '../fn/business-case-internal-controller/get-other-business-cases-for-company-internal';
import { getPagedBusinessCases } from '../fn/business-case-internal-controller/get-paged-business-cases';
import { GetPagedBusinessCases$Params } from '../fn/business-case-internal-controller/get-paged-business-cases';
import { getTotalInvestmentAmountByBusinessCaseId } from '../fn/business-case-internal-controller/get-total-investment-amount-by-business-case-id';
import { GetTotalInvestmentAmountByBusinessCaseId$Params } from '../fn/business-case-internal-controller/get-total-investment-amount-by-business-case-id';
import { getUserParticipationInBusinessCasesInternal } from '../fn/business-case-internal-controller/get-user-participation-in-business-cases-internal';
import { GetUserParticipationInBusinessCasesInternal$Params } from '../fn/business-case-internal-controller/get-user-participation-in-business-cases-internal';
import { modifyCasePermissionSetsForCustomers } from '../fn/business-case-internal-controller/modify-case-permission-sets-for-customers';
import { ModifyCasePermissionSetsForCustomers$Params } from '../fn/business-case-internal-controller/modify-case-permission-sets-for-customers';
import { modifyCasePermissionSetsForCustomers2 } from '../fn/business-case-internal-controller/modify-case-permission-sets-for-customers-2';
import { ModifyCasePermissionSetsForCustomers2$Params } from '../fn/business-case-internal-controller/modify-case-permission-sets-for-customers-2';
import { PageBusinessCase } from '../models/page-business-case';
import { UserParticipationInBusinessCaseResponse } from '../models/user-participation-in-business-case-response';

@Injectable({ providedIn: 'root' })
export class BusinessCaseInternalControllerService extends BaseService {
  constructor(config: ApiConfiguration, http: HttpClient) {
    super(config, http);
  }

  /** Path part for operation `getBusinessCaseByIdInternalWithSetupAuthentication()` */
  static readonly GetBusinessCaseByIdInternalWithSetupAuthenticationPath = '/internal/business-case/{businessCaseId}/{customerKey}';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getBusinessCaseByIdInternalWithSetupAuthentication()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  getBusinessCaseByIdInternalWithSetupAuthentication$Response(params: GetBusinessCaseByIdInternalWithSetupAuthentication$Params, context?: HttpContext): Observable<StrictHttpResponse<BusinessCase>> {
    return getBusinessCaseByIdInternalWithSetupAuthentication(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getBusinessCaseByIdInternalWithSetupAuthentication$Response()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  getBusinessCaseByIdInternalWithSetupAuthentication(params: GetBusinessCaseByIdInternalWithSetupAuthentication$Params, context?: HttpContext): Observable<BusinessCase> {
    return this.getBusinessCaseByIdInternalWithSetupAuthentication$Response(params, context).pipe(
      map((r: StrictHttpResponse<BusinessCase>): BusinessCase => r.body)
    );
  }

  /** Path part for operation `createSubCaseInternal()` */
  static readonly CreateSubCaseInternalPath = '/internal/business-case/{businessCaseId}/create-subcase';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `createSubCaseInternal()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  createSubCaseInternal$Response(params: CreateSubCaseInternal$Params, context?: HttpContext): Observable<StrictHttpResponse<BusinessCase>> {
    return createSubCaseInternal(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `createSubCaseInternal$Response()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  createSubCaseInternal(params: CreateSubCaseInternal$Params, context?: HttpContext): Observable<BusinessCase> {
    return this.createSubCaseInternal$Response(params, context).pipe(
      map((r: StrictHttpResponse<BusinessCase>): BusinessCase => r.body)
    );
  }

  /** Path part for operation `changeParticipationType()` */
  static readonly ChangeParticipationTypePath = '/internal/business-case/{businessCaseId}/change-participation-type';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `changeParticipationType()` instead.
   *
   * This method doesn't expect any request body.
   */
  changeParticipationType$Response(params: ChangeParticipationType$Params, context?: HttpContext): Observable<StrictHttpResponse<void>> {
    return changeParticipationType(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `changeParticipationType$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  changeParticipationType(params: ChangeParticipationType$Params, context?: HttpContext): Observable<void> {
    return this.changeParticipationType$Response(params, context).pipe(
      map((r: StrictHttpResponse<void>): void => r.body)
    );
  }

  /** Path part for operation `getBusinessCaseByIdPortalInternal()` */
  static readonly GetBusinessCaseByIdPortalInternalPath = '/internal/business-case/portal/{businessCaseId}';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getBusinessCaseByIdPortalInternal()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  getBusinessCaseByIdPortalInternal$Response(params: GetBusinessCaseByIdPortalInternal$Params, context?: HttpContext): Observable<StrictHttpResponse<BusinessCase>> {
    return getBusinessCaseByIdPortalInternal(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getBusinessCaseByIdPortalInternal$Response()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  getBusinessCaseByIdPortalInternal(params: GetBusinessCaseByIdPortalInternal$Params, context?: HttpContext): Observable<BusinessCase> {
    return this.getBusinessCaseByIdPortalInternal$Response(params, context).pipe(
      map((r: StrictHttpResponse<BusinessCase>): BusinessCase => r.body)
    );
  }

  /** Path part for operation `getUserParticipationInBusinessCasesInternal()` */
  static readonly GetUserParticipationInBusinessCasesInternalPath = '/internal/business-case/participation/{userId}';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getUserParticipationInBusinessCasesInternal()` instead.
   *
   * This method doesn't expect any request body.
   */
  getUserParticipationInBusinessCasesInternal$Response(params: GetUserParticipationInBusinessCasesInternal$Params, context?: HttpContext): Observable<StrictHttpResponse<UserParticipationInBusinessCaseResponse>> {
    return getUserParticipationInBusinessCasesInternal(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getUserParticipationInBusinessCasesInternal$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getUserParticipationInBusinessCasesInternal(params: GetUserParticipationInBusinessCasesInternal$Params, context?: HttpContext): Observable<UserParticipationInBusinessCaseResponse> {
    return this.getUserParticipationInBusinessCasesInternal$Response(params, context).pipe(
      map((r: StrictHttpResponse<UserParticipationInBusinessCaseResponse>): UserParticipationInBusinessCaseResponse => r.body)
    );
  }

  /** Path part for operation `modifyCasePermissionSetsForCustomers()` */
  static readonly ModifyCasePermissionSetsForCustomersPath = '/internal/business-case/modify-case-permissions';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `modifyCasePermissionSetsForCustomers()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  modifyCasePermissionSetsForCustomers$Response(params: ModifyCasePermissionSetsForCustomers$Params, context?: HttpContext): Observable<StrictHttpResponse<void>> {
    return modifyCasePermissionSetsForCustomers(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `modifyCasePermissionSetsForCustomers$Response()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  modifyCasePermissionSetsForCustomers(params: ModifyCasePermissionSetsForCustomers$Params, context?: HttpContext): Observable<void> {
    return this.modifyCasePermissionSetsForCustomers$Response(params, context).pipe(
      map((r: StrictHttpResponse<void>): void => r.body)
    );
  }

  /** Path part for operation `modifyCasePermissionSetsForCustomers2()` */
  static readonly ModifyCasePermissionSetsForCustomers2Path = '/internal/business-case/modify-case-permissions-refactored';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `modifyCasePermissionSetsForCustomers2()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  modifyCasePermissionSetsForCustomers2$Response(params: ModifyCasePermissionSetsForCustomers2$Params, context?: HttpContext): Observable<StrictHttpResponse<void>> {
    return modifyCasePermissionSetsForCustomers2(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `modifyCasePermissionSetsForCustomers2$Response()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  modifyCasePermissionSetsForCustomers2(params: ModifyCasePermissionSetsForCustomers2$Params, context?: HttpContext): Observable<void> {
    return this.modifyCasePermissionSetsForCustomers2$Response(params, context).pipe(
      map((r: StrictHttpResponse<void>): void => r.body)
    );
  }

  /** Path part for operation `deleteRevisionsForUsers()` */
  static readonly DeleteRevisionsForUsersPath = '/internal/business-case/information-revisions';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `deleteRevisionsForUsers()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  deleteRevisionsForUsers$Response(params: DeleteRevisionsForUsers$Params, context?: HttpContext): Observable<StrictHttpResponse<void>> {
    return deleteRevisionsForUsers(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `deleteRevisionsForUsers$Response()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  deleteRevisionsForUsers(params: DeleteRevisionsForUsers$Params, context?: HttpContext): Observable<void> {
    return this.deleteRevisionsForUsers$Response(params, context).pipe(
      map((r: StrictHttpResponse<void>): void => r.body)
    );
  }

  /** Path part for operation `getAllBusinessCasesInternal()` */
  static readonly GetAllBusinessCasesInternalPath = '/internal/business-case';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getAllBusinessCasesInternal()` instead.
   *
   * This method doesn't expect any request body.
   */
  getAllBusinessCasesInternal$Response(params?: GetAllBusinessCasesInternal$Params, context?: HttpContext): Observable<StrictHttpResponse<Array<BusinessCase>>> {
    return getAllBusinessCasesInternal(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getAllBusinessCasesInternal$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getAllBusinessCasesInternal(params?: GetAllBusinessCasesInternal$Params, context?: HttpContext): Observable<Array<BusinessCase>> {
    return this.getAllBusinessCasesInternal$Response(params, context).pipe(
      map((r: StrictHttpResponse<Array<BusinessCase>>): Array<BusinessCase> => r.body)
    );
  }

  /** Path part for operation `getOtherBusinessCasesForCompanyInternal()` */
  static readonly GetOtherBusinessCasesForCompanyInternalPath = '/internal/business-case/{companyId}/other-business-cases';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getOtherBusinessCasesForCompanyInternal()` instead.
   *
   * This method doesn't expect any request body.
   */
  getOtherBusinessCasesForCompanyInternal$Response(params: GetOtherBusinessCasesForCompanyInternal$Params, context?: HttpContext): Observable<StrictHttpResponse<Array<string>>> {
    return getOtherBusinessCasesForCompanyInternal(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getOtherBusinessCasesForCompanyInternal$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getOtherBusinessCasesForCompanyInternal(params: GetOtherBusinessCasesForCompanyInternal$Params, context?: HttpContext): Observable<Array<string>> {
    return this.getOtherBusinessCasesForCompanyInternal$Response(params, context).pipe(
      map((r: StrictHttpResponse<Array<string>>): Array<string> => r.body)
    );
  }

  /** Path part for operation `getBusinessCaseByIdInternal()` */
  static readonly GetBusinessCaseByIdInternalPath = '/internal/business-case/{businessCaseId}';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getBusinessCaseByIdInternal()` instead.
   *
   * This method doesn't expect any request body.
   */
  getBusinessCaseByIdInternal$Response(params: GetBusinessCaseByIdInternal$Params, context?: HttpContext): Observable<StrictHttpResponse<BusinessCase>> {
    return getBusinessCaseByIdInternal(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getBusinessCaseByIdInternal$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getBusinessCaseByIdInternal(params: GetBusinessCaseByIdInternal$Params, context?: HttpContext): Observable<BusinessCase> {
    return this.getBusinessCaseByIdInternal$Response(params, context).pipe(
      map((r: StrictHttpResponse<BusinessCase>): BusinessCase => r.body)
    );
  }

  /** Path part for operation `getTotalInvestmentAmountByBusinessCaseId()` */
  static readonly GetTotalInvestmentAmountByBusinessCaseIdPath = '/internal/business-case/total-investment-amount/{businessCaseId}';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getTotalInvestmentAmountByBusinessCaseId()` instead.
   *
   * This method doesn't expect any request body.
   */
  getTotalInvestmentAmountByBusinessCaseId$Response(params: GetTotalInvestmentAmountByBusinessCaseId$Params, context?: HttpContext): Observable<StrictHttpResponse<number>> {
    return getTotalInvestmentAmountByBusinessCaseId(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getTotalInvestmentAmountByBusinessCaseId$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getTotalInvestmentAmountByBusinessCaseId(params: GetTotalInvestmentAmountByBusinessCaseId$Params, context?: HttpContext): Observable<number> {
    return this.getTotalInvestmentAmountByBusinessCaseId$Response(params, context).pipe(
      map((r: StrictHttpResponse<number>): number => r.body)
    );
  }

  /** Path part for operation `getPagedBusinessCases()` */
  static readonly GetPagedBusinessCasesPath = '/internal/business-case/paged';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getPagedBusinessCases()` instead.
   *
   * This method doesn't expect any request body.
   */
  getPagedBusinessCases$Response(params: GetPagedBusinessCases$Params, context?: HttpContext): Observable<StrictHttpResponse<PageBusinessCase>> {
    return getPagedBusinessCases(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getPagedBusinessCases$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getPagedBusinessCases(params: GetPagedBusinessCases$Params, context?: HttpContext): Observable<PageBusinessCase> {
    return this.getPagedBusinessCases$Response(params, context).pipe(
      map((r: StrictHttpResponse<PageBusinessCase>): PageBusinessCase => r.body)
    );
  }

  /** Path part for operation `getLastModifiedPagedBusinessCases()` */
  static readonly GetLastModifiedPagedBusinessCasesPath = '/internal/business-case/paged/last-modified';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getLastModifiedPagedBusinessCases()` instead.
   *
   * This method doesn't expect any request body.
   */
  getLastModifiedPagedBusinessCases$Response(params: GetLastModifiedPagedBusinessCases$Params, context?: HttpContext): Observable<StrictHttpResponse<PageBusinessCase>> {
    return getLastModifiedPagedBusinessCases(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getLastModifiedPagedBusinessCases$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getLastModifiedPagedBusinessCases(params: GetLastModifiedPagedBusinessCases$Params, context?: HttpContext): Observable<PageBusinessCase> {
    return this.getLastModifiedPagedBusinessCases$Response(params, context).pipe(
      map((r: StrictHttpResponse<PageBusinessCase>): PageBusinessCase => r.body)
    );
  }

  /** Path part for operation `getBusinessCaseLeadCustomerKey()` */
  static readonly GetBusinessCaseLeadCustomerKeyPath = '/internal/business-case/lead-customer-key/{businessCaseId}';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getBusinessCaseLeadCustomerKey()` instead.
   *
   * This method doesn't expect any request body.
   */
  getBusinessCaseLeadCustomerKey$Response(params: GetBusinessCaseLeadCustomerKey$Params, context?: HttpContext): Observable<StrictHttpResponse<string>> {
    return getBusinessCaseLeadCustomerKey(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getBusinessCaseLeadCustomerKey$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getBusinessCaseLeadCustomerKey(params: GetBusinessCaseLeadCustomerKey$Params, context?: HttpContext): Observable<string> {
    return this.getBusinessCaseLeadCustomerKey$Response(params, context).pipe(
      map((r: StrictHttpResponse<string>): string => r.body)
    );
  }

  /** Path part for operation `checkParticipationPerCompanyInternal()` */
  static readonly CheckParticipationPerCompanyInternalPath = '/internal/business-case/check-participation-per-company';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `checkParticipationPerCompanyInternal()` instead.
   *
   * This method doesn't expect any request body.
   */
  checkParticipationPerCompanyInternal$Response(params: CheckParticipationPerCompanyInternal$Params, context?: HttpContext): Observable<StrictHttpResponse<boolean>> {
    return checkParticipationPerCompanyInternal(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `checkParticipationPerCompanyInternal$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  checkParticipationPerCompanyInternal(params: CheckParticipationPerCompanyInternal$Params, context?: HttpContext): Observable<boolean> {
    return this.checkParticipationPerCompanyInternal$Response(params, context).pipe(
      map((r: StrictHttpResponse<boolean>): boolean => r.body)
    );
  }

  /** Path part for operation `getAllBusinessCaseIdsInternal()` */
  static readonly GetAllBusinessCaseIdsInternalPath = '/internal/business-case/business-case-ids';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getAllBusinessCaseIdsInternal()` instead.
   *
   * This method doesn't expect any request body.
   */
  getAllBusinessCaseIdsInternal$Response(params?: GetAllBusinessCaseIdsInternal$Params, context?: HttpContext): Observable<StrictHttpResponse<Array<string>>> {
    return getAllBusinessCaseIdsInternal(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getAllBusinessCaseIdsInternal$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getAllBusinessCaseIdsInternal(params?: GetAllBusinessCaseIdsInternal$Params, context?: HttpContext): Observable<Array<string>> {
    return this.getAllBusinessCaseIdsInternal$Response(params, context).pipe(
      map((r: StrictHttpResponse<Array<string>>): Array<string> => r.body)
    );
  }

  /** Path part for operation `getBusinessCasesByCompanyIdInternalInternal()` */
  static readonly GetBusinessCasesByCompanyIdInternalInternalPath = '/internal/business-case/all/{companyId}';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getBusinessCasesByCompanyIdInternalInternal()` instead.
   *
   * This method doesn't expect any request body.
   */
  getBusinessCasesByCompanyIdInternalInternal$Response(params: GetBusinessCasesByCompanyIdInternalInternal$Params, context?: HttpContext): Observable<StrictHttpResponse<Array<string>>> {
    return getBusinessCasesByCompanyIdInternalInternal(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getBusinessCasesByCompanyIdInternalInternal$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getBusinessCasesByCompanyIdInternalInternal(params: GetBusinessCasesByCompanyIdInternalInternal$Params, context?: HttpContext): Observable<Array<string>> {
    return this.getBusinessCasesByCompanyIdInternalInternal$Response(params, context).pipe(
      map((r: StrictHttpResponse<Array<string>>): Array<string> => r.body)
    );
  }

}
