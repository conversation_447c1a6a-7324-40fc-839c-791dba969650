/* tslint:disable */
/* eslint-disable */
import { ContainedGroup } from '../models/contained-group';
import { ContentReferenceEntity } from '../models/content-reference-entity';
import { OwnerReference } from '../models/owner-reference';
export interface DocumentEntity {
  businessCaseId?: string;
  companyId?: string;
  contentBase64?: string;
  contentReference?: ContentReferenceEntity;
  createdOn?: string;
  digest?: string;
  groupsContainedIn?: Array<ContainedGroup>;
  hasError?: boolean;
  id?: string;
  isContainedInBusinessCase?: boolean;
  isContainedInInbox?: boolean;
  isWarningIgnored?: boolean;
  ownerReference?: OwnerReference;
  realmKey?: string;
  state?:
    | 'DELETED'
    | 'TEMPORARY'
    | 'CONTENT_UPLOAD_ONGOING'
    | 'CONTENT_UPLOAD_FAILED'
    | 'FINAL';
  uploadAllowed?: boolean;
  userCustomerKey?: string;
  userId?: string;
}
