@use 'styles/src/lib/common';

:host {
  @apply tw-inline-block;
  @apply tw-min-w-[27rem];
  background-color: theme('colors.color-surface-primary');
  border-radius: 1.2rem;
}

.tip {
  padding: 0 0.9rem 0.9rem;
  color: theme('colors.color-brand-dark');
}

.tip-image {
  height: 17.5rem;
  width: 100%;
}

.tip-header {
  @include common.heading6();
  padding: 0.8rem 0;
}

.tip-text {
  @include common.heading8(500);
  padding: 0.8rem 0;

  &-title {
    @include common.heading9();
  }
}

.drag-drop-image {
  img {
    object-fit: cover;
  }
}

::ng-deep .popover .arrow {
  display: none;
}

::ng-deep popover-container {
  position: absolute;
  border-radius: 1.2rem !important;
}

::ng-deep .ngx-ui-tour_backdrop {
  background-color: rgb(var(--colors-color-brand-dark) / 0.8) !important;
}
