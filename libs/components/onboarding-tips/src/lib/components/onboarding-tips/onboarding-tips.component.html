@if (activeStep) {
  <div
    class="tw-flex tw-flex-col tw-h-full tw-items-center tw-jusify-end tw-p-[2.5rem_1.9rem_1.5rem]"
    [ngClass]="activeStep.cssClass"
  >
    <div
      class="tw-flex tw-justify-space-between tw-gap-4 tw-gap-[2.9rem]"
      [ngClass]="{ 'tw-mb-[2.8rem]': activeStep?.keyPoints?.length }"
    >
      @if (activeStep?.keyPoints?.length) {
        <div class="tw-w-[31.7rem]">
          <div class="tw-text-heading-2-strong tw-mb-[0.6rem]">
            {{ activeStep?.header?.title }}
          </div>
          <div class="tw-text-body-2-moderate tw-mb-[3.4rem]">
            {{ activeStep?.header?.subTitle }}
          </div>
          <div class="tw-flex tw-flex-col tw-gap-[4.8rem]">
            @for (keyPoint of activeStep.keyPoints; track keyPoint.iconName) {
              <div class="tw-flex tw-gap-[1.1rem] tw-items-center">
                <fin-icon
                  [ngClass]="keyPoint.class"
                  [name]="keyPoint.iconName"
                  [matIconOutlined]="!keyPoint?.fillIcon"
                ></fin-icon>
                <span class="tw-text-body-2-moderate">{{ keyPoint.text }}</span>
              </div>
            }
          </div>
        </div>
      }
      <div
        class="tw-flex tw-min-h-64"
        [ngClass]="activeStep?.mediaClass || 'tw-h-full'"
      >
        @if (activeStep?.imageSrc) {
          <img
            class="tw-grow"
            width="100"
            height="100"
            [ngSrc]="activeStep.imageSrc"
            priority
          />
        }
        @if (activeStep?.svgName) {
          <div
            class="tw-flex tw-grow tw-items-center tw-justify-center tw-rounded-full tw-h-24 tw-w-24 tw-mt-24 tw-mb-8 tw-bg-color-background-secondary-minimal"
          >
            <fin-icon [name]="activeStep.svgName"></fin-icon>
          </div>
        }
      </div>
    </div>
    <div
      class="tw-w-full tw-flex tw-flex-col tw-gap-[0.9rem]"
      [ngClass]="{ 'tw-my-[0.9rem]': !activeStep?.keyPoints?.length }"
    >
      @if (
        !activeStep?.keyPoints?.length &&
        (activeStep?.header?.title || activeStep?.header?.subTitle)
      ) {
        <div class="tw-max-w-[31rem] tw-mb-12">
          <div class="tw-text-body-2-strong tw-mb-4">
            {{ activeStep?.header?.title }}
          </div>
          <div class="tw-text-body-2-moderate">
            {{ activeStep?.header?.subTitle }}
          </div>
        </div>
      }

      @for (item of activeStep?.content; track item.title) {
        <div class="tw-flex">
          @if (item.svgName) {
            <div
              class="tw-flex tw-items-center tw-justify-center tw-bg-color-background-secondary-minimal tw-rounded-full tw-size-10 tw-mr-4"
            >
              <ui-icon
                [name]="item.svgName"
                size="medium"
                color="blue"
              ></ui-icon>
            </div>
          }
          <div class="tw-mb-20 tw-w-fit">
            <div class="tw-text-body-2-strong">{{ item.title }}</div>
            <div
              class="tw-text-body-2-moderate"
              [class.tw-text-color-text-tertiary]="!!activeStep?.svgName"
            >
              {{ item.text }}
            </div>
          </div>
        </div>
      }
    </div>

    <button
      fin-button
      [size]="activeStep?.largerButton ? finSize.L : finSize.M"
      (click)="nextStep()"
      class="tw-self-end"
    >
      {{ activeStep?.nextStepButtonLabel }}
    </button>
  </div>
}
