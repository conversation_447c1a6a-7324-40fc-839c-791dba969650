import {
  ChangeDetectionStrategy,
  Component,
  EventEmitter,
  Input,
  Output,
} from '@angular/core';
import { OnboardingStep } from '@fincloud/types/models';
import { FinSize } from '@fincloud/ui/types';

@Component({
  selector: 'ui-onboarding-tips',
  templateUrl: './onboarding-tips.component.html',
  styleUrls: ['./onboarding-tips.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class OnboardingTipsComponent {
  @Input()
  onboardingSteps: OnboardingStep[] = [];

  @Output()
  markTipsAsRead = new EventEmitter<string>();

  readonly finSize = FinSize;

  private activeStepIndex = 0;

  get activeStep() {
    return this.onboardingSteps[this.activeStepIndex];
  }

  get isFinalStep() {
    return this.onboardingSteps.length - 1 === this.activeStepIndex;
  }

  nextStep() {
    if (this.isFinalStep) {
      this.markTipsAsRead.emit(this.activeStep.stepName);
    }
    if (!this.isFinalStep) {
      this.activeStepIndex++;
    }
  }
}
