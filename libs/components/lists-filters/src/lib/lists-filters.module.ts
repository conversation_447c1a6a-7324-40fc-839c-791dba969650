import { NgxSliderModule } from '@angular-slider/ngx-slider';
import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NsUiBooleansModule } from '@fincloud/components/booleans';
import { NsUiButtonsModule } from '@fincloud/components/buttons';
import { NsUiIconsModule } from '@fincloud/components/icons';
import { NsUiSelectsModule } from '@fincloud/components/selects';
import { NsCorePipesModule } from '@fincloud/core/pipes';
import { ContractStatusFilterSelectableItemComponent } from './components/contract-status-filter-selectable-item/contract-status-filter-selectable-item.component';

@NgModule({
  declarations: [ContractStatusFilterSelectableItemComponent],
  imports: [
    CommonModule,
    NsUiIconsModule,
    ReactiveFormsModule,
    FormsModule,
    NsUiBooleansModule,
    NgxSliderModule,
    NsCorePipesModule,
    NsUiSelectsModule,
    NsUiButtonsModule,
  ],
  exports: [ContractStatusFilterSelectableItemComponent],
})
export class NsUiListsFiltersModule {}
