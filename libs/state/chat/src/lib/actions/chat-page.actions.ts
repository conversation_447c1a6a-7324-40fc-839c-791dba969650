import { Chat } from '@fincloud/swagger-generator/communication';
import { createAction, props } from '@ngrx/store';

export const addMutedChat = createAction(
  '[BusinessCase Page] Add muted chat for logged in user',
  props<{ payload: string }>(),
);

export const removeMutedChat = createAction(
  '[BusinessCase Page] Remove muted chat for logged in user',
  props<{ payload: string }>(),
);

export const updateChat = createAction(
  '[BusinessCase Page] Update chat',
  props<{ payload: Chat }>(),
);

export const refreshAllChats = createAction(
  '[BusinessCase Page] Refresh all chats',
  props<{ payload: string }>(),
);

export const addToExistingChats = createAction(
  '[BusinessCase Page] Add chat to existing chats per business case',
  props<{ payload: Chat }>(),
);

export const refreshChat = createAction(
  '[BusinessCase Page] Refresh chat',
  props<{ payload: string }>(),
);

export const prepareChat = createAction(
  '[BusinessCase Page] Prepare chat',
  props<{ payload: Chat[] }>(),
);

export const setCurrentChat = createAction(
  '[Chat Page] Set Current Chat',
  props<{
    payload: Chat;
  }>(),
);

export const setInitialSelectedChatId = createAction(
  '[Chat Page] Set Initial Selected Chat Id',
  props<{
    payload: { chatId: string };
  }>(),
);

export const initiateFileUpload = createAction(
  '[Chat Page] File Upload initiated',
  props<{
    file: File;
  }>(),
);

export const clearAllUploadedFiles = createAction(
  '[Chat Page] clear state after send message',
);

export const removeSingleFile = createAction(
  '[Chat Page] remove single file',
  props<{
    fileName: string;
  }>(),
);

export const createOrUpdateCompanyInternalChat = createAction(
  '[BusinessCase Page] Create company internal chat',
  props<{ payload: { userIds: string[]; updateOnly: boolean } }>(),
);
