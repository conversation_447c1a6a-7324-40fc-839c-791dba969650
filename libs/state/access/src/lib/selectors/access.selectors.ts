// do not change these imports
import { overlap } from '@fincloud/core/utils';
import { selectCustomer } from '@fincloud/state/customer';
import { selectIsDemoEnvironment } from '@fincloud/state/environment';
import { selectUserPermissionsCodes } from '@fincloud/state/user';
import { Customer } from '@fincloud/swagger-generator/authorization-server';
import {
  BusinessCaseType,
  CustomerStatus,
  CustomerType,
  Permission,
} from '@fincloud/types/enums';
import { createSelector } from '@ngrx/store';
import { CommonPageAccess } from '../models/common-page-access';

export const selectAllowedBusinessCaseTypes = createSelector(
  selectUserPermissionsCodes,
  (permissionCodes: string[]): BusinessCaseType[] => {
    const caseTypes = [];

    if (permissionCodes.includes(Permission.PERM_0046)) {
      caseTypes.push(BusinessCaseType.FINANCING_CASE);
    }

    return caseTypes;
  },
);

export const permissionHtml = Permission;

export const selectAllowedPages = createSelector(
  selectUserPermissionsCodes,
  selectCustomer,
  selectIsDemoEnvironment,
  (
    permissionCodes: string[],
    customer: Customer,
    isDemoEnvironment: boolean,
  ): CommonPageAccess => {
    const access = {
      Dashboard: {
        read: hasPermission(permissionCodes, [Permission.PERM_0059]),
      },
      Cases: {
        read: hasPermission(permissionCodes, [
          Permission.PERM_0001,
          Permission.PERM_0002,
        ]),
        sections: {
          myCases: {
            read: hasPermission(permissionCodes, [Permission.PERM_0001]),
          },
          myOrganization: {
            read: hasPermission(permissionCodes, [Permission.PERM_0001]),
          },
          applications: {
            read:
              hasPermission(permissionCodes, [Permission.PERM_0002]) &&
              customer.customerType === CustomerType.BANK,
          },
          invitations: {
            read: hasPermission(permissionCodes, [Permission.PERM_0001]),
          },
        },
      },
      CompanyManagement: {
        read:
          hasPermission(permissionCodes, [Permission.PERM_0004]) &&
          customer.customerStatus === CustomerStatus.REGULAR,
      },
      CompanyAnalysis: {
        read:
          hasPermission(permissionCodes, [Permission.PERM_0005]) &&
          customer.customerStatus === CustomerStatus.REGULAR,
      },
      UserManagement: {
        read:
          hasPermission(permissionCodes, [Permission.PERM_0006]) &&
          customer.customerStatus === CustomerStatus.REGULAR,
      },
      CustomerMasterData: {
        read:
          hasPermission(permissionCodes, [
            Permission.PERM_0007,
            Permission.PERM_0008,
            Permission.PERM_0063,
            Permission.PERM_0064,
          ]) && customer.customerStatus === CustomerStatus.REGULAR,
        sections: {
          collaborations: {
            read: hasPermission(permissionCodes, [Permission.PERM_0007]),
          },
          security: {
            read: hasPermission(permissionCodes, [Permission.PERM_0063]),
          },
          organizationDetails: {
            read: hasPermission(permissionCodes, [Permission.PERM_0008]),
          },
          kpiSettings: {
            read: hasPermission(permissionCodes, [Permission.PERM_0064]),
          },
        },
      },
      TemplatesManagement: {
        read:
          hasPermission(permissionCodes, [Permission.PERM_0009]) &&
          customer.customerStatus === CustomerStatus.REGULAR,
      },
      CreateBusinessCase: {
        read:
          hasPermission(permissionCodes, [Permission.PERM_0010]) &&
          customer.customerStatus === CustomerStatus.REGULAR,
      },
      KeyAccountManagement: {
        read:
          hasPermission(permissionCodes, [
            Permission.PERM_0013,
            Permission.PERM_0014,
            Permission.PERM_0015,
            Permission.PERM_0016,
            Permission.PERM_0017,
            Permission.PERM_0018,
            Permission.PERM_0019,
            Permission.PERM_0020,
            Permission.PERM_0021,
            Permission.PERM_0022,
            Permission.PERM_0023,
          ]) && customer.customerStatus === CustomerStatus.REGULAR,
        // TODO: tabs permissions
      },
      ContractManagement: {
        read:
          hasPermission(permissionCodes, [
            Permission.PERM_0024,
            Permission.PERM_0025,
          ]) && customer.customerStatus === CustomerStatus.REGULAR,
        sections: {
          contractsManagement: {
            read: hasPermission(permissionCodes, [Permission.PERM_0024]),
          },
          contractsOverview: {
            read: hasPermission(permissionCodes, [Permission.PERM_0025]),
          },
        },
      },
      SignatureBoard: {
        read: hasPermission(permissionCodes, [
          Permission.PERM_0032,
          Permission.PERM_0033,
          Permission.PERM_0034,
          Permission.PERM_0035,
          Permission.PERM_0036,
        ]),
      },
      BillingManagement: {
        read:
          hasPermission(permissionCodes, [Permission.PERM_0000]) &&
          customer.customerStatus === CustomerStatus.REGULAR,
      },
      BusinessCase: {
        read:
          hasPermission(permissionCodes, [Permission.PERM_0042]) &&
          customer.customerStatus === CustomerStatus.REGULAR,
      },
      AppsIntegration: {
        read:
          hasPermission(permissionCodes, [Permission.PERM_0012]) &&
          customer.customerStatus === CustomerStatus.REGULAR,
      },
      UserSettings: {
        read:
          hasPermission(permissionCodes, [Permission.PERM_0011]) &&
          customer.customerStatus === CustomerStatus.REGULAR,
      },
      SingleClusterDemo: {
        read:
          isDemoEnvironment && customer.customerType === CustomerType.INTERNAL,
      },
    };
    return access;
  },
);

const hasPermission = (userPermissionCodes: string[], allowedCodes: string[]) =>
  overlap(userPermissionCodes, allowedCodes);
