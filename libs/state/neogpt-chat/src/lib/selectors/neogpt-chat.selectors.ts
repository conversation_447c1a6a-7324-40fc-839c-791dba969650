import { FieldTypeEnum } from '@fincloud/core/formly';
import { fieldSourceMapToGroupIndex } from '@fincloud/core/neo-gpt';
import { fieldTypeToIconName } from '@fincloud/core/utils';
import {
  selectBusinessCaseId,
  selectBusinessCaseInformation,
  selectDocumentsFiles,
} from '@fincloud/state/business-case';
import {
  selectRawDynamicFieldsets,
  selectRawStaticGroups,
} from '@fincloud/state/business-case-real-estate';
import { selectCustomerFeatureFlags } from '@fincloud/state/customer';
import { selectOrderedBusinessCaseFields } from '@fincloud/state/data-room';
import { selectInboxDocuments } from '@fincloud/state/inbox-documents';
import { selectUser, selectUserId } from '@fincloud/state/user';
import { selectUserOnboardingGPT } from '@fincloud/state/user-onboarding';
import { BusinessCaseInformation } from '@fincloud/swagger-generator/exchange';
import { ChunkType, MessageType } from '@fincloud/swagger-generator/neo-gpt';
import {
  DocumentStatus,
  NeoGptActiveSession,
  NeoGptGroupTypeName,
} from '@fincloud/types/enums';
import {
  CustomFinStructureGroup,
  NeoGptChatDocumentSource,
  NeoGptChatFieldSource,
  NeoGptChatGroupedSources,
  NeoGptChatSourceMeta,
  NeoGptChatState,
  NeoGptSourceDocumentLabel,
  NeoGptSourceMeta,
} from '@fincloud/types/models';
import { REFS_BLOCKS_GROUP_KEY } from '@fincloud/utils';
import { createFeatureSelector, createSelector } from '@ngrx/store';
import * as dayjs from 'dayjs';
import { groupBy } from 'lodash-es';
import { buildNeogptSourceDocumentLabel } from '../utils/build-neogpt-source-document-label';

export const selectNeoGptChatState =
  createFeatureSelector<NeoGptChatState>('neoGptChat');

export const selectDocumentId = createSelector(
  selectNeoGptChatState,
  (state: NeoGptChatState) => state.documentId,
);

export const selectChatSessionId = createSelector(
  selectNeoGptChatState,
  selectUserId,
  selectBusinessCaseId,
  (state: NeoGptChatState, userId, businessCaseId) => {
    switch (state.activeSession) {
      case NeoGptActiveSession.FINANCING_DETAILS:
      case NeoGptActiveSession.DATA_ROOM:
        return `${userId}-${businessCaseId}`;
      default:
        return '';
    }
  },
);

export const selectChatInitialData = createSelector(
  selectNeoGptChatState,
  (state: NeoGptChatState) => state.documentId,
);

export const selectIsChatRatingSeen = createSelector(
  selectNeoGptChatState,
  (state: NeoGptChatState) => state.isChatRatingSeen,
);

export const selectChatSessionResponse = createSelector(
  selectNeoGptChatState,
  ({ documentStatus, chatInitialData, error }: NeoGptChatState) => {
    return {
      isNeogptMsg: true,
      documentStatus: documentStatus.chat_state,
      timestamp: Math.floor(Date.now() / 1000),
      content: {
        text: chatInitialData.summary,
        questions: chatInitialData.questions,
      },
      error,
    };
  },
);

export const selectBusinessCaseFieldsDocLabels = createSelector(
  selectBusinessCaseInformation,
  (businessCaseInformation) => {
    const documentLabels = {} as Record<string, NeoGptSourceDocumentLabel>;

    Object.values(businessCaseInformation || {}).forEach(
      (infoRecord: BusinessCaseInformation) => {
        if (infoRecord.field.fieldType === FieldTypeEnum.DOCUMENT) {
          const label = infoRecord.field.label;
          const lastDotIndex = label.lastIndexOf('.');
          const fileExt = label.substring(lastDotIndex + 1);

          documentLabels[infoRecord.value] = {
            fileExt,
            label,
          };
        }
      },
    );

    return documentLabels;
  },
);

export const selectBusinessCaseAllDocLabels = createSelector(
  selectDocumentsFiles,
  selectInboxDocuments,
  selectBusinessCaseFieldsDocLabels,
  (documentsFiles, documentInboxDocuments, businessCaseFieldsDocLabels) => {
    const documentLabels = { ...businessCaseFieldsDocLabels };
    const nonInboxDocuments = buildNeogptSourceDocumentLabel(
      documentLabels,
      documentsFiles || [],
      false,
    );
    const inboxDocuments = buildNeogptSourceDocumentLabel(
      documentLabels,
      documentInboxDocuments,
      true,
    );
    return { ...nonInboxDocuments, ...inboxDocuments };
  },
);

export const selectDataRoomFields = createSelector(
  selectBusinessCaseInformation,
  (businessCaseInformation) => {
    const fields = {} as Record<string, NeoGptChatFieldSource>;

    Object.values(businessCaseInformation || {}).forEach(
      (infoRecord: BusinessCaseInformation) => {
        if (infoRecord.field.fieldType !== FieldTypeEnum.DOCUMENT) {
          fields[infoRecord.id] =
            fields[infoRecord.id] || ({} as NeoGptChatFieldSource);
          fields[infoRecord.id].label = infoRecord.field.label;
          fields[infoRecord.id].key = infoRecord.field.key;
          fields[infoRecord.id].type = infoRecord.field.fieldType;
          fields[infoRecord.id].lastModifiedDate = infoRecord.lastModifiedDate;
          fields[infoRecord.id].value = infoRecord.value;
        }
      },
    );

    return fields;
  },
);

export const selectRawChatMessages = createSelector(
  selectNeoGptChatState,
  (state) => state.chatMessages,
);

export const selectChatMessages = createSelector(
  selectRawChatMessages,
  selectBusinessCaseAllDocLabels,
  selectDataRoomFields,
  selectOrderedBusinessCaseFields,
  selectRawStaticGroups,
  selectRawDynamicFieldsets,
  (
    chatMessages,
    documentLabels,
    dataRoomFields,
    groupsOrdered,
    staticGroups,
    dynamicGroups,
  ) => {
    return chatMessages.map((message) => {
      const documents: NeoGptChatDocumentSource[] = [];
      const fields: { key: ChunkType; values: NeoGptChatFieldSource[] }[] = [];
      const groupedSources: NeoGptChatGroupedSources = { fields };
      const updatedMessage = structuredClone(message);
      const chunkType =
        message.content.sources && message.content.sources[0]?.source_type;
      const firstSourceContext = {
        key:
          chunkType === ChunkType.DataRoomField ||
          chunkType === ChunkType.Document
            ? ChunkType.DataRoomField
            : ChunkType.FinStructureField,
        values: [] as NeoGptChatFieldSource[],
      };

      const secondSourceContext = {
        key:
          chunkType === ChunkType.FinStructureField
            ? ChunkType.DataRoomField
            : ChunkType.FinStructureField,
        values: [] as NeoGptChatFieldSource[],
      };
      fields.push(firstSourceContext, secondSourceContext);

      let dataRoomContextFields =
        fields.find(
          (sourceContext) => sourceContext.key === ChunkType.DataRoomField,
        )?.values || [];
      let finStructureContextFields =
        fields.find(
          (sourceContext) => sourceContext.key === ChunkType.FinStructureField,
        )?.values || [];

      message.content.sources?.forEach((source) => {
        const messageTimestamp = message.timestamp * 1000;
        const messageDate = new Date(messageTimestamp);
        if (source.source_type === ChunkType.Document) {
          let document = documents.find((doc) => doc.id === source.doc_id);
          const { filetype } = source.meta as NeoGptSourceMeta;
          if (!document) {
            document = {
              id: source.doc_id,
              pages: [],
              ...documentLabels[source.doc_id],
              fileExt: filetype,
              meta: source.meta as NeoGptSourceMeta,
            };
            document.pages = document.pages.sort();
            documents.push(document);
          }
          document.pages.push(source.page_n);
        }

        if (
          source.source_type === ChunkType.DataRoomField &&
          dataRoomFields[source.doc_id]
        ) {
          const fieldLastModifiedDate = new Date(
            `${dataRoomFields[source.doc_id]?.lastModifiedDate}Z`,
          );
          let field = dataRoomContextFields.find(
            (field) => field.id === source.doc_id,
          );
          if (!field) {
            field = {
              id: source.doc_id,
              isUpdated: false,
              ...dataRoomFields[source.doc_id],
              sourceType: ChunkType.DataRoomField,
            };
            dataRoomContextFields.push(field);
          }
          field.isUpdated = fieldLastModifiedDate > messageDate;
          field.iconName = fieldTypeToIconName(field.type);
        }
        if (
          source.source_type === ChunkType.DataRoomField &&
          !dataRoomFields[source.doc_id]
        ) {
          const { label } = source.meta as { label: string };
          dataRoomContextFields.push({ label });
        }

        if (source.source_type === ChunkType.FinStructureField) {
          const { groupIds, sourceId, label, value, groupMember, fieldType } =
            source.meta as NeoGptChatSourceMeta;

          if (groupMember === NeoGptGroupTypeName.DYNAMIC) {
            const dynamicGroupId =
              staticGroups.find((group) => group.key === REFS_BLOCKS_GROUP_KEY)
                ?.id || '';

            finStructureContextFields.push({
              id: dynamicGroups.find((group) => group.id === sourceId)?.id,
              label,
              groupIds: [dynamicGroupId, '', ''],
              groupType: groupMember,
              iconName: 'extension',
              sourceType: ChunkType.FinStructureField,
            });
          }

          if (groupMember === NeoGptGroupTypeName.STATIC) {
            const [mainGroupId, subGroupId, subSubGroupId] = groupIds;
            const mainGroup: CustomFinStructureGroup = staticGroups.find(
              (group) => group.id === mainGroupId,
            );
            const subGroup = mainGroup?.subGroups.find(
              (group) => group.id === subGroupId,
            );

            const subSubGroup = subGroup?.subGroups?.find(
              (group) => group.id === subSubGroupId,
            );
            // Answer could be any of those, therefor we start from inside - out. Order matters
            const availableGroup = subSubGroup || subGroup || mainGroup;
            const field = availableGroup?.fields?.find(
              (field) => field.id === sourceId,
            );

            const isUpdated = field?.lastModifiedDate
              ? new Date(field.lastModifiedDate + 'Z') > messageDate
              : false;

            finStructureContextFields.push({
              id: field?.isHidden || field?.disabled ? '' : sourceId,
              label,
              type: fieldType,
              isUpdated,
              groupIds,
              groupType: groupMember,
              value,
              iconName: fieldTypeToIconName(fieldType),
              sourceType: ChunkType.FinStructureField,
            });
          }
        }
      });

      if (documents.length) {
        groupedSources.documents = documents.sort(
          (docA, docB) => docB.pages.length - docA.pages.length,
        );

        groupedSources.documents.forEach((doc) => {
          doc.pages.sort((pageA, pageB) => pageA - pageB);
        });
      }

      if (dataRoomContextFields.length) {
        dataRoomContextFields = fieldSourceMapToGroupIndex(
          dataRoomContextFields,
          groupsOrdered,
        );
      }

      if (finStructureContextFields.length) {
        finStructureContextFields = fieldSourceMapToGroupIndex(
          finStructureContextFields,
          groupsOrdered,
        );
      }

      return {
        ...updatedMessage,
        content: {
          ...updatedMessage.content,
          groupedSources,
        },
        copyContent: message.content.text,
      };
    });
  },
);

export const selectGroupByTimestampChatMessages = createSelector(
  selectChatMessages,
  (messages) => {
    return groupBy(messages, (entity) =>
      dayjs(entity.timestamp * 1000).format('YYYY.MM.DD'),
    );
  },
);

export const selectDocumentSummary = createSelector(
  selectNeoGptChatState,
  (state: NeoGptChatState) => state.chatInitialData.summary,
);

export const selectSuggestedQuestions = createSelector(
  selectNeoGptChatState,
  (state: NeoGptChatState) => state.chatInitialData.questions,
);

export const selectIsChatSummarySeenByUser = createSelector(
  selectNeoGptChatState,
  (state: NeoGptChatState) => {
    return state.chatInitialData.seen_by_user;
  },
);

export const selectShowSuggestedQuestions = createSelector(
  selectNeoGptChatState,
  selectIsChatSummarySeenByUser,
  (state: NeoGptChatState, isChatSummarySeenByUser) =>
    state.showSuggestedQuestions && !isChatSummarySeenByUser,
);

export const selectHasUnreadMessages = createSelector(
  selectNeoGptChatState,
  ({ chatMessages }: NeoGptChatState) => {
    if (!chatMessages?.length) {
      return false;
    }

    const lastMessage = chatMessages[chatMessages.length - 1];
    return lastMessage.source !== MessageType.User && !lastMessage.is_seen;
  },
);

export const selectDocumentState = createSelector(
  selectNeoGptChatState,
  ({ documentStatus }: NeoGptChatState) => documentStatus.chat_state,
);

export const selectIsDocumentStatePending = createSelector(
  selectDocumentState,
  (documentState) =>
    documentState === DocumentStatus.PENDING ||
    documentState === DocumentStatus.DOCUMENT_PENDING,
);

export const selectIsMessageLoading = createSelector(
  selectNeoGptChatState,
  ({ isMessageLoading }: NeoGptChatState) => isMessageLoading,
);

export const selectIsDocumentLoaded = createSelector(
  selectNeoGptChatState,
  (state: NeoGptChatState) => state.isDocumentLoaded,
);

export const selectIsNeoGptChatVisible = createSelector(
  selectNeoGptChatState,
  ({ isChatVisible }: NeoGptChatState) => isChatVisible,
);

export const selectIsChatVisible = createSelector(
  selectIsNeoGptChatVisible,
  (isChatVisible: boolean) => {
    return {
      isChatVisible,
    };
  },
);

export const selectIsNeoGptChatButtonVisible = createSelector(
  selectNeoGptChatState,
  ({ isChatBtnVisible }: NeoGptChatState) => isChatBtnVisible,
);

export const selectIsOnboardingActive = createSelector(
  selectNeoGptChatState,
  ({ isOnboardingActive }: NeoGptChatState) => isOnboardingActive,
);

export const selectIsDocumentStatusVisible = createSelector(
  selectNeoGptChatState,
  (state: NeoGptChatState) => {
    return state.isDocumentStatusVisible[state.documentId];
  },
);

export const selectIsChatRatingsVisible = createSelector(
  selectNeoGptChatState,
  (state: NeoGptChatState) => {
    return state.isChatRatingVisible;
  },
);

export const selectIsNegativeFeedbackFormVisible = createSelector(
  selectNeoGptChatState,
  (state: NeoGptChatState) => {
    return state.isNegativeFeedbackFormVisible;
  },
);

export const selectUserExperienceMessageId = createSelector(
  selectNeoGptChatState,
  (state: NeoGptChatState) => {
    return state.selectedChatMessageIndex;
  },
);

export const selectChatSettingsVisibility = createSelector(
  selectNeoGptChatState,
  (state: NeoGptChatState) => {
    return state.areChatSettingsVisible;
  },
);

export const selectIsEmojiAnimationVisible = createSelector(
  selectNeoGptChatState,
  (state: NeoGptChatState) => {
    return state.isEmojiAnimationVisible;
  },
);

export const selectNeoGptActiveSession = createSelector(
  selectNeoGptChatState,
  (state) => state.activeSession,
);

export const selectStartOnboarding = createSelector(
  selectUserOnboardingGPT,
  selectIsNeoGptChatVisible,
  selectIsOnboardingActive,
  (neoGptOnboarding, isChatVisible, isOnboardingActive) => {
    return neoGptOnboarding === false && isChatVisible && isOnboardingActive;
  },
);

export const selectChatSettings = createSelector(
  selectNeoGptChatState,
  (state: NeoGptChatState) => {
    return state.chatSettings;
  },
);

export const selectIsNeoGptActive = createSelector(
  selectNeoGptChatState,
  (state: NeoGptChatState) => {
    return state.activeSession !== NeoGptActiveSession.NON_SESSION;
  },
);

export const selectSelectedFieldId = createSelector(
  selectNeoGptChatState,
  (state: NeoGptChatState) => {
    return state.selectedFieldId;
  },
);

export const selectCountOfEnabledGroupsInCase = createSelector(
  selectBusinessCaseInformation,
  selectBusinessCaseId,
  (businessCaseInformation, businessCaseId) => {
    if (!businessCaseInformation || !businessCaseId) {
      return -1;
    }

    return Object.keys(businessCaseInformation).length;
  },
);

export const selectIsNeoGptVisible = createSelector(
  selectDocumentId,
  selectIsNeoGptChatVisible,
  selectIsNeoGptChatButtonVisible,
  selectIsNeoGptActive,
  selectNeoGptActiveSession,
  (
    documentId,
    isChatVisible,
    isChatButtonVisible,
    isNeoGptActive,
    activeSession,
  ) => {
    return {
      isChatVisible:
        activeSession === NeoGptActiveSession.DATA_ROOM
          ? !documentId && isChatVisible
          : isChatVisible,
      isChatButtonVisible,
      isNeoGptActive,
      documentId,
    };
  },
);

export const selectUserQuery = createSelector(
  selectNeoGptChatState,
  (state: NeoGptChatState) => {
    return state.userQuery;
  },
);

export const selectIsSwitchContextPromptVisible = createSelector(
  selectNeoGptChatState,
  (state: NeoGptChatState) => {
    return state.context;
  },
);

export const selectIsInitialMessageVisible = createSelector(
  selectNeoGptChatState,
  (state: NeoGptChatState) => {
    return (
      (state.documentId &&
        !state.chatMessages.length &&
        state.chatInitialData.seen_by_user) ||
      (!state.documentId &&
        !state.documentStatus.chat_state &&
        !state.chatMessages.length)
    );
  },
);

export const selectSendBusinessQueryRequest = createSelector(
  selectBusinessCaseId,
  selectChatSessionId,
  selectDocumentId,
  selectUser,
  selectUserQuery,
  selectNeoGptActiveSession,
  (businessCaseId, sessionId, documentId, user, userQuery, activeSession) => {
    return {
      businessCaseId,
      sessionId,
      documentId,
      user,
      userQuery,
      activeSession,
    };
  },
);

export const selectSendFinancingQueryRequest = createSelector(
  selectChatSessionId,
  selectUser,
  selectBusinessCaseId,
  selectNeoGptActiveSession,
  selectUserQuery,
  (chatSessionId, user, businessCaseId, activeSession, userQuery) => ({
    chatSessionId,
    user,
    businessCaseId,
    activeSession,
    userQuery,
  }),
);

export const selectNeoGptChatData = createSelector(
  selectChatMessages,
  selectSuggestedQuestions,
  selectShowSuggestedQuestions,
  selectIsMessageLoading,
  selectIsDocumentStatusVisible,
  selectIsChatRatingsVisible,
  selectIsNegativeFeedbackFormVisible,
  selectUserExperienceMessageId,
  selectChatSettingsVisibility,
  selectIsChatSummarySeenByUser,
  selectIsEmojiAnimationVisible,
  selectIsInitialMessageVisible,
  selectGroupByTimestampChatMessages,
  selectIsDocumentStatePending,
  (
    chatMessages,
    suggestedQuestions,
    showSuggestedQuestions,
    isMessageLoading,
    isDocumentStatusVisible,
    isChatRatingVisible,
    isNegativeFeedbackFormVisible,
    selectedChatMessageIndex,
    areChatSettingsVisible,
    summarySeenByUser,
    isEmojiAnimationVisible,
    isInitialMessageVisible,
    groupByTimestampChatMessages,
    isDocumentStatePending,
  ) => ({
    chatMessages,
    suggestedQuestions,
    showSuggestedQuestions,
    isMessageLoading,
    isDocumentStatusVisible,
    isChatRatingVisible,
    isNegativeFeedbackFormVisible,
    selectedChatMessageIndex,
    areChatSettingsVisible,
    summarySeenByUser,
    isEmojiAnimationVisible,
    isInitialMessageVisible,
    groupByTimestampChatMessages,
    isDocumentStatePending,
  }),
);

export const selectGetHistoryRequest = createSelector(
  selectChatSessionId,
  selectChatMessages,
  selectNeoGptActiveSession,
  (sessionId, chatMessages, activeSession) => ({
    sessionId,
    chatMessages,
    activeSession,
  }),
);

export const selectHighlightedId = createSelector(
  // selectHighlightedElementId,
  selectSelectedFieldId,
  selectNeoGptActiveSession,
  (dataRoomHighlightedId, activeSession) => {
    switch (activeSession) {
      case NeoGptActiveSession.DATA_ROOM: {
        return { id: dataRoomHighlightedId };
      }
      //TODO Nikolay Topalov
      // case NeoGptActiveSession.FINANCING_DETAILS: {
      //   return { id: financingHighlightedId };
      // }
      default:
        return { id: '' };
    }
  },
);

export const selectNeoGptActivateDataRoom = createSelector(
  selectCountOfEnabledGroupsInCase,
  selectCustomerFeatureFlags,
  (countOfEnabledGroupsInCase, featureFlags) => ({
    countOfEnabledGroupsInCase,
    featureFlags,
  }),
);
