import { createAction, props } from '@ngrx/store';

export const loadInboxDocuments = createAction(
  '[Inbox Documents Page] Load',
  props<{ payload: { businessCaseId: string } }>(),
);

export const clearInboxDocuments = createAction(
  '[Inbox Documents Page] Clear inbox documents',
);

export const activateInboxDocument = createAction(
  '[Inbox Documents Page] Activate document id inbox',
  props<{ documentId: string; pageNumber: number }>(),
);
