import { Injectable } from '@angular/core';
import { InboxDocumentControllerService } from '@fincloud/swagger-generator/document';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { catchError, map, switchMap } from 'rxjs/operators';

import {
  StateLibContextApiActions,
  selectBusinessCaseId,
} from '@fincloud/state/business-case';
import { concatLatestFrom } from '@ngrx/operators';
import { Store } from '@ngrx/store';
import { of } from 'rxjs';
import {
  StateLibInboxDocumentsApiActions,
  StateLibInboxDocumentsPageActions,
} from '../actions';

@Injectable()
export class StateLibInboxDocumentsEffects {
  loadDocumentInbox$ = createEffect(() =>
    this.actions$.pipe(
      ofType(
        StateLibContextApiActions.loadCustomerBusinessCaseContextSuccess,
        StateLibInboxDocumentsPageActions.loadInboxDocuments,
      ),
      concatLatestFrom(() => this.store.select(selectBusinessCaseId)),
      switchMap(([, businessCaseId]) => {
        return this.documentInboxControllerService
          .getAllInboxDocumentsForBusinessCase({
            businessCaseId,
          })
          .pipe(
            map((documents) =>
              StateLibInboxDocumentsApiActions.loadDocumentInboxSuccess({
                payload: documents,
              }),
            ),
            catchError(() => {
              return of(
                StateLibInboxDocumentsApiActions.loadDocumentInboxFailure(),
              );
            }),
          );
      }),
    ),
  );

  constructor(
    private actions$: Actions,
    private documentInboxControllerService: InboxDocumentControllerService,
    private store: Store,
  ) {}
}
