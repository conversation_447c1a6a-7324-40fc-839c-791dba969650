import { DocumentEntity } from '@fincloud/swagger-generator/document';
import { Action, ActionReducer, createReducer, on } from '@ngrx/store';
import {
  StateLibInboxDocumentsApiActions,
  StateLibInboxDocumentsPageActions,
} from '../actions';

export const initialInboxDocumentsState: DocumentEntity[] = [];

export const stateLibInboxDocumentsReducer: ActionReducer<
  DocumentEntity[],
  Action
> = createReducer(
  initialInboxDocumentsState,
  on(
    StateLibInboxDocumentsApiActions.loadDocumentInboxSuccess,
    (state, action): DocumentEntity[] => {
      return action.payload;
    },
  ),
  on(
    StateLibInboxDocumentsPageActions.clearInboxDocuments,
    (): DocumentEntity[] => {
      return initialInboxDocumentsState;
    },
  ),
);
