import { DataStructureUtils } from '@fincloud/core/data-structure';
import {
  User,
  UserAttributes,
  UserPreferences,
} from '@fincloud/swagger-generator/authorization-server';
import { UserPreferencesUpdateAction, UserRole } from '@fincloud/types/enums';
import { UserState } from '@fincloud/types/models';
import { createFeatureSelector, createSelector } from '@ngrx/store';

export const selectUserState = createFeatureSelector<UserState>('user');

export const selectHasUserPermission = (permissionCode: string) =>
  createSelector(selectUserPermissionsCodes, (permissionsCodes: string[]) => {
    return permissionsCodes.includes(permissionCode);
  });

export const selectUserCustomerKey = createSelector(
  selectUserState,
  (state) => state?.userToken?.customer_key,
);

export const selectUserId = createSelector(
  selectUserState,
  (state) => state?.userToken?.sub,
);

export const selectUserToken = createSelector(
  selectUserState,
  (state) => state?.userToken,
);

export const selectUserRoles = createSelector(
  selectUserState,
  (state) => state?.user?.userRoles,
);

export const selectUserRolesNames = createSelector(selectUserState, (state) =>
  DataStructureUtils.isStringArray(state?.user?.userRoles)
    ? state?.user?.userRoles
    : state?.user?.userRoles.map((ur) => ur.name),
);

export const selectUserDracconCredentials = createSelector(
  selectUserState,
  (state) => state?.dracoonCredentials,
);

export const selectUserNextfolderCredentials = createSelector(
  selectUserState,
  (state) => state?.nextfolderCredentials,
);

export const selectIsUserAdmin = createSelector(selectUserState, (state) =>
  state?.user?.userRoles?.some((r) => r.name === UserRole.ORG_ADMIN),
);

export const selectIsUserSigner = createSelector(selectUserState, (state) =>
  state?.user?.userRoles?.some(
    (r) => r.name === UserRole.USAGE_CONTRACT_SIGNER,
  ),
);

export const selectIsAccountManager = createSelector(
  selectUserState,
  (state: UserState) =>
    state?.user?.userRoles?.some((r) => r.name === UserRole.ACCOUNT_MANAGER),
);

export const selectIsInitialLogin = createSelector(
  selectUserState,
  (state: UserState) => state?.user?.initialPassword,
);

export const selectUser = createSelector(
  selectUserState,
  (state) => state?.user,
);

export const selectUserHasBeenPresentedTerms = createSelector(
  selectUser,
  (user) => user?.userPreferences?.hasBeenPresented?.accepted,
);

export const selectUserCustomerKeys = createSelector(
  selectUserState,
  (state: UserState): string[] => state.customerKeys,
);

export const selectUserCustomProperty = createSelector(
  selectUser,
  (state: User) => state.custom,
);

export const selectIsUserPartOfMultipleOrganisations = createSelector(
  selectUserCustomerKeys,
  (customerKeys: string[]): boolean => customerKeys.length > 1,
);

export const selectUserPreferences = createSelector(
  selectUser,
  (user): UserPreferences => user.userPreferences,
);

export const selectUserPreferencesUpdateAction = createSelector(
  selectUserState,
  (state: UserState): UserPreferencesUpdateAction =>
    state.userPreferencesUpdateAction,
);

export const selectUserAttributes = createSelector(
  selectUser,
  (user): UserAttributes => user.attributes,
);
export const selectIsPlatformManager = createSelector(
  selectUserState,
  (state: UserState): boolean =>
    !!state?.user?.userRoles?.some((r) => r.name === UserRole.PLATFORM_MANAGER),
);

export const selectUserPermissionsCodes = createSelector(
  selectUserState,
  (state: UserState) => state?.userPermissionsCodes,
);
