import { ActionReducer } from '@ngrx/store';
import { localStorageSync } from 'ngrx-store-localstorage';
import { STORAGE_PREFIX } from '../utils/storage-prefix';

export function sessionStorageSyncReducer(
  reducer: ActionReducer<unknown>,
): ActionReducer<unknown> {
  return localStorageSync({
    keys: [
      {
        neoGptChat: ['isChatRatingSeen'],
      },
      {
        nextFolder: ['businessCaseId'],
      },
    ],
    rehydrate: true,
    storage: sessionStorage,
    storageKeySerializer: (key) => `${STORAGE_PREFIX}${key}`,
  })(reducer);
}
