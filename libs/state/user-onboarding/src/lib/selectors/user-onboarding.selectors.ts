import { UserOnboardingState } from '@fincloud/types/models';
import { createFeatureSelector, createSelector } from '@ngrx/store';

const selectUserOnboardingState =
  createFeatureSelector<UserOnboardingState>('userOnboarding');

export const selectFeatureOnboarding = createSelector(
  selectUserOnboardingState,
  (state) => state.featureOnboarding ?? null,
);

export const selectUserOnboardingGPT = createSelector(
  selectUserOnboardingState,
  (state) => state.featureOnboarding?.gptAssistant,
);

export const selectIsOnboardingCompleted = createSelector(
  selectUserOnboardingState,
  (state) => state.featureOnboarding.notificationSystem,
);

export const selectDocumentInboxFeatureOnboarding = createSelector(
  selectFeatureOnboarding,
  (onboardingState) => onboardingState?.documentInbox,
);
