import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';

import { selectUserCustomProperty, selectUserId } from '@fincloud/state/user';
import { UserManagementControllerService } from '@fincloud/swagger-generator/authorization-server';
import { FeatureOnboarding, UserOnboardingState } from '@fincloud/types/models';
import { concatLatestFrom } from '@ngrx/operators';
import { Store } from '@ngrx/store';
import { catchError, filter, map, of, switchMap } from 'rxjs';

import { StateLibUserPageActions } from '@fincloud/state/user';
import {
  StateLibUserOnboardingApiActions,
  StateLibUserOnboardingPageActions,
} from '../actions';
import { selectFeatureOnboarding } from '../selectors/user-onboarding.selectors';

@Injectable()
export class StateLibUserOnboardingEffects {
  loadUserOnboardingState$ = createEffect(() =>
    this.actions$.pipe(
      ofType(StateLibUserOnboardingPageActions.loadUserOnboarding),
      concatLatestFrom(() => this.store.select(selectUserId)),
      switchMap(([, id]) => {
        return this.userManagementControllerService
          .getUserById({
            userId: id,
          })
          .pipe(
            map((user) => {
              const initialOnboardingState = (
                user.custom as UserOnboardingState
              )?.featureOnboarding;
              const onboardingState =
                initialOnboardingState ??
                ({
                  documentInbox: false,
                  gptAssistant: false,
                  notificationSystem: false,
                } as FeatureOnboarding);

              return StateLibUserOnboardingPageActions.setUserOnboarding({
                payload: onboardingState,
              });
            }),
          );
      }),
    ),
  );

  updateUserOnboardingNotifications$ = createEffect(() =>
    this.actions$.pipe(
      ofType(
        StateLibUserOnboardingPageActions.updateUserOnboardingNotifications,
      ),
      concatLatestFrom(() => [
        this.store.select(selectUserId),
        this.store.select(selectFeatureOnboarding),
        this.store.select(selectUserCustomProperty),
      ]),
      switchMap(([, userId, onboardingState, previousCustomState]) =>
        this.userManagementControllerService
          .updateUserDetails({
            userId,
            body: {
              custom: {
                ...previousCustomState,
                featureOnboarding: {
                  ...onboardingState,
                  notificationSystem: true,
                },
              },
            },
          })
          .pipe(
            map(() =>
              StateLibUserOnboardingApiActions.updateUserOnboardingNotificationsSuccess(),
            ),
            catchError(() =>
              of(
                StateLibUserOnboardingApiActions.updateUserOnboardingNotificationsFailure(),
              ),
            ),
          ),
      ),
    ),
  );

  updateInboxOnboarding$ = createEffect(() =>
    this.actions$.pipe(
      ofType(StateLibUserOnboardingPageActions.updateUserInboxOnboarding),
      concatLatestFrom(() => [
        this.store.select(selectUserId),
        this.store.select(selectFeatureOnboarding),
        this.store.select(selectUserCustomProperty),
      ]),
      filter(([, userId]) => !!userId),
      switchMap(([, userId, onboardingState, previousCustomState]) =>
        this.userManagementControllerService
          .updateUserDetails({
            userId,
            body: {
              custom: {
                ...previousCustomState,
                featureOnboarding: {
                  ...onboardingState,
                  documentInbox: true,
                },
              },
            },
          })
          .pipe(
            map(() =>
              StateLibUserOnboardingApiActions.updateUserInboxOnboardingSuccess(),
            ),
            catchError(() =>
              of(
                StateLibUserOnboardingApiActions.updateUserOnboardingGptFailure(),
              ),
            ),
          ),
      ),
    ),
  );

  loadUserOnboardingAfterUserIsLoaded$ = createEffect(() =>
    this.actions$.pipe(
      ofType(StateLibUserPageActions.setUser),
      map(() => {
        return StateLibUserOnboardingPageActions.loadUserOnboarding();
      }),
    ),
  );

  constructor(
    private store: Store,
    private actions$: Actions,
    private userManagementControllerService: UserManagementControllerService,
  ) {}
}
