import { UserOnboardingState } from '@fincloud/types/models';
import { Action, ActionReducer, createReducer, on } from '@ngrx/store';
import {
  StateLibUserOnboardingApiActions,
  StateLibUserOnboardingPageActions,
} from '../actions';

export const initialUserOnboardingState: UserOnboardingState = {
  featureOnboarding: {
    documentInbox: null,
    gptAssistant: null,
    notificationSystem: null,
  },
};

export const stateLibUserOnboardingReducer: ActionReducer<
  UserOnboardingState,
  Action
> = createReducer(
  initialUserOnboardingState,

  on(
    StateLibUserOnboardingPageActions.setUserOnboarding,
    (state, action): UserOnboardingState => {
      const { gptAssistant, documentInbox, notificationSystem } =
        action.payload || {};

      return {
        ...state,
        featureOnboarding: {
          documentInbox:
            documentInbox ??
            initialUserOnboardingState.featureOnboarding.documentInbox,
          gptAssistant:
            gptAssistant ??
            initialUserOnboardingState.featureOnboarding.gptAssistant,
          notificationSystem:
            notificationSystem ??
            initialUserOnboardingState.featureOnboarding.notificationSystem,
        },
      };
    },
  ),
  on(
    StateLibUserOnboardingApiActions.updateUserInboxOnboardingSuccess,
    (state): UserOnboardingState => {
      return {
        ...state,
        featureOnboarding: { ...state.featureOnboarding, documentInbox: true },
      };
    },
  ),
  on(
    StateLibUserOnboardingApiActions.updateUserOnboardingNotificationsSuccess,
    (state): UserOnboardingState => {
      return {
        ...state,
        featureOnboarding: {
          ...state.featureOnboarding,
          notificationSystem: true,
        },
      };
    },
  ),
);
