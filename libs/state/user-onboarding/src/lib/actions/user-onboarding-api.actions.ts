import { createAction } from '@ngrx/store';

export const updateUserOnboardingGptSuccess = createAction(
  '[ User onboarding  API] Update onboarding GTP Success',
);
export const updateUserOnboardingGptFailure = createAction(
  '[ User onboarding  API] Update onboarding GTP Failure',
);
export const updateUserOnboardingNotificationsSuccess = createAction(
  '[ User onboarding  API] Update onboarding notifications Success',
);
export const updateUserOnboardingNotificationsFailure = createAction(
  '[ User onboarding  API] Update onboarding notifications Failure',
);

export const updateUserInboxOnboardingSuccess = createAction(
  '[ User onboarding API] Update onboarding inbox Success',
);

export const updateUserInboxOnboardingFailure = createAction(
  '[ User onboarding API] Update onboarding inbox Failure',
);
