import { FeatureOnboarding } from '@fincloud/types/models';
import { createAction, props } from '@ngrx/store';

export const loadUserOnboarding = createAction(
  '[ User onboarding  Page] Load user onboarding from storage',
);

export const setUserOnboarding = createAction(
  '[ User onboarding  Page] set onboarding',
  props<{ payload: FeatureOnboarding }>(),
);

export const updateUserOnboardingGptTry = createAction(
  '[ User onboarding  Page] Update onboarding GPT try',
);
export const updateUserOnboardingNotifications = createAction(
  '[ User onboarding  Page] Update onboarding notifications',
);

export const updateUserInboxOnboarding = createAction(
  '[ User onboarding  Page] Update inbox onboarding',
);
