/*eslint-disable*/
require('dotenv').config();
const chalk = require('chalk');
function logError(message: any) {
  console.error(chalk.red(message));
}
function logSuccess(message: any) {
  console.log(chalk.green(message));
}
function logInfo(message: any) {
  console.log(chalk.blueBright(message));
}

class Generator {
  private objectHash = require('object-hash');
  private childProcess = require('child_process');
  private fs = require('fs');
  private fetch = require('node-fetch');

  private serviceInfos: Record<
    string,
    {
      projectId: number;
      branch?: string;
      swaggerArtifactURL?: string;
      swaggerHash?: string;
    }
  > = {};

  readonly gitLabToken = process.env.GITLAB_TOKEN;
  readonly gitLabUrl: string = 'https://git.neo.loan/api/v4';
  readonly servicesGroup: string =
    '/groups/6/projects?simple=true&per_page=100';
  readonly swaggerGeneratorDir: string = 'projects/swagger-generator';
  readonly serviceInfosJSONFile: string = `${this.swaggerGeneratorDir}/services.json`;

  static readonly javaServices: string[] = [
    'application',
    'billing',
    'communication',
    'company',
    'consortium-manager',
    'contract-management',
    'document',
    'document-signing',
  ];

  static readonly nodeServices: string[] = [
    'customer',
    'document-generator',
    'document-preview',
    'exchange',
    'feedback',
    'handelsregister',
    'notification',
    'user',
  ];
  static readonly supportedServices = [
    ...this.nodeServices,
    ...this.javaServices,
  ];

  constructor() {
    if (!this.gitLabToken) {
      throw new Error('Missing environment variable: GITLAB_TOKEN');
    }
  }

  async generateServices(services: string[], branch: string) {
    logInfo(
      `Generating ${services.length} services with source branch ${branch}...`,
    );
    this.loadExistingServiceInfos();
    const servicesWithKnownProjectId =
      await this.getServicesWithKnownProjectId(services);
    const servicesWithSwaggerDifference =
      await this.filterServicesBySwaggerHashDifference(
        servicesWithKnownProjectId,
        branch,
      );
    this.generateServicesFromApiSpec(servicesWithSwaggerDifference);
    this.writeServiceInfosToDisk();
  }

  private loadExistingServiceInfos() {
    try {
      if (this.fs.existsSync(this.serviceInfosJSONFile)) {
        this.serviceInfos = JSON.parse(
          this.fs.readFileSync(this.serviceInfosJSONFile).toString(),
        );
      }
    } catch (e) {
      logError(`Could not load ${this.serviceInfosJSONFile}`);
      logError(e);
    }
  }

  private createSwaggerGeneratorDir() {
    if (!this.fs.existsSync(this.swaggerGeneratorDir)) {
      this.fs.mkdirSync(this.swaggerGeneratorDir, { recursive: true });
    }
  }

  private async getServicesWithKnownProjectId(services: string[]) {
    let gitlabProjectList: any;
    try {
      const req = await this.fetch(
        `${this.gitLabUrl}${this.servicesGroup}&private_token=${this.gitLabToken}`,
      );
      gitlabProjectList = await req.json();
    } catch (e) {
      logError(`Could not get Gitlab project ids!`);
      process.exit(1);
    }

    const servicesWithExistingProjectId: string[] = [];
    for (const service of services) {
      const projectId = gitlabProjectList.find(
        (s: any) => s.name === `srv-${service}`,
      )?.id;
      if (projectId) {
        if (service in this.serviceInfos) {
          this.serviceInfos[service].projectId = projectId;
        } else {
          this.serviceInfos[service] = {
            projectId,
          };
        }
        servicesWithExistingProjectId.push(service);
      } else {
        logError(`Could not find Gitlab project id for srv-${service}!`);
      }
    }
    return servicesWithExistingProjectId;
  }

  private async filterServicesBySwaggerHashDifference(
    services: string[],
    branch: string,
  ) {
    const servicesWithHashDifference: string[] = [];
    for (const service of services) {
      const swaggerURL = this.getSwaggerJobArtifactURL(service, branch);

      try {
        const req = await this.fetch(
          `${swaggerURL}&private_token=${this.gitLabToken}`,
        );
        if (req.status === 200) {
          const swaggerHash = this.objectHash.sha1(await req.json());
          if (this.serviceInfos[service]?.swaggerHash != swaggerHash) {
            this.serviceInfos[service].branch = branch;
            this.serviceInfos[service].swaggerArtifactURL = swaggerURL;
            this.serviceInfos[service].swaggerHash = swaggerHash;
            servicesWithHashDifference.push(service);
          } else {
            console.log(`Skipping srv-${service}, swagger up to date!`);
          }
        } else {
          logError(`Could not get swagger of srv-${service}!`);
          logError(`URL: ${swaggerURL}`);
          logError(`Status: ${req.status}`);
        }
      } catch (e) {
        logError(`Request ${swaggerURL} failed`);
        logError(e);
      }
    }
    console.log(`Found swagger updates for following services:`);
    console.log(servicesWithHashDifference);
    return servicesWithHashDifference;
  }

  private getSwaggerJobArtifactURL(service: string, branch: string) {
    let jobName;
    if (Generator.javaServices.includes(service)) {
      jobName = 'maven_package';
    } else if (Generator.nodeServices.includes(service)) {
      jobName = 'generate_swagger';
    } else {
      throw new Error(
        `Service "${service}" is not yet supported by this script!`,
      );
    }
    return `${this.gitLabUrl}/projects/${this.serviceInfos[service].projectId}/jobs/artifacts/${branch}/raw/swagger.json?job=${jobName}`;
  }

  private generateServicesFromApiSpec(services: string[]) {
    for (const service of services) {
      const inputFileURL = `${this.serviceInfos[service].swaggerArtifactURL}&private_token=${this.gitLabToken}`;
      const outputDir = `${this.swaggerGeneratorDir}/src/${service}`;

      let generateCommand: string;
      if (Generator.javaServices.includes(service)) {
        generateCommand = `./node_modules/.bin/ng-swagger-gen -i '${inputFileURL}' -o ${outputDir}`;
      } else if (Generator.nodeServices.includes(service)) {
        generateCommand = `./node_modules/.bin/ng-openapi-gen --input '${inputFileURL}' --output ${outputDir}`;
      } else {
        logError(`Service "${service}" is not yet supported by this script!`);
        continue;
      }
      this.childProcess.exec(generateCommand, (error: any) => {
        if (error) {
          logError(`COMMAND: ${generateCommand}`);
          logError(`Could not generate ${outputDir}!`);
          logError(error);
        } else {
          logSuccess(`Generated ${outputDir}`);
        }
      });
    }
  }

  private writeServiceInfosToDisk() {
    try {
      this.createSwaggerGeneratorDir();
      this.fs.writeFileSync(
        this.serviceInfosJSONFile,
        JSON.stringify(this.serviceInfos, null, 2),
      );
    } catch (e) {
      logError(`Could not write ${this.serviceInfosJSONFile} to disk!`);
      throw e;
    }
  }

  async ensureNoSwaggerDifference(services: string[], branch: string) {
    logInfo(
      `Checking ${services.length} services with source branch ${branch} for swagger hash differences...`,
    );
    this.loadExistingServiceInfos();
    const servicesWithKnownProjectId =
      await this.getServicesWithKnownProjectId(services);
    if (servicesWithKnownProjectId.length != services.length) {
      logError('Exiting due to unknown Gitlab project ids!');
      process.exit(1);
    }
    const servicesWithSwaggerDifference =
      await this.filterServicesBySwaggerHashDifference(
        servicesWithKnownProjectId,
        branch,
      );
    if (servicesWithSwaggerDifference.length != 0) {
      logError(`Exiting due to outdated services!`);
      logError(
        `Please execute following command locally: npm run generate-swagger ${branch}`,
      );
      process.exit(1);
    }
    logSuccess('All previously generated services are up to date!');
  }
}

const processArguments = process.argv.splice(2);
const generator = new Generator();

let branch;
let services = Generator.supportedServices;
if (processArguments.length == 1) {
  branch = processArguments[0];
} else if (processArguments.length == 2) {
  branch = processArguments[1];
  services = [processArguments[0].replace(/^srv-/, '')];
} else {
  logError(
    'Expected either 1 argument $BRANCH to run on all services or 2 arguments $SERVICE $BRANCH!',
  );
  process.exit(1);
}

if (
  process.env.ONLY_CHECK_SWAGGER_DIFFERENCE == '1' ||
  process.env.ONLY_CHECK_SWAGGER_DIFFERENCE == 'true'
) {
  logInfo('You have enabled ONLY_CHECK_SWAGGER_DIFFERENCE');
  generator.ensureNoSwaggerDifference(services, branch).then();
} else {
  generator.generateServices(services, branch).then();
}
