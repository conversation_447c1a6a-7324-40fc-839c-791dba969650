{"locale": "de", "translations": {"2078863684537615971": "{VAR_PLURAL, plural, =1 {vor 1 Tag} other {vor {INTERPOLATION} Tagen}}", "4382629238310047310": "{VAR_PLURAL, plural, =1 {vor 1 Woche} other {vor {INTERPOLATION} W<PERSON><PERSON>}}", "5216529258824596799": "{VAR_PLURAL, plural, =1 {vor 1 Stunde} other {vor {INTERPOLATION} Stunden}}", "8830575832589857077": "{VAR_PLURAL, plural, =0 {jetzt} =1 {vor 1 Minute} other {vor {INTERPOLATION} Minuten}}", "ConnectedField.text": "<PERSON><PERSON> ist mit einem anderen verknüpft", "acceptRemove.info-text": " <PERSON><PERSON>, dass dadurch nicht das Teilen des unternehmensbezogenen Data Rooms via eines Finanzierungsfalls beendet wird. Das Teilen via eines Finanzierungsfalls wird automatisch beendet, wenn der unternehmensbezogene Data Room von allen Finanzierungsfällen dieses Unternehmens entfernt wird. ", "acceptRemove.info-text.label": "Schließen", "acceptRemove.title": " Das direkte Teilen des unternehmensbezogenen Data Rooms wurde beendet. ", "accountLogs.columns.deviceAndBrowser": "Ger<PERSON>/Browser", "accountLogs.columns.ipAddress": "IP-Adresse", "accountLogs.columns.operation": "Operation", "accountLogs.empty": "<PERSON><PERSON><PERSON> diesen Nutzer gibt es keine Aktivitätsprotokolle.", "accountManagement.actions.view.users": "<PERSON><PERSON><PERSON>", "accountManagement.button.create.user": "<PERSON><PERSON><PERSON> er<PERSON>", "accountManagement.customer.createCustomerModal.BIC": "BIC", "accountManagement.customer.createCustomerModal.bankingGroup": "Bankengruppe", "accountManagement.customer.createCustomerModal.basicTittle": " Kundendetails ", "accountManagement.customer.createCustomerModal.errorMessage.bicLength": "Bitte verwenden Sie für Ihr BIC 8 Zeichen oder mehr", "accountManagement.customer.createCustomerModal.errorMessage.forbiddenSymbol2": "Unzulässiges Symbol", "accountManagement.customer.createCustomerModal.errorMessage.invalidBIC": "Ungültige BIC", "accountManagement.customer.createCustomerModal.featureTemplate": "neoshare Feature-Vorlage", "accountManagement.customer.createCustomerModal.id": "Kundenschlüssel", "accountManagement.customer.createCustomerModal.label.customerName": "Kundenname", "accountManagement.customer.createCustomerModal.label.type": "Kundentyp", "accountManagement.customer.createCustomerModal.other": "<PERSON><PERSON>", "accountManagement.customer.createCustomerModal.placeholder": "Kundentyp", "accountManagement.customer.createCustomerModal.salesChannel": "Vertriebskanal", "accountManagement.customer.createCustomerModal.uploadLogo.title": "Logo der Organisation", "accountManagement.customer.createCustomerModal.validationError.forbiddenSymbol": "Unzulässiges Symbol", "accountManagement.customer.createCustomerModal.validationError.keyAlreadyExists": "Diese <PERSON>-ID ist bereits vergeben.", "accountManagement.customer.customerList.header": "<PERSON><PERSON>", "accountManagement.customer.customerList.label.createCustomer": "Kunde erstellen", "accountManagement.customer.customerList.placeholder": "Suche...", "accountManagement.customer.list.search.bar.placeholder": "<PERSON><PERSON> suchen", "accountManagement.customer.modal.create.label": "Neuen Kunden anlegen", "accountManagement.customers.guest.empty.state.description": "Im Moment sind keine Gastkunden verfügbar", "accountManagement.customers.guest.empty.state.header": "Im Moment sind keine Gastkunden verfügbar", "accountManagement.customers.regular.empty.state.description": "Im Moment sind keine regulären Kunden verfügbar", "accountManagement.customers.regular.empty.state.header": "<PERSON><PERSON>", "accountManagement.editCustomerDetails": "Kundendaten ändern", "accountManagement.editUserDetails": "Nutzerdaten ändern", "accountManagement.select.user.roles": "Nutzerrollen auswählen:", "accountManagement.stepper": "Schritt 1 von 2", "accountManagement.usageContract.createUsageContractModal.controlLabel.customerKey": "Kundenname", "accountManagement.usageContract.createUsageContractModal.controlLabel.dueDate": "Unterzeichnungstag", "accountManagement.usageContract.createUsageContractModal.controlLabel.title": "Nutzungsvertragstitel", "accountManagement.usageContract.createUsageContractModal.errorMessage.invalidDate": "Datum ist ungültig", "accountManagement.usageContract.createUsageContractModal.errorMessage.maxFileSize": "Die Datei ist zu groß. Die maximale Dateigröße beträgt 15 MB.", "accountManagement.usageContract.createUsageContractModal.label.control.documentId": "Unsigniertes Exemplar", "accountManagement.usageContract.createUsageContractModal.section.header": " Dokumente ", "accountManagement.usageContract.createUsageContractModal.section.header2": " Unterzeichner ", "accountManagement.user.list.search.bar.placeholder": "<PERSON>ch E-Mail-Ad<PERSON><PERSON> suchen", "accountManagement.users": "<PERSON><PERSON><PERSON>", "accountManagement.users.active": "Activ", "accountManagement.users.deactivated": "Deaktiviert", "accountManagement.users.empty-state-description": "Dieser Kunde hat im Moment keine aktiven Nutzer", "accountManagement.users.empty-state-description-deactivated": "Der Kunde hat im Moment keine deaktivierten Nutzer", "accountManagement.users.empty-state-header": "<PERSON><PERSON>", "activity.log.label": "Aktivitäten", "addButton.modal.title": "Hinzufügen", "addNewCustomerToChat.info": "ist nun Teil des Finanzierungsfalls. Würden Sie ihr Zugang zu den folgenden themenbezogenen Chats gewähren?", "addNewUserToChat.label.button": "<PERSON><PERSON>-<PERSON><PERSON><PERSON> gene<PERSON>n", "addNewUserToChat.text.footer": "Der bisherige Chat-Verlauf ist für jeden neuen Teilenehmer sichtbar.", "addReRequest.formControl": "Bitte ausfüllen", "addReRequestDescription.modal.body": " Kommentieren ", "addReRequestDescription.modal.footer": " <PERSON><PERSON><PERSON> auf Senden klicken, werden sofort alle Kundenportalnutzer dieses Finanzierungsfalls per E-mail informiert. ", "addReRequestDescription.modal.footer.realEstate": "<PERSON><PERSON><PERSON> auf Senden klicken, werden sofort alle Partnerportalnutzer dieses Finanzierungsfalls per E-Mail informiert.", "addReRequestDescription.modal.header": " Daten nochmals an<PERSON> ", "addUser.placeholder": "<PERSON><PERSON><PERSON> <PERSON>en", "address.placeholder.message": "<PERSON><PERSON><PERSON> suchen", "advanced-pie-chart.not-gathered-amount": "<PERSON>ch zu sammelnder Betrag", "allGroups.portal.actions.text": "Kundenportalaktionen", "allGroups.portal.actions.text.realEstate": "Partnerportalaktionen", "allGroupsPortalActions.groupTitle": " Sichtbarkeit im Kundenportal ", "allGroupsPortalActions.groupTitle.realEstate": "Sichtbarkeit im Partnerportal", "allGroupsPortalActions.label.requestData": "<PERSON><PERSON>", "allGroupsPortalActions.label.requestDataAgain": "<PERSON>en nochmals an<PERSON>", "allGroupsPortalActions.label.view.templates": "Vorlagen an<PERSON>hen", "allGroupsPortalActions.label.withdraw": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "already.used.email.error": "Die E-Mail-Adresse ist bereits registriert.", "amount.details.label": "Betragsangaben", "amountParticipation.sectionTitle": "Anforderungen des Finanzierungsfalls", "application.accepted.status": "Bewerbung angenommen", "application.canceled.status": "Bewerbung abgebrochen", "application.preparation.status": "In Vorbereitung", "application.rejected.status": "Bewerbung abgelehnt", "application.status.removed.from.business.case": "Entfernt", "applicationInvitation.statusTag.expired": "Einladung abgelaufen", "applicationModal.button.disconnect": "<PERSON><PERSON><PERSON>", "applicationModal.button.synchronize": "Synchroni<PERSON><PERSON>", "applicationModal.title": "Verfügbare Apps", "applicationWarningModal.button.disconnect": "<PERSON><PERSON><PERSON>", "applicationWarningModal.information": " Sind <PERSON><PERSON> sicher, dass Sie die Synchronisation von Ihrem Finanzierungsfall mit NextFolder trennen wollen? Dies wird alle hochgeladenen Dokumente aller Teilnehmer betreffen. ", "applicationWarningModal.title": "Synchronisation trennen", "applications.sortFields.amount": "Gesamt", "applications.sortFields.startedOn": "Erstellungsdatum", "applications.sortfields.customerName": "<PERSON><PERSON>ame", "applicationsList.toastError.youdonthaveActive": "<PERSON>e haben keinen Zugriff, da der Finanzierungsfall nicht mehr aktiv ist. Eine Teilnahme ist nicht möglich.", "approve.label": "<PERSON><PERSON><PERSON><PERSON>", "apps.card.notConnectedToPeople": "Verbinden", "apps.card.statusBox": "Verbunden", "apps.description": " Integrieren Sie Anwendungen, die die Mitarbeiter Ihrer Organisation in Ihren Finanzierungsfällen nutzen können. ", "apps.header": "Apps", "apps.messageUnderIcons": " Weitere Integrationen sind in Arbeit und bald verfügbar ", "archiveChat.archivedBy": "<PERSON><PERSON><PERSON><PERSON> von", "archiveChat.bilateralChat": "Bilaterale Chats", "archiveChat.dateOfArchive": " Datum der Archivierung ", "archiveChat.emptyChat": "<PERSON><PERSON> archivierten Chats vorhanden.", "archiveChat.header": "Archiv", "archiveChat.name": "Name", "archiveChat.themeChat": "Themenbezogene Chats", "authenticatedDevices.columns.date.time": "Datum/Uhrzeit", "bank.dashboard.exportExcel.general.filename.suffix": "Allgemein", "bank.dashboard.exportExcel.kpi.filename.suffix": "KPI", "basicFacility.model.basisInfo": "Basisinformationen", "basicFacility.model.loanAmmount": "Kreditbetrag", "basicIntegration.form.controlLabel": "Ordner-ID", "basicIntegration.form.headerText": " Verbinden Sie Dracoon mit Ihrem Finanzierungsfall ", "basicIntegration.guide.field.check.text": "Ordner-ID", "basicIntegration.guide.field.copyId": "Kopieren Sie die Ordner-ID und fügen Sie diese in das angezeigte Feld ein:", "basicIntegration.guide.header": " So verbinden Sie Ihr Projekt mit Dracoon\r\n", "basicIntegration.guide.label.allFiles": "<PERSON>e <PERSON>in", "basicIntegration.guide.label.copyLInk": "<PERSON>", "basicIntegration.guide.label.rightClick": "Rechtsklick", "basicIntegration.guide.label.selectFolder": "Ordner auswählen", "basicIntegration.guide.text": "Um eine erfolgreiche Verbindung zwischen Ihrem Datenraum und Dracoon her<PERSON>, müssen Si<PERSON> nach der Verknüpfung ca. 20 Minuten warten.", "bilateralModal.label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "billingAddress.city": "Stadt*", "billingAddress.customerName": "Bank (Firma)*", "billingAddress.email": "E-Mail-Adresse (Rechnungsempfänger)", "billingAddress.streetNameAndNumber": "Straße und Hausnummer*", "billingAddress.zipCode": "<PERSON><PERSON><PERSON><PERSON>*", "blankTemplate.areaDetails": "Flächen", "blankTemplate.bgfTotal": "BGF Gesamt", "blankTemplate.cadr.versionDescription": "VERSIONSBESCHREIBUNG", "blankTemplate.commercialRentalSpace": "Fläche Gewerbe", "blankTemplate.constructionYear": "<PERSON><PERSON><PERSON><PERSON>", "blankTemplate.currentJNKMTotal": "IST-JNKM Gesamt", "blankTemplate.customerInformation": "Kundeninformationen", "blankTemplate.detailedMacrolocation": "Makrolage ausführlich", "blankTemplate.detailedMicrolocation": "Mikrolage ausführlich", "blankTemplate.financingCommitmentDesiredBy": "Finanzierungszusage gewünscht bis zum", "blankTemplate.financingPurpose": "Finanzierungszweck", "blankTemplate.gastronomyRentalSpace": "Fläche Gastronomie", "blankTemplate.generalInformation": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "blankTemplate.hotelRentalSpace": "Fläche Hotel", "blankTemplate.incomeSituation": "Ertragssituation", "blankTemplate.interestRate": "Zinssatz", "blankTemplate.interestRateConsortiumPartner": "Zinsertrag Konsortialpartner", "blankTemplate.investmentLocation": "Investitionsstandort", "blankTemplate.keyDates": "Termine", "blankTemplate.landSize": "Grundstücksgröße", "blankTemplate.landValue": "Bodenwert", "blankTemplate.managementCostsTotalInProcent": "Bewirtschaftungskosten (BWK) in % Gesamt Markt-/Verkehrswert", "blankTemplate.marketValue": "Markt-/ Verkehrswert", "blankTemplate.numberOfParkingSpacesGarages": "Anzahl Stellplätze/Garagen", "blankTemplate.officeRentalSpace": "Fläche Büro", "blankTemplate.otherOperatorPropertyRentalSpace": "Fläche Sonstige Betreiberimmobilie", "blankTemplate.otherRentalSpace": "Fläche Sonstige", "blankTemplate.ownerPurchaserCaseOwner": "Eigentümer / E<PERSON><PERSON><PERSON> / <PERSON>", "blankTemplate.payoutCriteria": "Auszahlungskriterien", "blankTemplate.planDisposalProceeds": "Plan-Veräußerungserlös (EXIT-MIDCASE)", "blankTemplate.product": "Produkt", "blankTemplate.projectDescription": "Vorhabensbeschreibung", "blankTemplate.projectName": "Projektname", "blankTemplate.propertyData": "Objektdaten", "blankTemplate.propertyLocation": "Objektstandort", "blankTemplate.purchasePriceOfLand": "Kaufpreis Grundstück/Boden", "blankTemplate.rating": "Bewertung", "blankTemplate.residentialRentalSpace": "Fläche W<PERSON>", "blankTemplate.retailRentalSpace": "Fläche Handel", "blankTemplate.securities": "Sicherheiten", "blankTemplate.standardLandValuePerSqm": "Bodenrichtwert pro QM", "blankTemplate.targetJNKMCommercialMarketValue": "SOLL-JNKM Gewerbe Markt-/Verkehrswert", "blankTemplate.targetJNKMGastronomyMarketValue": "SOLL-JNKM Gastronomie Markt-/Verkehrswert", "blankTemplate.targetJNKMHotelLeaseMarketValue": "SOLL-JNKM Hotel Markt-/Verkehrswert", "blankTemplate.targetJNKMOfficeMarketValue": "SOLL-JNKM Büro Markt-/Verkehrswert", "blankTemplate.targetJNKMOtherOperatorPropertyMarketValue": "SOLL-JNKM /Pacht Sonstige Betreiberimmobilie Markt-/Verkehrswert", "blankTemplate.targetJNKMOtherRentalSpaceMarketValue": "SOLL-JNKM Sonstige Mietfläche Markt-/Verkehrswert", "blankTemplate.targetJNKMParkingSpacesGaragesMarketValue": "SOLL-JNKM Stellplätze/Garagen Markt-/Verkehrswert", "blankTemplate.targetJNKMResidentialMarketValue": "SOLL-JNKM Wohnen Markt-/Verkehrswert", "blankTemplate.targetNKMRetailMarketValue": "SOLL-JNKM Handel Markt-/Verkehrswert", "blankTemplate.valuation": "Bewertung", "blankTemplate.valueDateDesiredBy": "Valutierung gewünscht zum", "blankTemplate.website": "Website", "booleanField.no": "<PERSON><PERSON>", "booleanField.yes": "<PERSON>a", "btn.label.ignore": "Ignorieren", "buildManagement.address": "Re<PERSON>nungsadress<PERSON>", "buildManagement.navLink": "Re<PERSON><PERSON>ngen", "businessCase.DataRoom.noInformation": " Es werden aktuell keine Informationen mit Ihnen geteilt. Sobald Data Room Felder für Sie freigegeben werden, werden diese hier angezeigt.", "businessCase.DataRoom.noVisible.field": " <PERSON>s wurden noch keine Informationen geteilt.", "businessCase.accessRightsModal.visibilityChange.statusText.private": "Die Sichtbarkeit dieser Gruppe kann nicht verändert werden, da sie\r\n              nur für den Fallinhaber sichtbar ist.", "businessCase.accessRightsModal.visibilityChange.statusText.public": "Die Sichtbarkeit dieser Gruppe kann nicht verändert werden, da sie für jeden mit Zugriff auf den Finanzierungsfall sichtbar ist.\r\n              ", "businessCase.accessRightsModal.visibilityChange.tooltip.unmanaged": " <PERSON>cht verwaltet ", "businessCase.administration.dataExport.dataExportFieldConditionsNotMet": "Um den Datenexport zu erlauben, müssen alle Pflichtfelder ausgefüllt werden", "businessCase.administration.dataExport.modalSuccessNotification": "Datenexport erfolgreich aktiviert", "businessCase.administration.dataExport.noActiveCustomerTokenError": "Fehlendes oder abgelaufenes Authentifizierungs-Token. Kontaktieren Sie Ihren Administrator für Hilfe", "businessCase.application.title": " Bewerberkriterien\r\n", "businessCase.apply.conformation.container": " Bewerberkriterien ", "businessCase.apply.conformation.participationAmountContainer": " Mindest- und Höchstbeteiligungssumme ", "businessCase.apply.conformation.title": " Bitte bestätigen Sie die Bewerberkriterien ", "businessCase.applyConfirmation.button.label": "Bewerbung einreichen", "businessCase.card.moreInformation": "Weitere Firmeninformationen", "businessCase.card.navigateToCase": "Zum Fall", "businessCase.card1": "Finanzierungsvolumen", "businessCase.caseTransfer.warningModal.description": "<PERSON>lick<PERSON> <PERSON> auf <PERSON>, wenn <PERSON><PERSON> sich sicher sind, dass Sie die Gruppen für den Teilnehmer sichtbar machen wollen und die Informationen mit ihm teilen möchten, sobald er Fallinhaber dieses Finanzierungsfalls ist. Klicken Sie auf Überprüfen, um die betreffenden Gruppen einzusehen.", "businessCase.caseTransfer.warningModal.text": " Es gibt Gruppen im Data Room, welche aktuell für diesen Teilnehmer nicht sichtbar sind.\r\n", "businessCase.changeState.readOnly": "Lesezugriff", "businessCase.changeState.tooltipReadOnly": "Keine Änderungen möglich da der Fall bereits abgeschlossen wurde", "businessCase.chat.navigate": "Zum Fall", "businessCase.collaboration.modal.uiSelect.option.acceptAndApply": "Organisation Bewerbungsmöglichkeit für Finanzierungsfall schicken", "businessCase.collaboration.modal.uiSelect.option.acceptAndJoin": "Organisation direkt zur Teilnahme am Finanzierungsfall einladen", "businessCase.dashboard.button.label.assume": "<PERSON><PERSON><PERSON>", "businessCase.dashboard.button.label.next": "<PERSON><PERSON>", "businessCase.dashboard.button.label.reject": "<PERSON><PERSON><PERSON><PERSON>", "businessCase.dashboard.changeStatus.error": "Statusupdate fehlgeschlagen", "businessCase.dashboard.changeStatus.success": "Fallstatus erfolgreich geändert", "businessCase.dashboard.modals.changeBusinessCaseStateModal.label.reason": " Auswählen ", "businessCase.dashboard.modals.completeBusinessCaseModal.buttons.confirm": "Abschließen", "businessCase.dashboard.modals.completeBusinessCaseModal.message": "Sind <PERSON> sic<PERSON>, dass Sie Ihren Finanzierungsfall abschließen möchten?\r\n      Bitte wählen Sie einen Grund aus:", "businessCase.dashboard.modals.reactivateBusinessCaseModal.buttons.reactivate": "Reaktivieren", "businessCase.dashboard.modals.reactivateBusinessCaseModal.message": "Sind <PERSON> sicher, dass Sie Ihren Finanzierungsfall reaktivieren wollen?\r\n      Bitte wählen Sie einen Grund aus:", "businessCase.dataExport.allowLabel": "Erlauben", "businessCase.dataExport.allowedLabel": "<PERSON><PERSON><PERSON><PERSON>", "businessCase.dataExport.confirmModalHeader": "Erforderliche Felder für den Datenexport", "businessCase.dataExport.confirmModalTitle": "Sind <PERSON> sic<PERSON>, dass Sie den Datenexport ins Kernbankensystem für diesen Fall erlauben wollen?", "businessCase.dataExport.requiredFields.companyLabel": "Unternehmensdaten", "businessCase.dataExport.requiredFields.creditData": "Kreditdaten", "businessCase.dataExport.requiredFields.viewPageLabel": "Seite anzeigen", "businessCase.dataExport.sectionDescription": "Exportieren Sie Ihre Falldaten in das Kernbankensystem. Die Verbindung bleibt dauerhaft bestehen. Bitte vervollständigen Sie alle Daten, bevor <PERSON>. ", "businessCase.dataExport.sectionLabel": "Datenexport erlauben", "businessCase.dataExport.viewRequiredFieldsLabel": "<PERSON>hr zu den erforderlichen Daten", "businessCase.dataRoom.fieldInformationActions.subCaseLinkedTooltip": "{$START_TAG_UI_ICON}{$CLOSE_TAG_UI_ICON}", "businessCase.dataRoom.filters.documentsAndFolders": "Dokumente und Ordner", "businessCase.dataRoom.group.delete.message": "Sind Si<PERSON> sicher, dass Sie diese Gruppe löschen wollen?", "businessCase.dataRoom.noFieldsInGeneralGroup.afterBtn": ", um Daten per Drag and Drop hinzuzufügen.", "businessCase.dataRoom.noResult": "<PERSON>ine Ergebnisse gefunden. Versuchen Sie andere Schlüsselwörter oder überprüfen Sie die Rechtschreibung", "businessCase.dataRoom.noResultAfterFilter": "<PERSON><PERSON> passenden Ergebnisse. Versuchen Sie andere Filtereinstellungen.", "businessCase.dataRoom.noResultAfterSearchAndFilter": "<PERSON>ine passenden Ergebnisse. Versuchen Sie andere Filtereinstellungen oder Schlüsselwörter. ", "businessCase.dataRoom.participantRequest.pleaseFill": " <PERSON>te geben Sie Informationen an...", "businessCase.dataRoom.participantRequest.reRequestLabel": " Daten nochmals an<PERSON>", "businessCase.dataRoom.participantRequest.requestButtonTitle": " An<PERSON>ern", "businessCase.dataRoom.participantRequest.requestLabel": "<PERSON><PERSON>", "businessCase.dataRoom.participantRequest.withdrawLabel": " Datenanforderung zurückziehen", "businessCase.dataRoom.participantRequest.withdrawLabelButtonTitle": " <PERSON><PERSON><PERSON><PERSON><PERSON>", "businessCase.dataRoom.prefix": "Information zu ", "businessCase.dataRoom.suffix": "hinzufügen", "businessCase.dataRoom.text.addField": " Fügen Sie Felder durch Drag & Drop hinzu ", "businessCase.dataRoom.text.nextfolderInfo": "Unterstützte Dateiformate: PNG, JPG, PDF bis 100MB. Für einen idealen Workflow verarbeiten Sie PDF-Daten im DIN A4 Format.", "businessCase.dataRoom.text.textSub": " <PERSON><PERSON><PERSON> dafür die Felder aus der rechten Spalte in diesen Bereich. ", "businessCase.kpi.buttons.active": "Aktiv", "businessCase.kpi.buttons.disabled": "Inaktiv", "businessCase.kpi.emptyState": "<PERSON><PERSON><PERSON> diesen Fall gibt es keine inaktiven KPIs.", "businessCase.kpi.evaluationRisk": "Bewertungsris<PERSON>", "businessCase.kpi.evaluationRisk.checkExclusionCriteria": "Ausschlußkriterium prüfen", "businessCase.kpi.evaluationRisk.checkPermissibleDeviation": "Zulässige Abweichung prüfen", "businessCase.kpi.evaluationRisk.compliantWithSpecifications": "Vorgabenkonform", "businessCase.kpi.evaluationRisk.notDefined": "<PERSON><PERSON> defini<PERSON>", "businessCase.kpi.expanded.kpiField": "KPI-Feld", "businessCase.kpi.expanded.value": "Wert", "businessCase.kpi.group.costs": "<PERSON><PERSON>", "businessCase.kpi.group.debtService": "Kapitaldienst", "businessCase.kpi.group.multipliers": "Multiplikatoren", "businessCase.kpi.group.others": "<PERSON><PERSON><PERSON>", "businessCase.kpi.group.relations": "Relation<PERSON>", "businessCase.kpi.group.returns": "Ren<PERSON>en", "businessCase.kpi.kpiResult": "KPI-<PERSON><PERSON><PERSON><PERSON>", "businessCase.linkedFieldsMessage": " Alle Felder mit diesem Symbol sind mit Werten der Organisation verknüpft, die den Fall zur Verfügung gestellt hat. Klicken Sie auf das Symbol, um die Verknüpfung des Feldes aufzuheben und seinen Wert zu bearbeiten. ", "businessCase.optionCard.choose": "Auswählen", "businessCase.optionCard.duplicateCase.cardContent": "Sie können den Finanzierungsfall komplett oder nur von Ihnen ausgewählte Komponenten kopieren.", "businessCase.optionCard.financingCase.cardContent": "<PERSON>e können neue als auch bestehende Finanzierungsfälle anlegen.", "businessCase.optionCard.passingCase.cardContent": "<PERSON><PERSON>hlen Sie diesen Falltyp aus, um die Weitergabe eines Finanzierungsfalls auf neoshare zu platzieren. Sie haben anschließend die Möglichkeit, den Fall per Direkteinladung an andere Banken heranzutragen.", "businessCase.optionCard.selected": "Ausgewählt", "businessCase.overview.breakDownCard.proportion": " vom Finanzierungsvolumen ", "businessCase.overview.breakDownCard.real-estate-proportion": "vom Beantragter Finanzierungsbetrag", "businessCase.overview.managementSummary.comments": "Kommentare", "businessCase.overview.managementSummary.description": "Bitte notieren Sie hier KPI-bezogene Informationen (z. B. Aktivierung/Deaktivierung eines KPI, ein bestimmtes KPI-Ergebnis liegt außerhalb des optimalen KPI-Bereichs usw.)", "businessCase.overview.managementSummary.header": "Management Summary", "businessCase.overview.managementSummary.noCommentsMessage": "Hier werden Kommentare von I<PERSON>en oder anderen Nutzern aus Ihrer Organisation angezeigt.", "businessCase.overview.tabs.general": "Allgemein", "businessCase.overview.tabs.kpi": "KPI", "businessCase.overviewBreakDownCard.series": "Gesamtbetrag", "businessCase.participantAccess.copyAs.caseParticipant": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "businessCase.participantAccess.copyAs.invitee": "Eingeladener", "businessCase.participantAccess.rights.editAccess.text": " Bearbeitungsrechte verwalten ", "businessCase.participantAccess.rights.editAccess.text.groupVisibility": " Gruppen ", "businessCase.participantAccess.rights.editAccess.text.groupVisibility2": " Sichtbarkeit ", "businessCase.participantAccessRights.modal.editRights.editor": "<PERSON><PERSON><PERSON>", "businessCase.participantAccessRights.modal.editRights.observer": "Beobachter", "businessCase.participantRoles.cancelLabel": "Abbrechen", "businessCase.participantRoles.cancelLabel.removeParticipant": "<PERSON><PERSON>", "businessCase.participantRoles.confirmLabel": "Übergeben", "businessCase.participantRoles.confirmLabel.removeParticipant": "<PERSON><PERSON>, ich bin sicher", "businessCase.participantRoles.confirmationMessage": "Möchten Sie wirklich den Finanzierungsfall an folgende Organisation übergeben?", "businessCase.participantRoles.confirmationMessage.removeParticipant": "Sind <PERSON> sicher, dass Sie diesen Teilnehmer löschen möchten?", "businessCase.participantRoles.toast.error": "Fehler beim Aktualiseren des Teilnehmereinstellungen", "businessCase.participantRoles.toast.success": "Teilnehmereinstellungen erfolgreich aktualisiert", "businessCase.participationWithdraw.modal.title": " Die Finanzierungsbeteiligung wurde bereits gesetzt. ", "businessCase.participationWithdraw.modal.title2": " Sind Sie sich sicher, dass Sie diese zurückziehen und vom gesammelten Betrag abziehen möchten? ", "businessCase.publishedState.toast.error": "Die Marktsichtbarkeit kann auf “Öffentlich” gestellt werden nur wenn Sie Ihren Finanzierungsfall reaktivieren.", "businessCase.roles.tooltip.text": "Teilnehmer kann andere \r\n        Teilnehmer und deren Daten bezogen auf den\r\n        Finanzierungsfall einsehen.", "businessCase.type.financing": "Finanzierung", "businessCase.type.passingOn": "Weitergabe", "businessCaseCadr.text": " Der unternehmensbezogene Data Room wurde zum Finanzierungsfall hinzugefügt, allerdings sind noch keine Felder im unternehmensbezogenen Data Room vorhanden. <PERSON><PERSON><PERSON> Felder zu diesem hinzugefügt werden, werden diese hier angezeigt. ", "businessCaseCard.caseInvitationMessage": "Sie haben eine Einladung zur Teilnahme", "businessCaseCard.companyName": "Firmenname", "businessCaseDashboard.addMeToCase.confirmationText": "Sind <PERSON> sic<PERSON>, dass Si<PERSON> am Fall {$PH} teilnehmen möchten?", "businessCaseDashboard.button.label.platformManagerJoin": "<PERSON><PERSON>", "businessCaseDashboard.conformationMessageFirstPart": "möchte Ihnen den Finanzierungsfall", "businessCaseDashboard.conformationMessageSecondPart": "übergeben", "businessCaseDashboard.transferCase.confirm": "<PERSON><PERSON><PERSON>", "businessCaseDashboard.transferCase.confirmationText": "Möchten Sie diesen annehmen?", "businessCaseDataRoom.filters.fieldType": "fieldType", "businessCaseDataRoom.filters.selectPeriod": "Zeitraum auswählen", "businessCaseManagement.templateEditor.templateList.label.new": "Neue Vorlage", "businessCaseManagement.templateEditor.templateList.placeholder.search": "Suche...", "businessCaseManagement.templateEditor.templateList.selectedTemplateStatusFilter": " Aktuell sind keine Vorlagen mit diesem Status vorhanden. ", "businessCaseManagement.templateEditor.templateList.selectedTemplateStatusFilterAll": " Aktuell sind keine Vorlage vorhanden. ", "businessCaseParticipant.card.avatarText": " <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ", "businessCaseParticipant.modal.title": "Zugriffsrechte im Data Room bearbeiten", "businessCaseParticipantRoles.label.button.remove": "Teilnehm<PERSON> en<PERSON>fernen", "businessCaseParticipantRoles.label.button.request": "Finanzierungsvorhaben übergeben", "businessCaseSelectorForDuplication.button.label.cancel": "<PERSON> an<PERSON>hen", "businessCaseSelectorForDuplication.button.label.confirm": "Fall beitreten", "businessCaseSelectorForDuplication.empty": " Für Ihre Organisation sind noch keine Finanzierungsfälle verfügbar. ", "businessCaseSelectorForDuplication.header": " Wählen Sie einen Finanzierungsfall zum Kopieren aus ", "businessCaseSelectorForDuplication.myCases": "Nur meine Finanzierungsfälle", "businessCaseSelectorForDuplication.platformManager.confirmation": "Sie sind kein Mitglied des Falls {$PH}. Bitte wählen Sie eine der folgenden Optionen:", "businessCaseTemplateManagement.templateEditor.emptyMessage": " Bitte wählen Sie eine Vorlage aus. ", "businessCaseTemplateManagement.templateEditor.label.createTemplate": "<PERSON><PERSON><PERSON> anleg<PERSON>", "businessCaseTemplateManagement.templateEditor.lastUpdate.test": " Letze Änderung: ", "businessCaseTemplateManagement.templateEditor.message": " JSON-Format ist fehlerhaft. ", "businessCaseTemplateManagement.templateEditor.thisRevisionCanNotBeUpdated": " Diese Revision ist veraltet und kann nicht bearbeitet werden. ", "button.label.actions": "Aktionen", "button.label.assign": "<PERSON><PERSON><PERSON><PERSON>", "button.label.assignAndDeactivate": "Zuordnen und Deaktivieren", "button.label.assignAndDelete": "Zuordnen und Löschen", "button.label.cancel": "Abbrechen", "button.label.cancel.no": "<PERSON><PERSON>", "button.label.cancel:Abbrechen": "Abbrechen", "button.label.clearFilters": "<PERSON><PERSON>", "button.label.close": "Schließen", "button.label.closeCase": "Fall abschließen", "button.label.complete": "Fertigstellen", "button.label.confirm": "<PERSON><PERSON>, ich bin sicher", "button.label.continue": "Fortfahren", "button.label.copy": "<PERSON><PERSON><PERSON>", "button.label.delete": "Löschen", "button.label.delete.small": " Löschen", "button.label.enterEditMode": "Bearbeitungsmodus aktivieren", "button.label.exportAsExcel": "Als Excel exportieren", "button.label.i-understand": "<PERSON>ch verstehe", "button.label.moveHere": "<PERSON><PERSON><PERSON> versch<PERSON>", "button.label.next": "<PERSON><PERSON>", "button.label.reactivateCase": "Fall reaktivieren", "button.label.review": "Überprüfen", "button.label.save": "Speichern", "button.label.start": "<PERSON><PERSON><PERSON>", "button.label.understood": "Verstanden", "calendar.clear": "Löschen", "calendar.today": "<PERSON><PERSON>", "calendar.yesterday": "Gestern", "caseReassignment.required.error": "<PERSON><PERSON> Plattformmanager ausgewählt", "caseReassignment.title": "Der Nutzer {$PH} ist der einzige Nutzer für laufende Fälle innerhalb Ihrer Organisation. Weisen Sie den Fall einem Plattformmanager aus der folgenden Liste zu.", "caseStatusChangeModal.description.cancel": "Ihr Finanzierungsfall wird abgebrochen und deaktiviert.", "caseStatusChangeModal.description.complete": "Ihr Finanzierungsfall wird abgeschlossen und deaktiviert.", "caseStatusChangeModal.description.publish": "Ihr Fall bleibt unverändert bestehen.", "caseStatusChangeModal.label.cancel": "Abbrechen", "caseStatusChangeModal.label.complete": "Abschließen", "caseStatusChangeModal.label.publish": "Behalten", "caseStatusChangeModal.text": " <PERSON><PERSON><PERSON><PERSON> Si<PERSON> bitte aus, was mit dem ursprünglichen Finanzierungsfall passieren soll.\r\n", "caseTable.columns.fundingCase": "Finanzierungsfall", "caseTable.columns.inquiry": "Anfrage", "cases.applicationsTable.columns.caseType": "Falltyp", "cases.applicationsTable.emptyState.text": "<PERSON><PERSON> darstellbaren Informationen vorhanden", "cases.bank.fullEmptyState": "Ihre Organisation ist bisher an keinem Finanzierungsfall beteiligt. <PERSON><PERSON>d sie einem Fall beitritt, wird es hier ersche<PERSON>.", "cases.bewerbungenTab.header1": "Fall", "cases.bewerbungenTab.header2": " Produkt", "cases.bewerbungenTab.header3": "Volumen", "cases.bewerbungenTab.header4": "<PERSON><PERSON><PERSON><PERSON>", "cases.bewerbungenTab.header5": "Datum", "cases.bewerbungenTab.header6": "Status", "cases.businessCase.Collaboration.tab.invitationApplications": "Einladungen und Bewerbungen", "cases.casesList.emptyState.text": "<PERSON><PERSON> darstellbaren Informationen vorhanden", "cases.casesTable.columns.caseStatus": "<PERSON><PERSON><PERSON>", "cases.casesTable.columns.caseType": "Falltyp", "cases.casesTable.columns.financingType": "Finanzierungsart", "cases.emptyState": "<PERSON>e sind bisher an keinem Finanzierungsfall beteiligt. Sobald Si<PERSON> einem Fall beitreten, wird es hier ersche<PERSON>.", "cases.filter.button.right": "<PERSON><PERSON><PERSON>", "cases.filter.modal": "Status", "cases.filter.modal.case": "Fall", "cases.filter.modal.options.active": "Aktiv", "cases.filter.modal.options.canceled": "Abgebrochen", "cases.filter.modal.options.completed": "Abgeschlossen", "cases.filters.caseStatus.title": "<PERSON><PERSON><PERSON>", "cases.filters.caseStatusTag.placeholder": "Status auswählen", "cases.filters.caseType.financing.label": "Finanzierung", "cases.filters.caseType.passingOn.label": "Weitergabe", "cases.filters.caseType.title": "Falltyp", "cases.filters.financingType.corporate.label": "Unternehmensfinanzierung", "cases.filters.financingType.corporate.label.new": "Unternehmensfinanzierung", "cases.filters.financingType.miscellaneous.label": "Sonstiges", "cases.filters.financingType.realEstate.label": "Immobilienfinanzierung", "cases.filters.financingType.realEstate.label.new": "Immobilienfinanzierung", "cases.filters.financingType.title": "Finanzierungsart", "cases.filters.financingType.title.new": "Finanzierungsart", "cases.filters.invitationApplication.caseType.label": "Falltyp", "cases.filters.invitationApplication.invitationStatus.label": "Status", "cases.filters.invitationApplication.invitations.label": "Einladungen", "cases.filters.invitationApplication.status.label": "Status", "cases.filters.modal.status.applicationsTab": " Status ", "cases.filters.modal.status.invitationsTab": " Status ", "cases.filters.noResults": "<PERSON><PERSON> darstellbaren Informationen vorhanden", "cases.filters.title": "Filter", "cases.filters.totalResults": "<PERSON>älle ausgewählt", "cases.filters.totalResultsOne": "Fall ausgewählt", "cases.invitationTaB.header.case": "Fall", "cases.invitationTaB.header.caseType": "Falltyp", "cases.invitationTaB.header.customerName": "<PERSON><PERSON><PERSON><PERSON>", "cases.invitationTaB.header.date": "Datum", "cases.invitationTaB.header.financingVolume": "Volumen", "cases.invitationTaB.header.product": "Produkt", "cases.invitationTaB.header.status": "Status", "cases.invitations-list.toaster-message": "Sie haben keinen Zugriff mehr auf den Finanzierungsfall, da er nicht mehr aktiv ist. Eine Teilnahme ist nicht möglich.", "cases.invitationsApplications.fullEmptyState": "Sie haben noch keine Bewerbung verschickt oder Einladung erhalten.", "cases.invitationsTable.emptyState.text": "<PERSON><PERSON> darstellbaren Informationen vorhanden", "cases.my.fullEmptyState": "<PERSON>e sind bisher an keinem Finanzierungsfall beteiligt. Sobald Si<PERSON> einem Fall beitreten, wird es hier ersche<PERSON>.", "cases.search.placeholder": "Suche...", "cases.sidebar.bank": "Bank", "cases.sidebar.bankDefault": "Bank", "cases.sidebar.firm": "Firma", "cases.sidebar.marketplace.bank": "Bank", "cases.sidebar.marketplace.bankDefault": "Bank", "cases.sidebar.marketplace.firm": "Firma", "cases.statistics.completedCases": "Abgeschlossene Fälle", "cases.statistics.ongoingCases": "Laufende Fälle", "cases.statistics.totalParticipation": "Gesamtbeteiligung", "cases.tabNav.tab.applications": "Bewerbungen", "cases.tabNav.tab.invitations": "Einladungen", "cases.tabNav.tab.mine": "<PERSON><PERSON>", "cases.table.header.1": "Fall", "cases.table.header.3": "Volumen", "cases.table.header.4": "<PERSON><PERSON><PERSON><PERSON>", "cases.table.header.5": "Änderungsdatum", "cases.table.header.product": "Produkt", "characteristics.commissionFee": " Vermittlungskommission", "characteristics.disagio": "<PERSON><PERSON><PERSON>", "characteristics.interestRate": " <PERSON><PERSON>", "characteristics.interestRateComission": " Zinsertrag", "characteristics.rating": " Rating", "chat.exportChat.modal.downloadPdf": "PDF herunterladen", "chat.exportChat.modal.message.success": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>n", "chat.exportChat.modal.options.all": "<PERSON> an", "chat.exportChat.modal.options.custom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chat.exportChat.modal.options.lastMonth": "Letzte 30 Tage", "chat.exportChat.modal.options.lastYear": "Letzte 365 Tage", "chat.exportChat.modal.to": "Bis", "chat.exportChat.modal.warning": "<PERSON><PERSON>r den ausgewählten Zeitrahmen gibt es keine Nachrichten.", "chat.placeholder": "Schreibe etwas...", "chat.send.message.businessCaseCanceled.cantTypeMessage": "Der Chat steht Ihnen derzeit nicht zur Verfügung, da Ihr Finanzierungsfall storniert wurde.", "chat.send.message.businessCaseCompleted.cantTypeMessage": "Der Chat steht Ihnen nicht zur Verfügung, da Ihr Finanzierungsfall abgeschlossen wurde.", "chat.send.message.isTypeInBusinessCaseDisabled.cantTypeMessage": "Der Chat steht Ihnen aktuell noch nicht zur Verfügung", "chat.window.customerKey.partner": "Partner", "chatDashboard.dialog.topicChat.create": "Themenbezogenen Chat erstellen", "chatDashboard.header": " <PERSON><PERSON>", "chatDashboard.section": "Organisationsintern", "chatDashboard.sectionHeader.bilateraleCHat": "Bilaterale Chats", "chatDashboard.sectionHeader.category": " Kundenchat ", "chatDashboard.sectionHeader.topicChat": " Themenbezogene Chats", "chatWindow.actionMenuItem.mute": "Stummschalten", "chatWindow.actionMenuItem.showParticipant": "Teilnehmer anzeigen", "chatWindow.archived": "ARCHIVIERT", "chatWindow.chatActions.title": "<PERSON><PERSON><PERSON><PERSON>", "chatWindow.chatMessage.primary.text": "Shift + Enter", "chatWindow.chatMessage.secondary.text": "um eine Zeile einzufügen", "checkbox.all": "Alle", "choose.option.error.message": "<PERSON>ählen Sie eine Option, um fortzufahren", "clear.all.filters.label": "<PERSON><PERSON>", "collaboration.customerOption.notRegistered": "<PERSON>cht registriert", "collaboration.customerOption.registeredOrg": "Registrierte Organisation", "collaboration.customerOption.registeredOrgGas": "Registrierte Gastorganisation", "collaboration.customerUserInfo.email": "E-Mail-Adresse des Ansprechpartners*", "collaboration.customerUserInfo.partner.cred": " Geben Sie die Benutzerdaten Ihres Partners ein ", "collaboration.customerUserInfo.partner.label": "Organisation", "collaboration.customerUserInfo.partner.userName": "Ansprechpartner*", "collaboration.filters.selected": "ausgewählt", "collaboration.invitations.modal.header.preview": "Einladungsübersicht", "collaboration.invitations.modal.no.signers.added": "<PERSON><PERSON> hinzugefügt", "collaboration.invitations.modal.options.with.nda": "Mit Vertraulichkeitsvereinbarung", "collaboration.invitations.modal.options.without.nda": "Ohne Vertraulichkeitsvereinbarung", "collaboration.ndaStep.organization": "Organization*", "collaboration.ndaStep.participationType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "collaboration.participant.emptyState.message": "Wählen Si<PERSON> einen Teilnehmer aus der linken Spalte aus um die Kollaborationsmöglichkeiten anzupassen.", "collaboration.stateCHange.label": "<PERSON><PERSON>, ich bin sicher", "collaborationCustomer.organization": " Organisationsnutzer ", "collaborationInvitations.result.modal.failed": " Einladung erfolglos ", "collaborationInvitations.result.modal.header": " Sie haben die folgenden Organisationen eingeladen ", "collaborationInvitations.result.modal.header2": " Sie haben die folgende Organisation eingeladen ", "collaborationManagement.alertConfig": "<PERSON>te beachten Si<PERSON>, dass in Dracoon keine Änderungen an Dokumenten vorgenommen werden sollten. Dracoon spiegelt den Data Room wider und somit kann nicht garantiert werden, dass die Änderungen in Dracoon erhalten bleiben und nicht durch eine Synchronisation vom Data Room überschrieben werden. Die Synchronisation der Dokumente kann bis zu 10-15 <PERSON>uten dauern.", "collaborationManagement.caseSettings": "Falleinstellungen", "collaborationManagement.integrations": "Integrationen", "collaborationManagement.toast.success": "Ihr Finanzierungsfall wurde erfolgreich mit NextFolder synchronisiert.", "collaborationStateChange.cancel.modal": "Sind <PERSON> sicher, dass Sie Ihren Finanzierungsfall abbrechen möchten?", "collaborationStateChange.close.modal": "Sind <PERSON> sic<PERSON>, dass Sie Ihren Finanzierungsfall abschließen möchten?", "collaborationStateChange.modal.prefix": "<PERSON>d <PERSON> sic<PERSON>, dass Sie Ihren Finanzierungsfall", "collaborationStateChange.modal.suffix": "möchten?", "collaborations-page.addToBlacklist": "zu den Geblockte Organisationen hinzufügen", "collaborations-page.block": "<PERSON><PERSON><PERSON>", "collaborations-page.block.confirm.message.prefix": "Sind <PERSON> sic<PERSON>, dass Sie die Kollaboration mit", "collaborations-page.block.confirm.message.suffix": "deaktivieren wollen?", "collaborations-page.block.empty.message": "<PERSON>e von Ihnen geblockten Organisationen werden hier angezeigt.", "collaborations-page.placeholder": "<PERSON>en suchen", "collaborations-page.unblock": "Entsperren", "collaborations-page.unblock.confirm.message.prefix": "Sind <PERSON> sicher, dass Sie die Kollaboration mit dieser", "collaborations-page.unblock.confirm.message.suffix": " aktivieren wollen?", "comercialRegisterlabel": "<PERSON><PERSON><PERSON> au<PERSON>wählen", "commercialRegister.documentInformationMessage": "Dieser Bereich wird regelmäßig aktualisiert. <PERSON><PERSON><PERSON> ein neues Dokument verfügbar ist oder ein Dokument aktualisiert wird, wird es zum Download angeboten.", "commercialRegister.noDataFound": "Derzeit gibt es keine relevanten Dokumente zu diesem Unternehmen.", "commercialRegister.noDataFound.contactPerson": "Derzeit gibt es keine Ansprechpartner.", "commercialRegisterDocuments.chronological.label": "Chronologischer Handelsregisterauszug", "commercialRegisterDocuments.documentType.currentSummary": "Aktueller Handelsregisterauszug", "commercialRegisterDocuments.documentType.historicalSummary": "Historischer Handelsregisterauszug", "commercialRegisterDocuments.download.label": " Aktueller Handelsregisterauszug ", "commercialRegisterDocuments.info": " Verfügbare Dokumente ", "commercialRegisterDocuments.info2": " Verfügbare Dokumente ", "commercialRegisterDocuments.label": " Historischer Handelsregisterauszug ", "commercialRegisterDocuments.toast.error": "Die Datei konnte nicht heruntergeladen werden. Bitte versuchen Sie es später erneut", "commercialRegisterDocuments.toast.info": "Dokumentenexport gestartet. Ihr Download sollte in wenigen Sekunden verfügbar sein.", "commercialRegisterDocuments.tooltip.text": "Dokumentenübersicht", "commercialRegisterDocuments.tooltip.text2": "Dokumentenübersicht", "company.industries.1": "Getränkefachhandel", "company.industries.10": "Einzelhandel", "company.industries.100": "Stuckateure, Gipser und Verputzer", "company.industries.101": "Tiefbau und Spezialbau", "company.industries.102": "Zentralheizungs- und Lüftungsbauer", "company.industries.103": "<PERSON><PERSON><PERSON>", "company.industries.104": "Aug<PERSON>pt<PERSON><PERSON>", "company.industries.105": "Bekleidungsgewerbe", "company.industries.106": "Bäcker und Konditoren", "company.industries.107": "Chemie- und Pharmaindustrie", "company.industries.108": "Druckereien", "company.industries.109": "<PERSON><PERSON><PERSON>r", "company.industries.11": "Einzelhandel mit Bekleidung", "company.industries.110": "Gewinnung und Verarbeitung von Steinen und Erden", "company.industries.111": "Herstellung von Metallerzeugnisse", "company.industries.112": "Herstellung von elektrotechnischen Gebrauchsgütern", "company.industries.113": "Herstellung von elektrotechnischen Investitionsgütern", "company.industries.114": "Herstellung von Kraftwagenteilen", "company.industries.115": "Herstellung von Kunststoffwaren", "company.industries.116": "Herstellung von Werkzeugen", "company.industries.117": "Hörgeräteakustiker", "company.industries.118": "Holzbearbeitung", "company.industries.119": "Kraftfahrzeuggewerbe", "company.industries.12": "Einzelhandel mit Blumen und Pflanzen", "company.industries.120": "Maschinenbau", "company.industries.121": "<PERSON><PERSON><PERSON><PERSON>", "company.industries.122": "Metallbearbeitungsmaschinen", "company.industries.123": "Möbelherstellung", "company.industries.124": "Stahlverformung", "company.industries.125": "Textilgewerbe", "company.industries.126": "<PERSON><PERSON>", "company.industries.13": "Einzelhandel mit Büromaschinen, -möbeln und Organisationsmitteln", "company.industries.14": "Einzelhandel mit Haus- und Heimtextilien", "company.industries.15": "Einzelhandel mit Haushaltswaren und Heimwerkerbedarf", "company.industries.16": "Einzelhandel mit Papier, Büroartikeln und Schreibwaren", "company.industries.17": "Einzelhandel mit Uhren und Schmuckwaren", "company.industries.18": "Einzelhandel mit Unterhaltungselektronik und Fotobedarf", "company.industries.19": "Eisenwaren- und Hausrathandel", "company.industries.2": "Großhandel und Handelsvermittlung", "company.industries.20": "Elektroeinzelhandel", "company.industries.21": "Fah<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "company.industries.22": "Handel mit Krafträdern", "company.industries.23": "Fotoeinzelhandel", "company.industries.24": "Möbeleinzelhandel", "company.industries.25": "Facheinzelhandel mit Nahrungs- und Genussmitteln", "company.industries.26": "Sanitätsfachhandel", "company.industries.27": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "company.industries.28": "Sortimentseinzelhandel mit Nahrungs- und Genussmitteln", "company.industries.29": "Spielwareneinzelhandel", "company.industries.3": "Großhandel mit Nahrungs- und Genussmitteln", "company.industries.30": "Sporteinzelhandel", "company.industries.31": "Tankstellen", "company.industries.4": "Handel mit Kfz-Te<PERSON>n, -<PERSON><PERSON><PERSON><PERSON><PERSON> und -Reifen", "company.industries.5": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "company.industries.57": "<PERSON><PERSON><PERSON>uren", "company.industries.58": "<PERSON><PERSON><PERSON>", "company.industries.59": "Dienstleistungen in der Agrarwirtschaft", "company.industries.6": "Landmaschinenhandel", "company.industries.60": "Erneuerbare Energien", "company.industries.61": "Garten- und Landschaftsbau", "company.industries.62": "Gartenbau", "company.industries.63": "<PERSON><PERSON><PERSON> von <PERSON>h<PERSON> und Geflügel", "company.industries.64": "Landwirtschaftsnahe Ewerbsalternativen", "company.industries.65": "Ökologische Landwirtschaft", "company.industries.66": "Rinderhaltung", "company.industries.67": "Ärzte", "company.industries.68": "Alten- und Pflegedienste", "company.industries.69": "Architektur- und Ingenieurbüros", "company.industries.7": "Apotheken und Sanitätsfachhandel", "company.industries.70": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "company.industries.71": "Beherbergungsgewerbe", "company.industries.72": "Dienstleistungen in der Versicherungswirtschaft", "company.industries.73": "Entsorgungswirtschaft", "company.industries.74": "Facility Management", "company.industries.75": "Fahrschulen", "company.industries.76": "Friseure und Kosmetikinstitute", "company.industries.77": "Gaststättengewerbe", "company.industries.78": "Gebäudedienstleistungen", "company.industries.79": "Grundstücks- und Wohnungsverwalter", "company.industries.8": "<PERSON><PERSON><PERSON><PERSON>", "company.industries.80": "Güterbeförderung im Straßenverkehr", "company.industries.81": "Immobilienmakler", "company.industries.82": "Personenbeförderung", "company.industries.83": "Physiotherapeu<PERSON>", "company.industries.84": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "company.industries.85": "IKT-Dienstleistungen", "company.industries.86": "Spedition und Logistik", "company.industries.87": "Sport- und Freizeiteinrichtungen", "company.industries.88": "Steuer-, Rechts- und Unternehmensberater", "company.industries.89": "Vermietung beweglicher Sachen", "company.industries.9": "Drogerien und Parfümerien", "company.industries.90": "Wäschereien und Reinigungen", "company.industries.91": "Werbung und Marktforschung", "company.industries.92": "Zahnärzte und Zahntechniker", "company.industries.93": "Bau- und Möbeltischler", "company.industries.94": "<PERSON><PERSON><PERSON><PERSON>", "company.industries.95": "Elektroinstallateure", "company.industries.96": "Fußboden-, Fliesen- und Plattenleger", "company.industries.97": "Hochbau", "company.industries.98": "<PERSON><PERSON><PERSON><PERSON>, Gas- und Wasserinstallateure", "company.industries.99": "<PERSON><PERSON> <PERSON>", "company.management": "Firmenkuden", "company.sectors.1": "Sonstige", "company.sectors.2": "Industrie / Handwerk", "company.sectors.3": "Bau- und Ausbaugewerbe", "company.sectors.4": "Dienstleistungen", "company.sectors.5": "Agrarwirtschaft", "company.sectors.6": "Einzelhandel", "company.sectors.7": "G<PERSON>ßhandel", "companyANalysis.registerDocumentsTree.tooltipText": "Dokumentenübersicht", "companyAnalysis.companyBranchFilters.placeholder.cityFilter": "Alle Städte", "companyAnalysis.companyBranchFilters.placeholder.countryFilter": "Alle Länder", "companyAnalysis.companyBranchFilters.placeholder.onAppFilters": "<PERSON><PERSON><PERSON>", "companyAnalysis.companyBranchFilters.placeholder.typeFilter": "Filialtyp", "companyAnalysis.companyBranchFilters.title": " Zweigfilter ", "companyAnalysis.companyBranches": "Unternehmen", "companyAnalysis.companyBranches.deleteBranchButton": "Zweig löschen", "companyAnalysis.companyBranches.emptyInfo": " Noch keine Firmenzweige hinzugefügt. Sie können jetzt Ihren ersten Zweig hinzufügen. ", "companyAnalysis.companyBranches.heading": " Unternehmenszweige ", "companyAnalysis.companyBranches.info": " mit dieser <PERSON><PERSON>e ", "companyAnalysis.companyBranches.label.rework": "<PERSON><PERSON><PERSON>", "companyAnalysis.companyBranches.openManageBranch": "Filiale hinzufügen", "companyAnalysis.companyBranches.openManageButton": "Filiale hinzufügen", "companyAnalysis.companyBranches.placeholder": "Suche...", "companyAnalysis.companyDocumentPreview.downloadText": "Download", "companyAnalysis.companyDocumentPreview.noPreview": " <PERSON><PERSON> Vorschau für diesen Dateityp verfügbar. ", "companyAnalysis.companyInformation.navLink": "KYC", "companyAnalysis.companyInformation.navLink.action": " Aktionen ", "companyAnalysis.companyInformation.navLink.cases": "Finanzierungsfälle", "companyAnalysis.companyInformation.navLink.dataRoom": "Data Room", "companyAnalysis.companyInformation.navLink.documents": "Dokumente", "companyAnalysis.companyInformation.navLink.information": "Zusatzinformation", "companyAnalysis.companyInformationSections.companyInformationSections.title": " <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ", "companyAnalysis.companyInformationSections.informationSection.label": " Status ", "companyAnalysis.companyInformationSections.informationSection.label.additionalInfo.employees": " <PERSON><PERSON><PERSON><PERSON> ", "companyAnalysis.companyInformationSections.informationSection.label.additionalInfo.sales": " Umsätze ", "companyAnalysis.companyInformationSections.informationSection.label.additionalInfoCapital": " <PERSON><PERSON><PERSON> ", "companyAnalysis.companyInformationSections.informationSection.label.capitalCurrency": " Kapitalwährung ", "companyAnalysis.companyInformationSections.informationSection.label.corporatePurpose": " Unternehmenszweck ", "companyAnalysis.companyInformationSections.informationSection.label.deletionDate": " Löschdatum ", "companyAnalysis.companyInformationSections.informationSection.label.foundingDate": " Gründungsdatum ", "companyAnalysis.companyInformationSections.informationSection.label.identifiedLegalEntity": " Identifizierte Rechtsperson ", "companyAnalysis.companyInformationSections.informationSection.label.inputInfo": " Weitere Registernummer ", "companyAnalysis.companyInformationSections.informationSection.label.liquidStartDate": " Start der liquidation? ", "companyAnalysis.companyInformationSections.informationSection.label.liquidStatus": " Status der liquidation? ", "companyAnalysis.companyInformationSections.informationSection.label.liquidationEndDate": " Ende der liquidation? ", "companyAnalysis.companyInformationSections.informationSection.label.liquidationInformationRiskDate": " Datum der Risikoinformation ", "companyAnalysis.companyInformationSections.informationSection.label.liquidationInformationRiskHistory": " Risikoinformation (Geschichte) ", "companyAnalysis.companyInformationSections.informationSection.label.liquidationInsolvent": " Zahlungsunfähig ", "companyAnalysis.companyInformationSections.informationSection.label.taxRegistryNumber": " Steuern<PERSON>mer ", "companyAnalysis.companyInformationSections.informationSection.title": " Anmeldedaten ", "companyAnalysis.companyInformationSections.informationSection.title.informationSection": " Statistische Daten ", "companyAnalysis.companyInformationSections.informationSection.title.liquidation": " Liquidation ", "companyAnalysis.companyPortal.text": "Zum Finanzierungsfall", "companyAnalysis.sameAddressCompanies.label.registerCity": " Registerstadt: ", "companyAnalysis.sameAddressCompanies.label.registerNumber": " Registriernummer: ", "companyAnalysis.sameAddressCompanies.title": " Unternehmen unter der gleichen Adresse ", "companyBranch.filial": "Filialtyp", "companyBranch.filter.city": "Stadt", "companyBranch.filter.clearAll": "Z<PERSON>ücksetzen", "companyBranch.filter.filialType": "Filialtyp", "companyBranch.filter.land": "Land", "companyBranch.filter.placeholder": "Alle", "companyBranch.filter.save": "Speichern", "companyBranch.filter.title": "Filter", "companyBranchEditor.label.submit": " Hinzufügen ", "companyBranchEditor.title.friendsList": " <PERSON><PERSON><PERSON><PERSON> ", "companyBranchEditor.title.header": " Firmenzweig hinzufügen ", "companyBranchEditor.title.placeholder": "Name e<PERSON>ben", "companyBranchEditor.title.placeholder.companyBranchTypeDropdown": "Typ auswählen", "companyBranchEditor.title.placeholder.location": "<PERSON><PERSON><PERSON> e<PERSON>", "companyComponent.hoverTooltip.text": "Weitere Firmeninformationen", "companyDocument.toast.info": "Dokumentenexport gestartet. Ihr Download sollte in wenigen Sekunden verfügbar sein.", "companyDocumentPreview.loadingText": "Wird geladen", "companyDocuments.download.toast.error": "Die Datei konnte nicht heruntergeladen werden. Bitte versuchen Sie es später erneut", "companyDocuments.download.toast.error2": "Die Dateien konnten nicht heruntergeladen werden. Bitte versuchen Si<PERSON> es später erneut", "companyDocuments.toast.info": "Dokumentenexport gestartet. Ihr Download sollte in wenigen Sekunden verfügbar sein.", "companyEdit.table.address": "<PERSON><PERSON><PERSON>", "companyEdit.table.company": "Unternehmen", "companyEdit.table.legalForm": "Rechtsform", "companyEdit.table.nameOfCompany": "Unternehmensname", "companyEdit.table.registryCourt": "Registergericht", "companyEdit.table.registryNumber": "Registernummer", "companyEdit.toast.error": "Fehler beim erstellen des Unternehmens", "companyEdit.toast.error.deleteCompany": "Das Unternehmen kann nicht deaktiviert werden, da es in einem aktiven Finanzierungsfall verwendet wird.", "companyEdit.toast.error.deleteCompany.show": "Fehler beim deaktivieren des Unternehmens", "companyEdit.toast.error.editCompany": "Fehler beim aktualiseren des Unternehmens", "companyEdit.toast.error.toggleCompanyStatus": "Fehler beim Reaktivieren des Unternehmens", "companyEdit.toast.success": "Unternehmen erfolgreich angelegt", "companyEdit.toast.success.deleteCompany": "Unternehmen erfolgreich deaktiviert", "companyEdit.toast.success.editCompany": "Unternehmen erfolgreich aktualisiert", "companyEdit.toast.success.editCompany2": "Unternehmen erfolgreich deaktiviert", "companyEdit.toast.success.toggleCompanyStatus": "Unternehmen erfolgreich reaktiviert", "companyField.addCustomerTooltip": "Als Garantiegeber hinzufügen", "companyField.prefix": "Unternehmen hinzufügen", "companyField.revisions.button.label": "Revision wiederherstellen", "companyField.revisions.changesBy": " Änderungen vorgenommen von ", "companyField.revisions.newVersion": "Neuste Version", "companyField.revisions.showRevision": "Revision anzeigen", "companyField.revisions.uiTable.noDataMessage": "Keine Revisionen verfügbar", "companyGraph.UBOList.buttonTooltip": "Liste wirt. <PERSON><PERSON><PERSON><PERSON> an<PERSON>", "companyGraph.UBOList.empty": "In diesem Netzwerk konnten keine wirt. Berechtigte gefunden werden", "companyGraph.UBOList.notAvailablePercentage": "k.A.", "companyGraph.UBOList.title": "Liste wirt. <PERSON>", "companyGraph.addConectionModal.header": "Verknüpfung hinzufügen ", "companyGraph.addConectionModal.selectAutoCompany": "Unternehmen aus der Liste für automatische Verknüpfung auswählen.", "companyGraph.addConectionModal.selectManualCompany": "Verknüpfung manuell in der Netzwerkansicht erstellen.", "companyGraph.connectEdge.companyNode": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "companyGraph.connectEdge.connectTo": "Verknüpfen mit", "companyGraph.connectEdge.connectionNode": "Verknüpfungsknoten", "companyGraph.connectEdge.header": "Verknüpfung erstellen", "companyGraph.connectEdge.infoMessage": "<PERSON><PERSON> dieser Knoten mehrfach vorhanden ist, werden neue Verknüpfungen auf sämtliche Instanzen angewendet.", "companyGraph.connectEdge.ownership": "Anteile %", "companyGraph.connectEdge.ownership.validationError": "Sie überschreiten 100% der Anteile", "companyGraph.createCompany.existingCompany": "Das Unternehmen existiert bereits in der Grafik. Um dieses Unternehmen zu nutzen, können Sie im Diagrammbereich Verbindungen erstellen.", "companyGraph.createCompany.modal.address": "<PERSON><PERSON><PERSON>*", "companyGraph.createCompany.modal.companyName": "Unternehmensname", "companyGraph.createCompany.modal.header": "Netzwerkknoten für das Unternehmen hinzufügen", "companyGraph.createCompany.modal.legalForm": "Rechtsform*", "companyGraph.createCompany.modal.registerCity": "Registergericht", "companyGraph.createCompany.modal.registryNumber": "Registernummer*", "companyGraph.documents.tabDisabledTooltip": "Reiter enthält keine Dokumente", "companyGraph.documents.tableHeader": "HR-<PERSON>ummer", "companyGraph.documents.tableHeader.addressLocation": "<PERSON><PERSON>", "companyGraph.documents.tableHeader.company": "Unternehmen", "companyGraph.documents.tableHeader.lastUpdate": "Letzte Aktualisierung", "companyGraph.documents.tableHeader.level": "<PERSON><PERSON><PERSON>", "companyGraph.documents.tableHeader.registerCourt": "Registergericht", "companyGraph.documentsTab": "Netzwerk-Dokumente", "companyGraph.edge.deleteFromGraph": "Möchten Sie diese Verknüpfung löschen?", "companyGraph.expandNetworkButtonTooltip.collapse": "Erweiterte Netzwerkansicht schließen", "companyGraph.expandNetworkButtonTooltip.expand": "Dieses Netzwerk erweitern", "companyGraph.expandNode.button": "Dieses Netzwerk erweitern", "companyGraph.expandNode.button.disabled": "Netzwerk kann mit aktivem Filter nicht erweitert werden", "companyGraph.filter.infoNote": "Die Filter gelten nur für die aktuell geladenen Ebenen der Ansicht. Erweitern Sie die Ansicht, um tiefere Ebenen zu filtern.", "companyGraph.filterNode.button": "Filter", "companyGraph.fullScreen.button": "Vollbildmodus", "companyGraph.gallery.limitReached.toastMessage": "Sie haben die maximale Anzahl für Netzwerkknoten in der Galerie ausgeschöpft", "companyGraph.graphFilters.button.label.clearAll": "Z<PERSON>ücksetzen", "companyGraph.graphFilters.button.label.save": "<PERSON><PERSON><PERSON>", "companyGraph.graphFilters.entities.corporateEntity": "Juristische Person", "companyGraph.graphFilters.entities.individualOwner": "Privatperson", "companyGraph.graphFilters.entities.ubo": "<PERSON><PERSON><PERSON>", "companyGraph.graphFilters.label.entityType": "Entitätstyp", "companyGraph.graphFilters.label.role": "<PERSON><PERSON>", "companyGraph.graphFilters.label.sharesPercentage": "Ante<PERSON> (%)", "companyGraph.graphFilters.noResult": "<PERSON>s gibt keine Ergeb<PERSON>, die Ihren Suchkriterien entsprechen. Löschen Sie die angewandten Filter oder wählen Sie andere Kriterien aus.", "companyGraph.graphFilters.title": "Filter", "companyGraph.graphToolbar.uboListButton": "<PERSON><PERSON><PERSON>", "companyGraph.groupLabel.tooltip.aboveNineteen": " mit Anteilen höher 19%", "companyGraph.groupLabel.tooltip.betweenFiveAndNineteen": " mit Anteilen zwischen 5% und 19%", "companyGraph.groupLabel.tooltip.holding": " An diesen Unternehmen beteiligt", "companyGraph.groupLabel.tooltip.owner": "{$PH} Anteilseigner", "companyGraph.groupLabel.tooltip.owners": "{$PH} Anteilseigner", "companyGraph.groupLabel.tooltip.underFivePercent": " mit Anteilen niedriger 5%", "companyGraph.hasChanges.btn.review": "Überprüfen", "companyGraph.hasChanges.message": "Es gibt neue Änderungen im Firmennetzwerk", "companyGraph.hideMinimap.tooltip": "Minimap verbergen", "companyGraph.holding": "Beteiligung", "companyGraph.loadingMessage": "Die Daten für die Darstellung werden gerade geladen. <PERSON>e können die Se<PERSON> schließen, und neoshare and<PERSON><PERSON><PERSON><PERSON> nutzen, ohne den Vorgang zu unterbrechen. <PERSON>te beachten Sie, dass der Vorgang längere Zeit in Anspruch nehmen kann.", "companyGraph.modal.proceed": "Fortfahren", "companyGraph.modifyGraph.commentTitle": "Kommentar (optional)", "companyGraph.modifyGraph.commentTitle.placeholder": "<PERSON>e können hier weitere Informationen hinzufügen", "companyGraph.noCompany.info.bottomLink": "Hauptknoten erstellen", "companyGraph.noCompany.info.bottomPrefix": "<PERSON><PERSON><PERSON>n Si<PERSON> den ersten Hauptknoten, um das Netzwerk manuell aufzubauen.", "companyGraph.noCompany.info.bottomSuffix": "oder indem Sie auf “Netzwerkknoten erstellen” auf der Seitenleiste klicken.", "companyGraph.noCompany.info.top": "Keine Informationen zum Unternehmensnetzwerk verfügbar", "companyGraph.node.deleteAllFromGallery": "Sind <PERSON> sic<PERSON>, dass Si<PERSON> alle Netzwerkknoten aus der Galerie entfernen möchten?", "companyGraph.node.deleteFromGallery": "Möchten Sie diesen Netzwerkknoten aus der Galerie entfernen?", "companyGraph.node.deleteFromGraph": "Möchten Sie diesen Netzwerkknoten aus der Darstellung entfernen?", "companyGraph.other": "Sonstiges", "companyGraph.paginator.compactRangeLabel": "{$PH} von {$PH_1} Änderungen", "companyGraph.person.modal.header": "Natürliche Person hinzufügen", "companyGraph.resetGraph.buttonTooltip": "Alle geladenen Darstellungen zurücksetzen", "companyGraph.resetGraphVisualization.toastMessage": "Darstellungen erfolgreich zurückgesetzt", "companyGraph.review.btn.updated": "<PERSON><PERSON><PERSON><PERSON>", "companyGraph.review.header": "Änderungen überprüfen", "companyGraph.review.new": "G<PERSON><PERSON>ndert", "companyGraph.review.newEdge": "Neue Komponente", "companyGraph.review.newNode": "Neuer Netzwerkknoten", "companyGraph.review.old": "Aktuell", "companyGraph.review.paginator.prefix": "von", "companyGraph.review.paginator.suffix": "Änderungen", "companyGraph.review.removeNode": "Gelöscht", "companyGraph.review.toastMessage": "Änderungen erfolgreich durchgeführt", "companyGraph.reviewChanges.accept": "<PERSON><PERSON><PERSON>", "companyGraph.reviewChanges.acceptAll": "<PERSON>e annehmen", "companyGraph.reviewChanges.endDate": "Enddatum:", "companyGraph.reviewChanges.startDate": "Anfangsdatum:", "companyGraph.showMinimap.tooltip": "Minimap anzeigen", "companyGraph.sideMenu.createNode": "Netzwerkknoten hinzufügen", "companyGraph.sideMenu.createNode.person": "<PERSON><PERSON><PERSON><PERSON>", "companyGraph.sideMenu.createNodeInfo": "<PERSON><PERSON><PERSON> einen neuen Netzwerkknoten erstellt haben, wird er hier angezeigt.", "companyGraph.sideMenu.detailsTab.company.labels.address": "<PERSON><PERSON><PERSON>", "companyGraph.sideMenu.detailsTab.company.labels.registerCity": "Registergericht", "companyGraph.sideMenu.detailsTab.company.labels.registrationNumber": "Registernummer", "companyGraph.sideMenu.detailsTab.naturalPerson.labels.address": "<PERSON><PERSON><PERSON>", "companyGraph.sideMenu.detailsTab.naturalPerson.labels.birthDate": "Geburtsdatum", "companyGraph.sideMenu.detailsTab.naturalPerson.labels.location": "<PERSON><PERSON>", "companyGraph.sideMenu.detailsTab.naturalPerson.labels.wealthIndex": "Vermögen", "companyGraph.sideMenu.galleryEmpty": "Galerie ist leer", "companyGraph.sideMenu.galleryEmpty.subheading": "<PERSON>s gibt keine neuen oder nicht zugewiesenen Netzwerkknoten", "companyGraph.sideMenu.header.gallery": "Galerie", "companyGraph.sideMenu.legend.headerTitle.kind": "KATEGORIE", "companyGraph.sideMenu.legend.headerTitle.symbol": "SYMBOL", "companyGraph.sideMenu.legend.headerTitle.type": "ROLLE", "companyGraph.sideMenu.legendTab.address": "<PERSON><PERSON><PERSON>", "companyGraph.sideMenu.legendTab.birthDate": "Geburtsdatum", "companyGraph.sideMenu.legendTab.business": "Unternehmen", "companyGraph.sideMenu.legendTab.edited": "Bearbeitet", "companyGraph.sideMenu.legendTab.nonUbo": "<PERSON><PERSON> wirt. <PERSON>", "companyGraph.sideMenu.legendTab.shareholder": "Gesellschafter", "companyGraph.sideMenu.legendTab.ubo": "<PERSON><PERSON><PERSON>", "companyGraph.sideMenu.legendTab.uboShareholder": "Gesellschafter (wirt. berechtigt)", "companyGraph.sideMenu.toggles.compactView": "Kompaktansicht", "companyGraph.sideMenu.toggles.expandSecondLevel": "Zweite Ebene erweitern", "companyGraph.sideMenu.toggles.expandThirdLevel": "Dritte Ebene erweitern", "companyGraph.sideMenu.toggles.highlightEdits": "Änderungen hervorheben", "companyGraph.sideMenu.toggles.showUBOPath": "<PERSON><PERSON><PERSON> Be<PERSON><PERSON> er<PERSON>tern", "companyGraph.toolbar.title": "Netzwerkansicht", "companyGraph.viewDocument.button": "Dokument anzeigen", "companyGraph.viewDocument.button.tooltip": "Handelsregisterauszug", "companyGraph.zoomSlider.tooltip": "Zum Zoomen ziehen", "companyInformation.navLInk.branches": "Zweigstellen", "companyInformation.switch.dataRoom": "Bearbeitungsmo<PERSON>", "companyInformationFields.informationText": " Wählen Sie die Data Room Felder aus, welche Sie in Ihren unternehmensbezogenen Data Room kopieren möchten ", "companyPortal.companyPortalDashboard.heading": " Es wurde Ihnen noch kein Finanzierungsfall zugewiesen. Sobald Ihnen ein Finanzierungsfall zugewiesen wird, werden Sie darüber per E-Mail benachrichtigt. ", "companyPortal.companyPortalDashboard.title": " Dashboard ", "components.alertPanel.title": " Upload erfolgreich", "composite.field.numeric": "<PERSON><PERSON><PERSON><PERSON>", "composite.field.percent": "Prozentsatz", "confirm.accept.application.message": "Sind Si<PERSON> sicher, dass Sie diese Bewerbung annehmen wollen?", "confirm.accept.invitation.message": "Sind <PERSON> sicher, dass Sie diese Einladung annehmen wollen?", "confirm.reject.application.message": " Sind Si<PERSON> sicher, dass Sie diese Bewerbung ablehnen wollen?", "confirm.reject.invitation.message": "Sind <PERSON> sicher, dass Sie diese Einladung ablehnen wollen?", "confirmEmptyCadr.closeReject": "<PERSON>cht teilen", "confirmEmptyCadr.closeSuccess": "Trotzdem teilen", "confirmEmptyCadr.infoText": " Es muss mindestens eine Gruppe mit einem Feld vorhanden sein, welches nicht privat ist, damit der unternehmensbezogene Data Room für Ihre Partner nicht leer erscheint. Möchten Sie den unternehmensbezogenen Data Room trotzdem teilen? ", "confirmEmptyCadr.title": "Es sind keine Informationen vorhanden, welche geteilt werden können.", "confirmationDialog.initialCaseOwner.checkbox.label.new": "<PERSON><PERSON><PERSON>", "confirmationDialog.initialCaseOwner.checkbox.label.other": "<PERSON><PERSON>", "confirmationDialog.initialCaseOwner.title": " Den unternehmensbezogenen Data Room teilen mit: ", "confrim.cancel.invitation.message": "Sind <PERSON> sicher, dass Sie diese Einladung abbrechen wollen?", "congrats.label": "Herzlichen Glückwunsch!", "contactDownloadDialogProp.cancelLabel": "Ältere Version herunterladen", "contactDownloadDialogProp.confirmLabel": "Schließen", "contactDownloadDialogProp.message": "<PERSON>te versuchen Si<PERSON> es später noch einmal oder laden Sie eine ältere Version dieser Datei herunter.", "contactDownloadDialogProp.title": "Die Datei wird noch bearbeitet.", "contactPeople.errorMessage.emailOrganisationTaken.prefix": "Diese E-Mail-Adresse wird bereits für ein Nutzerkonto von", "contactPeople.errorMessage.emailTaken.company.suffix": "verwendet. Bitte geben Si<PERSON> eine andere E-Mail-Adresse an oder entfernen Sie den Ansprechspartner.", "contactPeople.errorMessage.emailTaken.prefix": "Diese E-Mail-Adresse wird bereits für einen Ansprechpartner von", "contactPeople.errorMessage.emailTaken.suffix": "verwen<PERSON>. Bitte geben Si<PERSON> eine andere E-Mail-Adresse an.", "contactPeople.roles.CEO": "CEO", "contactPeople.roles.COO": "COO", "contactPeople.roles.boardMember": "Vorstandsmitglied", "contactPeople.roles.clerk": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "contactPeople.roles.commercialManager": "Kaufm. Leiter", "contactPeople.roles.consultant": "Unternehmensberater", "contactPeople.roles.corporateFinanceAdvisor": "Corporate Finance Berater", "contactPeople.roles.customerRepresentative": "Bankvertreter", "contactPeople.roles.generalManager": "<PERSON><PERSON><PERSON>", "contactPeople.roles.managingDirector": "Geschäftsführer", "contactPeople.roles.other": "<PERSON><PERSON>", "contactPeople.roles.shareholder": "Gesellschafter", "contactPeople.roles.supervisorBoardMember": "Aufsichtsrat", "contactPeople.roles.treasurer": "Treasurer", "contactPeople.roles.vicePresident": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "contactPeople.rolesCFO": "CFO", "contactPerson.actionMenu.label.addUser": "Als Ansprechpartner hinzufügen", "contactPerson.actionMenu.label.removeUser": "Ansprechpartner entfernen", "contactPerson.card.label.removeUser": "<PERSON><PERSON><PERSON> entfernen", "contract-create.modal.search.placeholder": "<PERSON><PERSON>", "contract.contractDetailsModal.annulmentButton": "<PERSON><PERSON><PERSON><PERSON>", "contract.create.modal.backButton": "Zurück", "contract.create.modal.closeButton": "Schließen", "contract.create.modal.firstStep": "Vertrag mit oder ohne Bezug zu einem Finanzierungsvorhaben anlegen", "contract.create.modal.firstStep.button.forward": "<PERSON><PERSON>", "contract.create.modal.firstStep.withoutFininacingVolume": "<PERSON>ne Finanzierungsvorhaben", "contract.create.modal.fourthStep": "Überprüfen und absenden", "contract.create.modal.fourthStep.button.forward": "Senden", "contract.create.modal.header.ID": "ID", "contract.create.modal.header.finiancingVolume": "Finanzierungsvolumen", "contract.create.modal.header.signers": "Unternehmen", "contract.create.modal.label.enterUsernameOrEmail": "Nutzername oder E-Mail eingeben", "contract.create.modal.label.search": "<PERSON><PERSON>", "contract.create.modal.noSearchResultsFound": " <PERSON><PERSON> Suchergebnisse gefunden ", "contract.create.modal.notRegistered.nonNeoshare.user.email.message": "E-Mail nicht registriert. Nutzen Sie dieses Formular, um Nutzer einzuladen.", "contract.create.modal.openDocumentEditor": "Signaturplatzierung", "contract.create.modal.participant": " <PERSON><PERSON><PERSON><PERSON><PERSON>", "contract.create.modal.secondStep": "Unterzeichner zuweisen*", "contract.create.modal.secondStep.button.forward": "<PERSON><PERSON>", "contract.create.modal.secondStep.contractTitle": "Vertragstitel", "contract.create.modal.secondStep.fileInput.errorMessage.tooBigSize": "Die Datei ist zu groß. Die maximale Dateigröße beträgt 15 MB.", "contract.create.modal.secondStep.signingDay": "Unterzeichnungstag", "contract.create.modal.secondStep.signingDay.errorMessage.invalidDate": "Datum ist ungültig", "contract.create.modal.secondStep.unsignedExample": "Unsigniertes Exemplar", "contract.create.modal.signer": " Unterzeichner ", "contract.create.modal.stepThird.search.placeholder": "Name oder E-Mail-Adresse", "contract.create.modal.stepThird.title": " Unterzeichner zuweisen\r\n", "contract.create.modal.thirdStep": "Vertragsdaten eintragen", "contract.create.modal.thirdStep.button.forward": "Überprüfen", "contract.create.modal.title": " <PERSON><PERSON><PERSON> ", "contract.filter.header.label1": " Status\r\n", "contract.filter.header.label2": " Unternehmen\r\n", "contract.filter.label.without": "<PERSON>ne Finanzierungsvorhaben", "contract.heading.noContractsOption": " Aktuell sind keine Verträge vorhanden. ", "contract.status.AuthoritativeCopy": "<PERSON><PERSON><PERSON>", "contract.status.autoResponded": "<PERSON><PERSON><PERSON>", "contract.status.completed": "<PERSON><PERSON><PERSON>", "contract.status.correct": "<PERSON><PERSON><PERSON>", "contract.status.declined": "<PERSON><PERSON><PERSON>", "contract.status.deleted": "<PERSON><PERSON><PERSON>", "contract.status.delivered": "<PERSON><PERSON><PERSON>", "contract.status.draft": "<PERSON><PERSON><PERSON>", "contract.status.faxPending": "<PERSON><PERSON><PERSON>", "contract.status.filter.all": "Alle Unterlagen", "contract.status.filter.completed": "<PERSON><PERSON><PERSON>", "contract.status.filter.extendedPending": "<PERSON><PERSON><PERSON>", "contract.status.filter.pending": "<PERSON><PERSON><PERSON>", "contract.status.filter.voided": "<PERSON><PERSON><PERSON><PERSON>", "contract.status.sent": "<PERSON><PERSON><PERSON>", "contract.status.signed": "<PERSON><PERSON><PERSON>", "contract.status.successfullySigned": "<PERSON>ir<PERSON>", "contract.status.template": "<PERSON><PERSON><PERSON>", "contract.status.test": "<PERSON><PERSON><PERSON>", "contract.status.timedOut": "<PERSON><PERSON><PERSON>", "contract.status.transferCompleted": "<PERSON><PERSON><PERSON>", "contract.status.voided": "<PERSON><PERSON><PERSON><PERSON>", "contract.table.list.header.createdBy": "<PERSON><PERSON><PERSON><PERSON> von", "contract.table.list.header.createdOn": "<PERSON><PERSON><PERSON><PERSON> von", "contract.table.list.header.financingVolume": "Finanzierungsvolumen", "contract.table.list.header.signingDay": "Unterzeichnungstag", "contract.table.list.header.status": "Status", "contract.table.list.header.title": "Vertragstitel", "contractCard.reject.description": "Die Namen oder E-Mail-Adressen der unterzeichnenden Parteien stimmen nicht mit dem aktuellen Nutzer überein. Daher sind Si<PERSON> nicht berechtigt, dieses Dokument zu signieren. Um die Sicherheit und Authentizität des Dokuments zu gewährleisten, lehnen Sie das Dokument bitte jetzt ab.", "contractCard.reject.title": "Ungültiger Zugriff des Unterzeichners", "contractList.placeholder.search": "Suche...", "contractList.search.button.label": "<PERSON><PERSON><PERSON><PERSON>", "contractList.title": "Verträge', fragment: 'contracts", "contractManagement.contract.businessCaseSelectableItem.id": " ID ", "contractManagement.contract.businessCaseSelectableItem.title": "Finanzierungsvolumen", "contractManagement.contract.showUserWithCustomerInModal.label.dateSigned": " Unterzeichnet am ", "contractManagement.contract.showUserWithCustomerInModal.label.pending": "<PERSON><PERSON><PERSON>", "contractManagement.contract.showUserWithCustomerInModal.label.voided": "<PERSON><PERSON><PERSON><PERSON>", "contractManagement.contract.showUserWithCustomerInModal.tooltip": " <PERSON><PERSON><PERSON><PERSON> am ", "contractManagement.exportChat": "Chat exportieren", "contractManagement.template.templateCard.createdBy": "<PERSON><PERSON><PERSON><PERSON> von", "contractManagement.template.templateCard.dropDownItem1": "Umbenennen", "contractManagement.template.templateCard.dropDownItem2": "Archivieren", "contractManagement.template.templateCard.dropDownItem3": "Reaktivieren", "contractManagement.template.templateCard.dropDownItem4": "Veröffentlichen", "contractManagement.template.templateCard.updatedBy": "<PERSON><PERSON><PERSON><PERSON><PERSON> von", "contractManagement.template.templateModal.create": "Neue Vorlage erstellen", "contractManagement.template.templateModal.description.label": "Beschreibung", "contractManagement.template.templateModal.header": "Vorlageninformationen bearbeiten", "contractManagement.template.templateModal.title.label": " Vorlagentitel * ", "contractManagement.template.templateSideFilter.label.all": "Alle Vorlagen", "contractManagement.template.templateSideFilter.label.archived": "<PERSON><PERSON><PERSON><PERSON>", "contractManagement.template.templateSideFilter.label.draft": "<PERSON><PERSON><PERSON><PERSON>", "contractManagement.template.templateSideFilter.label.published": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "contractManagement.visualizeContractPreview.dt1": " Unternehmen ", "contractManagement.visualizeContractPreview.dt2": " Finanzierungsvolumen ", "contractManagement.visualizeContractPreview.dt3": "ID", "contractManagement.visualizeContractPreview.label.button": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "contractManagement.visualizeContractPreview.title": " Finanzierungsfalldetails ", "contractManagement.visualizeContractPreview.title.dt1": " Vertragstitel ", "contractManagement.visualizeContractPreview.title.dt2": " Unterzeichnungstag ", "contractManagement.visualizeContractPreview.title.dt3": " Unsigniertes Exemplar ", "contractManagement.visualizeContractPreview.title2": " Vertragsdaten ", "contractManagement.visualizeContractPreview.title3": " Unterzeichner ", "contractSignersList.label.tooltipContent": " Unterzeichnet am ", "contractSignersList.label.tooltipContent.delivered": " <PERSON><PERSON><PERSON><PERSON> am ", "contractStatus.envelopeExpired": "Abgelaufen", "contractStatus.filter.label.notSignedByMe": "Von mir zu signieren", "contractStatus.filter.label.pending": "Alle", "contractStatus.filter.label.signedByMe": "<PERSON> mir signiert", "contractStatus.manuallyVoided": "<PERSON><PERSON> Anforderer annulliert", "contractStatus.voidedBySigner": "Vom Unterzeichner annulliert", "contracts.contractDetailsModal.contractDataHeader": " Vertragsdaten ", "contracts.contractDetailsModal.contractTitle": "Vertragstitel", "contracts.contractDetailsModal.signedExample": "Signiertes Exemplar", "contracts.contractDetailsModal.signerAndStatus": " Unterzeichner und Status ", "contracts.contractDetailsModal.signingDate": "Unterzeichnungstag", "contracts.contractDetailsModal.title": " Vertragsdaten ", "contracts.contractDetailsModal.title.financingVolume": "Finanzierungsvolumen", "contracts.contractDetailsModal.title.finiancingDetails": " Finanzierungsfalldetails ", "contracts.contractDetailsModal.title.signers": "Unternehmen", "contracts.contractDetailsModal.unsignedExample": "Unsigniertes Exemplar*", "contracts.contractsDetailsModal.downloadButton": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "copyInformation.fields.modal.copy": "<PERSON><PERSON><PERSON>", "copyInformation.fields.modal.text": " Wählen Sie eine Gruppe aus, in welche die Data Room Felder kopiert werden sollen ", "copyInformationModal.groupSelectOptions.copyDataRoomFieldsText": "Data Room Felder kopieren", "copyInformationModal.groupSelectOptions.existingGroupText": "Bestehende Gruppe", "copyInformationModal.groupSelectOptions.newGroupText": "Neue Gruppe", "coreBanking.expirationDate": "Verfallsdatum", "coreBanking.generateToken.buttonLabels.generate": "<PERSON><PERSON><PERSON><PERSON>", "coreBanking.generateToken.daysUntilExpiryText": "Tage bis Verfall", "coreBanking.generateToken.description": "Das Token hat eine Nutzungsdauer von 30 Tagen.", "coreBanking.generateToken.header": "Integrations-Token erstellen", "coreBanking.generateToken.information": "Das Token wird zur Authentifizierung genutzt, um auf die neoshare-Daten zuzugreifen. Bitte geben Si<PERSON> ein Verfallsdatum an, um fortzufahren.", "coreBanking.generateToken.maximumDaysToExpirationText": "Die Aktivierungsdauer muss zwischen 1 und {$PH} Tagen liegen", "coreBanking.generateToken.modal.information": "Die Erstellung eines neuen Tokens ersetzt automatisch das vorherige Token.", "coreBanking.generateToken.modal.question": "M<PERSON>chten Si<PERSON> fortfahren?", "coreBanking.generateToken.tokenExpiredMessage": "Ihr Token ist nicht länger gültig. Erstellen Si<PERSON> bitte ein neues Token, um den Transfer an das Kernbankensystem fortzusetzen.", "coreBanking.viewToken.activeTokenLabel": "Aktives Token", "coreBanking.viewToken.buttonLabels.generateNewToken": "Neues Token erstellen", "coreBanking.viewToken.information": "<PERSON>e können das Token nur einmal herunterladen. Wenn Sie ein neues erstellen, wird das vorherige Token automatisch deaktiviert.", "coreBanking.viewToken.tokenExpirationDaysLeftMessage": "Diese To<PERSON> verfällt in {$PH} Tagen. Bitte erneuern Sie das Token, um den Authentifizierungsprozess für das Kernbankensystem fortzusetzen. ", "coreBanking.viewToken.tokenExpirationIsTodayMessage": "Dieses Token verfällt heute. Bitte erneuern Sie das Token, um den Authentifizierungsprozess für das Kernbankensystem fortzusetzen.", "coreBanking.viewToken.tokenExpirationOnlyOneDayLeftMessage": "Dieses Token verfällt in 1 Tag. Bitte erneuern Sie das Token, um den Authentifizierungsprozess für das Kernbankensystem fortzusetzen.", "coreBankingLabel": "Kernbankensystem", "coreBankingMessage": "Mit der Erstellung eines Tokens ermöglichen Sie den Informationstransfer zwischen neoshare und dem Kernbankensystem. Das Token wird zur Authentifizierung genutzt, um auf die neoshare-Daten zuzugreifen.", "create.invitation.addUnregisteredUser": "Unregistrierten Nutzer hinzufügen", "create.invitation.label": "Einladung erstellen", "create.nonNeoshare.user.additional.information": "Zusätzliche Informationen hinzufügen", "createBusinessCase.cancelBusinessCase": " Sind <PERSON> sicher, dass Sie die Erstellung des Falls abbrechen möchten? ", "createBusinessCase.cancelBusinessCase.button.label.cancel": "<PERSON><PERSON>", "createBusinessCase.cancelBusinessCase.button.label.confirm": "<PERSON><PERSON>, ich bin sicher", "createBusinessCase.checkbox.label.isCADRLinked": "Unternehmensbezogenen Data Room verlinken", "createBusinessCase.createBusinessCaseFormFooter.button.label.back": "Zurück", "createBusinessCase.createBusinessCaseFormFooter.button.label.back2": "Zurück", "createBusinessCase.createBusinessCaseFormFooter.button.label.continue": "<PERSON><PERSON>", "createBusinessCase.createBusinessCaseFormFooter.button.label.submit2": "<PERSON><PERSON><PERSON><PERSON>", "createBusinessCase.errorMessage.selectOneCheckbox": "Es muss mindestens eine Komponente ausgewählt sein", "createBusinessCase.formField": "Unternehmen*", "createBusinessCase.formField.caseType": " Falltyp* ", "createBusinessCase.formField.label": " Finanzierungsvolumen* ", "createBusinessCase.formField.label.signer": "<PERSON><PERSON><PERSON>", "createBusinessCase.formField.own-capital": " Eigenkapital ", "createBusinessCase.formField.real-estate-total-financing-volume": "Gesamtinvestitionskosten", "createBusinessCase.formField.select": "Nutzer ihrer Organisation hinzufügen", "createBusinessCase.formField.total-financing-volume": "Finanzierungsvolumen", "createBusinessCase.header": " Finanzierungsfall anlegen ", "createBusinessCase.info.message": " Sie können den Finanzierungsfall komplett oder nur von Ihnen ausgewählte Komponenten kopieren. <PERSON>ür nicht ausgewählte Komponenten werden die Standardeinstellungen für die Erstellung eines neuen Falls angewandt. ", "createBusinessCase.label": " Vorlage*", "createBusinessCase.label.buttonLink": "Unternehmen anlegen", "createBusinessCase.ownCapacityField.valueBiggerThanTotalError": "Das Eigenkapital darf nicht größer sein als die Gesamtinvestitionskosten", "createBusinessCase.placeholder.select": "<PERSON><PERSON><PERSON><PERSON> suchen", "createBusinessCase.subHeader": " Bitte wählen Sie eine der folgenden Optionen aus: ", "createBusinessCase.text": "Alle gespeicherten Vorlagen von I<PERSON> und Ihren Kollegen finden Si<PERSON> hier.", "createBusinessCase.toast.error": "Etwas ist schiefgelaufen.", "createBusinessCase.toolTip.text": "Sie entscheiden welches Unternehmen aus Ihrer Liste mit Firmenkunden finanziert werden soll.", "createCompany.select.addTagText": "Unternehmen hinzufügen", "createCompany.select.placeholder": "<PERSON><PERSON><PERSON><PERSON> suchen", "createCustomer.modal.errorMessage.bicAlreadyExists": "Dieser BIC ist bereits vergeben.", "createInvoice.button": "Zahlung tätigen", "createInvoice.form.info": " Nachdem Sie die Zahlung tätigen, erstellen wir eine automatische Rechnung. ", "createInvoice.form.invoiceLast48H": " <PERSON><PERSON><PERSON>, Ihre letzte freiwillige Zahlung liegt weniger als 48 Stunden zurück. {$LINE_BREAK}", "createInvoice.form.title": "<PERSON><PERSON> e<PERSON>ben", "createInvoice.span": " <PERSON>e haben am", "createInvoice.span2": "eine Zahlung von", "createInvoice.span3": "getätigt .", "createInvoice.subTitle": " Mit einer freiwilligen Zahlung unterstützen Sie die Arbeit unserers Startups. ", "createInvoice.title": " Sie möchten unsere Arbeit unterstützen? ", "createUsageContract.missingUsageSignersAlert": "Nutzungsvertragszeichner ist für diesen Kunden nicht verfügbar.", "createUsageContractModal.title": " Nutzungsvertrаgsdaten ", "createUserModal.title": " Nutzerdaten ", "createUserModal.validationError.noRoleGiven": "Bitte wählen Sie mindestens eine Rolle", "currentPasswordIncorrect": "Ungültige passwort!", "customer.list.snapshot.description": "Liste der von Ihnen ausgewählten Kunden und der Organisationen, mit denen diese zusammenarbeiten", "customer.list.snapshot.tab.addedByMe": "<PERSON> hinzugefüg", "customer.list.snapshot.tab.autoAdded": "Automatisch", "customer.list.snapshot.tab.autoAddedCustomers": "Automatisch hinzugefügte Kunden", "customer.type.bank": "Bank", "customer.type.corporate": "Corporate", "customer.type.fsp": "Finanzdienstleister", "customer.type.immo": "<PERSON><PERSON>o", "customerCollaboration.tooltip.notAllowToBlock": "Derzeit können Sie eine Zusammenarbeit mit dieser Organisation nicht blockieren, da Sie gemeinsam an aktiven Fällen arbeiten", "customerDetails.addressField.address": "<PERSON><PERSON><PERSON>", "customerDetails.deleteLogoPhoto.tooltip": "Logo löschen", "customerDetails.deleteLogoPhoto.tooltip:Logo löschen": "Logo löschen", "customerDetails.mfa.description": "Aus Sicherheitsgründen empfehlen wir, diese Funktion aktiviert zu lassen.", "customerDetails.mfa.disabled.tooltip": "Feature derzeit nicht verfügbar.", "customerDetails.mfa.header": "Multifaktor-Authentifizierung", "customerDetails.mfa.options.description": "Wählen Sie die Funktionen die Sie aktivieren möchten.", "customerDetails.numberField.money": "Bilanzsumme", "customerDetails.numberField.numberOfEmployees": "<PERSON><PERSON><PERSON> der Mitarbeiter", "customerDetails.textField.name": "Organisationsname", "customerDetails.uploadLogoPhoto.tooltip": "Logo hochladen", "customerDetailsForm.title": "Logo der Organisation", "customerDetailsForm.toast.error": "Kundendaten konnten nicht gespeichert werden. Bitte überprüfen Sie Ihre Eingabe.", "customerDetailsForm.toast.success": "Aktion erfolgreich ausgeführt.", "customerDetailsForm.website": "Webseite", "customerKpi.tab.description": " <PERSON>n Si<PERSON> einen KPI auf Organisationsebene aktivieren oder deaktivieren, gilt er automatisch für alle Finanzierungsfälle innerhalb der Organisation.", "customerKpi.tab.label": "KPI verwalten", "customerList.EditDetails": "Daten ä<PERSON>n", "customerList.disableUserLabel": "<PERSON><PERSON><PERSON>akti<PERSON>", "customerList.disableUserTooltip": "<PERSON>ser Nutzer kann nicht deaktiviert werden, da es in dieser Organisation derzeit keinen Plattformmanager gibt.", "customerList.enableUserLabel": "Nutzer aktivieren", "customerList.userUpdate.toast.failure": "Ein Fehler ist aufgetreten. Versuchen Sie es bitte erneut.", "customerList.userUpdate.toast.success": "Nutzer erfolgreich aktualisiert", "customerMasterData.kpiSettings.list.actualAnnualNetColdRentPerSquareMeterOfRentalSpace": "IST-JNKM Gesamt pro QM", "customerMasterData.kpiSettings.list.annualNetColdRentPerSquareMeterOfRentalSpaceTarget": "SOLL-JNKM Gesamt pro QM", "customerMasterData.kpiSettings.list.capitalServiceSurplusShortfallWithActualAnnualNetColdRent": "Kapitaldienst Über-/Unterdeckung bei IST-JNKM", "customerMasterData.kpiSettings.list.capitalServiceSurplusShortfallWithTargetAnnualNetColdRent": "Kapitaldienst Über-/Unterdeckung bei SOLL-JNKM", "customerMasterData.kpiSettings.list.costsPerQmGrossFloorArea": "Kosten QM BGF zu GIK", "customerMasterData.kpiSettings.list.costsPerQmRentalSpace": "Kosten QM Mietfläche zu GIK", "customerMasterData.kpiSettings.list.debtRatio": "Fremdkapitalquote zu GIK", "customerMasterData.kpiSettings.list.dscr": "DSCR", "customerMasterData.kpiSettings.list.equityMultiplierForContractedSalesProceeds": "Eigenkapitalmultiplikator bei kontrahiertem Veräußerungserlös", "customerMasterData.kpiSettings.list.equityMultiplierForPlannedSalesProceeds": "Eigenkapitalmultiplikator bei Plan-Veräußerungserlös", "customerMasterData.kpiSettings.list.equityRatio": "Eigenkapitalquote zu GIK", "customerMasterData.kpiSettings.list.ltcAtTotalInvestmentCosts": "LTC zu GIK", "customerMasterData.kpiSettings.list.ltvAtContractedSalesProceeds": "LTV zu kontrahiertem Veräußerungserlös", "customerMasterData.kpiSettings.list.ltvAtEvaluationMortgageLendingValue": "LTV zum Beleihungswert", "customerMasterData.kpiSettings.list.ltvAtMarketValue": "LTV zum Verkehrswert", "customerMasterData.kpiSettings.list.ltvAtPlannedSalesProceeds": "LTV zum Plan-Veräußerungserlös", "customerMasterData.kpiSettings.list.ltvAtRealisticSalesProceeds": "LTV zu marktgängigem (Projekt-)Veräußerungserlös", "customerMasterData.kpiSettings.list.multiplierOfActualAnnualNetColdRentToEvaluationMortgageLendingValue": "Multiplikator IST-JNKM zum Beleihungswert", "customerMasterData.kpiSettings.list.multiplierOfActualAnnualNetColdRentToMarketValue": "Multiplikator IST-JNKM zum Verkehrswert", "customerMasterData.kpiSettings.list.multiplierOfActualAnnualNetColdRentToTotalInvestmentAmount": "Multiplikator IST-JNK<PERSON> zu GIK", "customerMasterData.kpiSettings.list.multiplierOfTargetAnnualNetColdRentToContractedSalesProceeds": "Multiplikator SOLL-JNKM zu kontrahiertem Veräußerungserlös", "customerMasterData.kpiSettings.list.multiplierOfTargetAnnualNetColdRentToEvaluationMortgageLendingValue": "Multiplikator SOLL-JNKM zum Beleihungswert", "customerMasterData.kpiSettings.list.multiplierOfTargetAnnualNetColdRentToMarketValue": "Multiplikator SOLL-JNKM zum Verkehrswert", "customerMasterData.kpiSettings.list.multiplierOfTargetAnnualNetColdRentToPlannedSalesProceeds": "Multiplikator SOLL-JNKM zum Plan-VE", "customerMasterData.kpiSettings.list.multiplierOfTargetAnnualNetColdRentToRealisticSalesProceeds": "Multiplikator SOLL-JNKM zu marktgängigem Veräußerungserlös", "customerMasterData.kpiSettings.list.multiplierOfTargetAnnualNetColdRentToTotalInvestmentAmount": "Multiplikator SOLL-JNKM zu GIK", "customerMasterData.kpiSettings.list.planProjectProfitAmount": "Plan-Projektgewinn €", "customerMasterData.kpiSettings.list.planProjectProfitPercentage": "Plan-Projektgewinn %", "customerMasterData.kpiSettings.list.pricePerQmLand": "Preis QM Grundstück zum Kaufpreis GS", "customerMasterData.kpiSettings.list.projectProfitContractedSalesProceedsAmount": "Projektgewinn bei kontrahiertem Veräußerungserlös €", "customerMasterData.kpiSettings.list.projectProfitContractedSalesProceedsPercentage": "Projektgewinn bei kontrahiertem Veräußerungserlös %", "customerMasterData.kpiSettings.list.projectProfitWithContractedSalesProceedsAmount": "Projektgewinn bei kontrahiertem Veräußerungserlös €", "customerMasterData.kpiSettings.list.projectProfitWithContractedSalesProceedsPercentage": "Projektgewinn bei kontrahiertem Veräußerungserlös %", "customerMasterData.kpiSettings.list.projectProfitWithRealisticSalesProceedsAmount": "Projektgewinn bei marktgängigem Veräußerungserlös €", "customerMasterData.kpiSettings.list.projectProfitWithRealisticSalesProceedsPercentage": "Projektgewinn bei marktgängigem Veräußerungserlös %", "customerMasterData.kpiSettings.list.propertyRelatedDebtServiceCapacityActual": "Objekt-Kapitaldienstfähigkeit IST-JNKM", "customerMasterData.kpiSettings.list.propertyRelatedDebtServiceCapacityTarget": "Objekt-Kapitaldienstfähigkeit SOLL-JNKM", "customerMasterData.kpiSettings.list.propertyRelatedUnsecuredPortionAt60EvaluationMortgageLendingValue": "Objektbezogener Blankoanteil bei 60% BW", "customerMasterData.kpiSettings.list.propertyRelatedUnsecuredPortionAt60MortgageLendingValue": "Objektbezogener Blankoanteil bei 60% BW", "customerMasterData.kpiSettings.list.propertyRelatedUnsecuredPortionAt70EvaluationMortgageLendingValue": "Objektbezogener Blankoanteil bei 70% BW", "customerMasterData.kpiSettings.list.propertyRelatedUnsecuredPortionAt70MortgageLendingValue": "Objektbezogener Blankoanteil bei 70% BW", "customerMasterData.kpiSettings.list.propertyRelatedUnsecuredPortionAt80EvaluationMortgageLendingValue": "Objektbezogener Blankoanteil bei 80% BW", "customerMasterData.kpiSettings.list.propertyRelatedUnsecuredPortionAt80MortgageLendingValue": "Objektbezogener Blankoanteil bei 80% BW", "customerMasterData.kpiSettings.list.realEastate.ltvAtMarketValue": "LTV zum Marktwert", "customerMasterData.kpiSettings.list.realEastate.multiplierOfActualAnnualNetColdRentToMarketValue": "Multiplikator IST-JNKM zum Marktwert", "customerMasterData.kpiSettings.list.realEastate.multiplierOfTargetAnnualNetColdRentToMarketValue": "Multiplikator SOLL-JNKM zum Marktwert", "customerMasterData.kpiSettings.list.realEastate.returnAtMarketValueBasedOnActualAnnualNetRentalIncomeTotal": "Rendite - Marktwert zur  IST-JNKM", "customerMasterData.kpiSettings.list.realEastate.returnAtMarketValueBasedOnTargetAnnualNetRentalIncomeTotal": "Rendite - Marktwert zur  SOLL-JNKM", "customerMasterData.kpiSettings.list.realEastate.roeAtPlannedProjectProfit": "ROE bei Plan-Veräußerungserlös", "customerMasterData.kpiSettings.list.rentalSpaceFactorSpaceEfficiency": "Mietflächenfaktor / Flächeneffizienz", "customerMasterData.kpiSettings.list.returnAtMarketValueBasedOnActualAnnualNetRentalIncomeTotal": "Rendite - Verkehrswert zur  IST-JNKM", "customerMasterData.kpiSettings.list.returnAtMarketValueBasedOnTargetAnnualNetRentalIncomeTotal": "Rendite - Verkehrswert zur  SOLL-JNKM", "customerMasterData.kpiSettings.list.returnOfTotalInvestmentCostsBasedOnActualAnnualNetRentalIncomeTotal": "Rendite-GIK zur IST-JNKM", "customerMasterData.kpiSettings.list.returnOfTotalInvestmentCostsBasedOnTargetAnnualNetRentalIncomeTotal": "Rendite-GIK zur SOLL-JNKM", "customerMasterData.kpiSettings.list.returnOnContractedSalesProceedsToTargetAnnualNetColdRent": "Rendite-KontrahierterVE zur SOLL-JNKM", "customerMasterData.kpiSettings.list.returnOnPlanSalesProceedsToTargetAnnualNetColdRent": "Rendite-PlanVE zur SOLL-JNKM", "customerMasterData.kpiSettings.list.returnOnRealisticSalesProceedsToTargetAnnualNetColdRent": "Rendite-MarktgängigerVE zur SOLL-JNKM", "customerMasterData.kpiSettings.list.roeAtContractedSalesProceeds": "ROE bei kontrahiertem Veräußerungserlös", "customerMasterData.kpiSettings.list.roeAtPlannedProjectProfit": "ROE bei Plan-Projektgewinn", "customerMasterData.kpiSettings.list.roeAtPlannedSalesProceeds": "ROE bei Plan-Veräußerungserlös", "customerMasterData.kpiSettings.list.standardLandValueForComparison": "Bodenrichtwert pro QM zum Vergleich", "customerMasterData.kpiSettings.list.targetAnnualNetColdRentPerSquareMeterOfRentalSpace": "SOLL-JNKM Gesamt pro QM", "customerMasterData.kpiSettings.list.utilizationOfDebtServiceLimitForAnnualNetRentalIncomeTotalActual": "Auslastung Kapitaldienstgrenze bei IST-JNKM", "customerMasterData.kpiSettings.list.utilizationOfDebtServiceLimitForAnnualNetRentalIncomeTotalTarget": "Auslastung Kapitaldienstgrenze bei SOLL-JNKM", "customerMasterData.pageTitle": "Stammdaten", "customerMasterData.tab.blocked": "Geblockte Organisationen", "customerMasterData.tab.collaboration": "Kollaborationen", "customerMasterData.tab.collaboration.placeholder.search": "<PERSON>en filtern...", "customerMasterData.tab.collaboration.table.column.employees": "<PERSON><PERSON><PERSON><PERSON>", "customerMasterData.tab.collaboration.table.column.name": "Name", "customerMasterData.tab.collaboration.table.column.status": "Marktplatzkollaboration", "customerMasterData.tab.collaboration.table.column.totalAssets": "Bilanzsumme", "customerMasterData.tab.customerDetails": "Organisationsdaten", "customerMasterData.tab.customerDetails.label.button.rework": "Daten bearbeiten", "customerMasterData.tab.customerDetails.label.button.save": "Daten speichern", "customerMasterData.tab.customerDetails.lastUpdated": " Letzte Änderung am", "customerMasterData.tab.kpiSettings": "KPI-Einstellungen", "customerOrganisationData.kpiSettings.enabled": "Aktivieren", "customerOrganisationData.kpiSettings.kpiRanges.error.firstMaxHighest": "Der MAX-Wert des ersten Intervalls darf nicht höher als {$PH} sein.", "customerOrganisationData.kpiSettings.kpiRanges.error.firstMaxHighestDesc": "Der MAX-Wert des ersten Intervalls muss {$PH} oder niedriger sein.", "customerOrganisationData.kpiSettings.kpiRanges.error.firstMaxLowest": "Der MAX-Wert des ersten Intervalls darf nicht niedriger als {$PH} sein.", "customerOrganisationData.kpiSettings.kpiRanges.error.firstMaxLowestDesc": "Der MAX-Wert des ersten Intervalls muss {$PH} oder höher sein.", "customerOrganisationData.kpiSettings.kpiRanges.error.firstMinHighest": "Der MIN-Wert des ersten Intervalls darf nicht höher als {$PH} sein.", "customerOrganisationData.kpiSettings.kpiRanges.error.firstMinHighestDesc": "Der MIN-Wert des ersten Intervalls muss {$PH} oder niedriger sein.", "customerOrganisationData.kpiSettings.kpiRanges.error.firstMinLowest": "Der MIN-Wert des ersten Intervalls darf nicht niedriger als {$PH} sein.", "customerOrganisationData.kpiSettings.kpiRanges.error.firstMinLowestDesc": "Der MIN-Wert des ersten Intervalls muss {$PH} oder höher sein.", "customerOrganisationData.kpiSettings.kpiRanges.error.notAscending": "Die Werte der Intervallfelder (für alle drei Intervalle) müssen in aufsteigender Reihenfolge angegeben werden", "customerOrganisationData.kpiSettings.kpiRanges.error.notDescending": "Die Werte der Intervallfelder (für alle drei Intervalle) müssen in absteigender Reihenfolge angegeben werden", "customerOrganisationData.kpiSettings.kpiRanges.error.required": "Alle Intervallfelder müssen ausgefüllt werden", "customerOrganisationData.kpiSettings.kpiRanges.error.secondMaxHighest": "Der MAX-Wert des zweiten Intervalls darf nicht höher als {$PH} sein.", "customerOrganisationData.kpiSettings.kpiRanges.error.secondMaxHighestDesc": "Der MAX-Wert des zweiten Intervalls muss {$PH} oder niedriger sein.", "customerOrganisationData.kpiSettings.kpiRanges.error.secondMaxLowest": "Der MAX-Wert des zweiten Intervalls darf nicht niedriger als {$PH} sein.", "customerOrganisationData.kpiSettings.kpiRanges.error.secondMaxLowestDesc": "Der MAX-Wert des zweiten Intervalls muss {$PH} oder höher sein.", "customerOrganisationData.kpiSettings.kpiRanges.error.thirdMaxHighest": "Der MAX-Wert des dritten Intervalls darf nicht höher als {$PH} sein.", "customerOrganisationData.kpiSettings.kpiRanges.error.thirdMaxHighestDesc": "Der MAX-Wert des dritten Intervalls muss {$PH} oder niedriger sein.", "customerOrganisationData.kpiSettings.kpiRanges.error.thirdMaxLowest": "Der MAX-Wert des dritten Intervalls darf nicht niedriger als {$PH} sein.", "customerOrganisationData.kpiSettings.kpiRanges.error.thirdMaxLowestDesc": "Der MAX-Wert des dritten Intervalls muss {$PH} oder höher sein.", "customerOrganisationData.kpiSettings.kpiRanges.header": "Intervalle festlegen", "customerOrganisationData.kpiSettings.kpiRanges.rangeDirection.ascending": "Die Intervalle müssen in aufsteigender Reihenfolge festgelegt werden.", "customerOrganisationData.kpiSettings.kpiRanges.rangeDirection.descending": "Die Intervalle müssen in absteigender Reihenfolge festgelegt werden.", "customerOrganisationData.kpiSettings.kpiRanges.rangeLabel.attention": "Ausschlußkriterium prüfen", "customerOrganisationData.kpiSettings.kpiRanges.rangeLabel.moderate": "Zulässige Abweichung prüfen", "customerOrganisationData.kpiSettings.kpiRanges.rangeLabel.optimal": "Vorgabenkonform", "customerOrganisationData.kpiSettings.kpiRanges.saveChanges.failure": "Es ist ein Fehler aufgetreten, bitte versuchen Si<PERSON> es später noch einmal.", "customerOrganisationData.kpiSettings.kpiRanges.saveChanges.success": "Änderungen erfolgreich gespeichert.", "customerOrganisationData.pageTitle": "Organisationsdaten", "customerOrganisationData.security.mfa.authOptions": "Authentifizierungsoptionen", "customerOrganisationData.security.mfa.description": "Aus Sicherheitsgründen empfehlen wir, diese Funktion aktiviert zu lassen.", "customerOrganisationData.security.mfa.enable": "Aktivieren", "customerOrganisationData.security.mfa.header": "Multifaktor-Authentifizierung", "customerOrganisationData.tab.blocked": "Geblockte Organisationen", "customerOrganisationData.tab.masterData": "Stammdaten", "customerOrganisationData.tab.security": "Sicherheit", "customerRole.editor": "<PERSON><PERSON><PERSON>", "customerRoles.observer": "Beobachter", "customerStatus.Guest": "Gas<PERSON>", "customerStatus.Regular": "Regulär", "customerStatus.model.contractSent": "Initialer NV - gesendet", "customerStatus.model.contractSignedByAll": "Initialer NV - von allen signiert", "customerStatus.model.contractSignedByCustomer": "Initialer NV - von Bank signiert", "customerStatus.model.initialized": "<PERSON><PERSON><PERSON>", "customerStatus.model.initializing": "Initialisieren", "customerTable.columns.createdOn": "Erstellt am", "customerTable.columns.customerKey": "Kunden ID", "customerTable.columns.customerType": "Kundentyp", "customerTable.columns.name": "Kundenname", "customerTable.columns.source": "<PERSON><PERSON>", "customerTable.columns.sourceType": "Quellentyp", "customerTemplates.tabs.companyAndCaseTemplates": "Unternehmensbezogene und Fallvorlagen", "customerTemplates.tabs.documentTemplates": "Dokumentenvorlagen", "customers.sourceType.mainSystem": "Hauptsystem", "dasboard.businessCase.leaderContactInfo.mobilePhone": "Mobil", "dasboard.businessCase.leaderContactInfo.phone": "Telefon", "dashbaord.bank.partnersClientsWidget.clients": "<PERSON><PERSON>", "dashbaord.bank.partnersClientsWidget.financingPartner": "Finanzierungspartner", "dashboard.BusinessCase.administration.fundingCase.button.conclude": "Abschließen", "dashboard.BusinessCase.administration.fundingCase.button.reactivate": "Reaktivieren", "dashboard.BusinessCase.administration.fundingCase.description": " Sie haben die Möglichkeit Ihren Finanzierungsfall abzuschließen oder unter “Abbrechen” zu deaktivieren. Ein Fallabschluss ist jedoch nur möglich, wenn die Marktsichtbarkeit Ihres Finanzierungsfalls auf “Privat” gestellt wurde. ", "dashboard.BusinessCase.administration.fundingCase.isReactivatable.description": " Sie haben die Möglichkeit Ihren bereits abgeschlossenen oder abgebrochenen Finanzierungsfall zu reaktivieren. Ein Projekt zu reaktivieren ist jedoch nur möglich, wenn die Marktsichtbarkeit Ihres Finanzierungsfalls auf “Privat” gestellt wurde. ", "dashboard.BusinessCase.administration.sectionText.duplicateCase": " Sie können den Finanzierungsfall komplett oder nur von Ihnen ausgewählte Komponenten kopieren. ", "dashboard.activityLogs.header.recentChanges": " Neueste Änderungen ", "dashboard.activityLogs.noParticipationYet": " <PERSON><PERSON><PERSON> an einem Finanzierungsfall teilnehmen, werden alle relevanten Aktivitäten hier angezeigt. ", "dashboard.activityLogs.weekdays.friday": "Freitag", "dashboard.activityLogs.weekdays.monday": "Montag", "dashboard.activityLogs.weekdays.saturday": "Samstag", "dashboard.activityLogs.weekdays.sunday": "Sonntag", "dashboard.activityLogs.weekdays.thursday": "Don<PERSON><PERSON>", "dashboard.activityLogs.weekdays.tuesday": "Dienstag", "dashboard.activityLogs.weekdays.wednesday": "Mittwoch", "dashboard.analytic.noClassified": "nicht e<PERSON>uft", "dashboard.bank.analytics.assetClasses.title": " Assetklasse ", "dashboard.bank.analytics.assetClasses.tooltipPrefix": "Betrag", "dashboard.bank.analytics.assetClasses.tooltipSuffix": "des Gesamtkreditbetrags aller Assetklassen bei Immobilienfinanzierungen", "dashboard.bank.analytics.cases": "<PERSON><PERSON><PERSON>", "dashboard.bank.analytics.certified.title": "Zertifiziert", "dashboard.bank.analytics.energy.title": " Energieeffizienzklasse ", "dashboard.bank.analytics.historicalInvestment.title": " Historische Entwicklung des Gesamtkreditbetrags ", "dashboard.bank.analytics.nonCertified.title": "Nicht zertifiziert", "dashboard.bank.analytics.ownInvestmentAmount.title": " Kreditbetrag nach Objekttyp ", "dashboard.bank.analytics.title": " Analytische Daten ", "dashboard.bank.analytics.totalInvestmentAmount.ownInvestmentAmount.tooltip": "Betrag", "dashboard.bank.analytics.totalInvestmentAmount.totalOwnInvesmentAmount.tooltip": "des Gesamtkreditbetrags aller Objekttypen bei Immobilienfinanzierungen", "dashboard.bank.casesTable.general.columns.assetClass": "Assetklasse", "dashboard.bank.casesTable.general.columns.caseStatus": "<PERSON><PERSON><PERSON>", "dashboard.bank.casesTable.general.columns.caseType": "Falltyp", "dashboard.bank.casesTable.general.columns.company": "Unternehmen", "dashboard.bank.casesTable.general.columns.creationDate": "Erstellungsdatum", "dashboard.bank.casesTable.general.columns.ecoreScoring": "ECORE-SCORING", "dashboard.bank.casesTable.general.columns.energyEfficiencyClass": "Energieeffizienzklasse", "dashboard.bank.casesTable.general.columns.esgCertified": "ESG-Zertifiziert", "dashboard.bank.casesTable.general.columns.financingType": "Finanzierungsart", "dashboard.bank.casesTable.general.columns.objectAddress": "Objektstandort", "dashboard.bank.casesTable.general.columns.objectName": "Projektname", "dashboard.bank.casesTable.general.columns.objectType": "Objekttyp", "dashboard.bank.casesTable.general.columns.ownInvestmentAmount": "Kreditbetrag", "dashboard.bank.casesTable.kpi.columns.caseStatus": "<PERSON><PERSON><PERSON>", "dashboard.bank.casesTable.kpi.columns.caseType": "Falltyp", "dashboard.bank.casesTable.kpi.columns.creationDate": "Erstellungsdatum", "dashboard.bank.casesTable.kpi.columns.financingType": "Finanzierungsart", "dashboard.bank.casesTable.kpi.columns.ltcAtMarketValue": "LTV zum Verkehrswert", "dashboard.bank.casesTable.kpi.columns.ltcAtTotalInvestmentAmount": "LTC zu GIK", "dashboard.bank.casesTable.kpi.columns.multiplierMarketValueActual": "Multiplikator IST-JNKM zum Verkehrswert", "dashboard.bank.casesTable.kpi.columns.multiplierMarketValueTarget": "Multiplikator SOLL-JNKM zum Verkehrswert", "dashboard.bank.casesTable.kpi.columns.multiplierTotalInvestmentAmountActual": "Multiplikator IST-JNK<PERSON> zu GIK", "dashboard.bank.casesTable.kpi.columns.multiplierTotalInvestmentAmountTarget": "Multiplikator SOLL-JNKM zu GIK", "dashboard.bank.casesTable.kpi.columns.objectAddress": "Objektstandort", "dashboard.bank.casesTable.kpi.columns.projectName": "Projektname", "dashboard.bank.casesTable.kpi.columns.projectProfitPercentage": "Projektgewinn bei realistischem VE %", "dashboard.bank.casesTable.kpi.columns.propertyDebtForAnnualIncomeActual": "Objekt-Kapitaldienstfähigkeit IST-JNKM", "dashboard.bank.casesTable.kpi.columns.propertyDebtForAnnualIncomeTarget": "Objekt-Kapitaldienstfähigkeit SOLL-JNKM", "dashboard.bank.casesTable.kpi.columns.propertyUnsecuredPortion60Percent": "Objektbezogener Blankoanteil bei 60% BW", "dashboard.bank.casesTable.kpi.columns.propertyUnsecuredPortion70Percent": "Objektbezogener Blankoanteil bei 70% BW", "dashboard.bank.casesTable.kpi.columns.propertyUnsecuredPortion80Percent": "Objektbezogener Blankoanteil bei 80% BW", "dashboard.bank.casesTable.kpi.columns.rentalSpaceFactor": "Mietflächenfaktor / Flächeneffizienz", "dashboard.bank.casesTable.kpi.columns.utilizationOfDebtActual": "Auslastung Kapitaldienstgrenze bei IST-JNKM", "dashboard.bank.casesTable.kpi.columns.utilizationOfDebtTarget": "Auslastung Kapitaldienstgrenze bei SOLL-JNKM", "dashboard.bank.financingPartnerClientWidget.client.cases": "<PERSON><PERSON><PERSON>", "dashboard.bank.financingPartnerClientWidget.client.name": "Name", "dashboard.bank.financingPartnerClientWidget.client.totalLoanAmount": "Kreditbetrag", "dashboard.bank.historicalDevelopmentInvestmentAmountWidget.title": " Historische Entwicklung des Gesamtkreditbetrags\r\n", "dashboard.bank.portfolio.realEstateKpiLocationOverviewWidget.allConsortialCases": "Alle Konsortialfälle", "dashboard.bank.portfolio.realEstateKpiLocationOverviewWidget.cases": "<PERSON><PERSON><PERSON>", "dashboard.bank.portfolio.realEstateKpiLocationOverviewWidget.totalLtv": "LTV Gesamt", "dashboard.bank.portfolio.realEstateKpiLocationOverviewWidget.totalOwnInvestmentAmount": "Gesamtkreditbetrag", "dashboard.bewerbungenTab.header1": "Fall", "dashboard.bewerbungenTab.header5": "Bewerbungsdatum", "dashboard.businessCase.Collaboration.tab.applications": " Bewerbungen ", "dashboard.businessCase.Collaboration.tab.invitationApplications": "Einladungen und Bewerbungen", "dashboard.businessCase.Collaboration.tab.invitations": " Einladungen ", "dashboard.businessCase.Collaboration.tab.myParticipation": " <PERSON><PERSON> ", "dashboard.businessCase.actions": "Aktionen", "dashboard.businessCase.administration.button.label": "<PERSON><PERSON><PERSON> hinzufügen", "dashboard.businessCase.administration.button.label.viewDataRoom": " Unternehmensbezogenen Data Room anzeigen ", "dashboard.businessCase.administration.companyContactUsers.addUserLabel": " <PERSON><PERSON><PERSON> hinzufügen ", "dashboard.businessCase.administration.companyContactUsers.doesNotHaveUsers": " Es wurde noch keinem Nutzer Zugang zum Kundenportal gegeben. Bitte berechtigen Sie diese dazu in ", "dashboard.businessCase.administration.companyContactUsers.doesNotHaveUsers.realEstate": "Es wurde noch keinem Nutzer Zugang zum Partnerportal gegeben. Bitte\r\n    berechtigen Sie diese dazu in", "dashboard.businessCase.administration.companyContactUsers.hasCompanyUsersAndNoUsersAssigned.firstPart": " Tauschen Sie Informationen mit den Nutzern des Kundenportals aus. ", "dashboard.businessCase.administration.companyContactUsers.hasCompanyUsersAndNoUsersAssigned.firstPart.realEstate": "Tauschen Sie Informationen mit den Nutzern des Partnerportals aus.", "dashboard.businessCase.administration.companyContactUsers.hasCompanyUsersAndNoUsersAssigned.secondPart": " W<PERSON><PERSON>en Sie diese hier aus… ", "dashboard.businessCase.administration.companyContactUsers.hasUsers": " Kundenportalnutzer ", "dashboard.businessCase.administration.companyContactUsers.hasUsers.realEstate": "Partnerportalnutzer", "dashboard.businessCase.administration.contactPersonCard.label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dashboard.businessCase.administration.contactPersonTitle": " <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ", "dashboard.businessCase.administration.customerUsersTitle": " <PERSON><PERSON><PERSON> ", "dashboard.businessCase.administration.deleteFaq": "Möchten Sie diese Frage löschen?", "dashboard.businessCase.administration.dracoonStatusBox": "Verbunden", "dashboard.businessCase.administration.dracoonText": " Apps mit Ihrem Finanzierungsfall synchronisieren ", "dashboard.businessCase.administration.faq.label": "Frage hinzufügen", "dashboard.businessCase.administration.faq.label.addQuestion": "Frage hinzufügen", "dashboard.businessCase.administration.faq.label.editQuestion": "Frage bearbeiten", "dashboard.businessCase.administration.faq.noQuestions": "<PERSON>s gibt noch keine Fr<PERSON>n", "dashboard.businessCase.administration.label.notSynched": "<PERSON><PERSON><PERSON>", "dashboard.businessCase.administration.platformManagerRemovalDisabled": "Plattform-Manager k<PERSON><PERSON><PERSON> nicht entfernt werden", "dashboard.businessCase.administration.sectionTitle": " <PERSON><PERSON><PERSON><PERSON><PERSON> ", "dashboard.businessCase.administration.sectionTitle.FAQ": " FAQ ", "dashboard.businessCase.administration.sectionTitle.companyContactPeople": " Kundenportalfreigabe ", "dashboard.businessCase.administration.sectionTitle.companyContactPeople.realEstate": "Partnerportalfreigabe", "dashboard.businessCase.administration.sectionTitle.duplicateCase": " Finanzierungsfall kopieren ", "dashboard.businessCase.administration.sectionTitle.fundingCase": " Finanzierungsfall ", "dashboard.businessCase.button.caseTransfer": "Fallübertragung", "dashboard.businessCase.card.financingVolume": "Finanzierungsvolumen", "dashboard.businessCase.card.toolTipText": "Weitere Firmeninformationen", "dashboard.businessCase.card.totalInvestmentCosts": "Gesamtinvestitionskosten", "dashboard.businessCase.charts.notStructuredText": "<PERSON>cht strukturiert", "dashboard.businessCase.charts.valueZeroOrEmptyErrorText": "Die Gesamtbeteiligung kann nicht gleich <PERSON>ull sein", "dashboard.businessCase.collaboration": "Kollaboration", "dashboard.businessCase.collaboration.applications.header.tab": " Bewerbungen ", "dashboard.businessCase.collaboration.applications.numbersOf.accepted": "<PERSON><PERSON><PERSON><PERSON>", "dashboard.businessCase.collaboration.applications.numbersOf.preparation": "In Vorbereitung", "dashboard.businessCase.collaboration.applications.numbersOf.rejected": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dashboard.businessCase.collaboration.applications.numbersOf.submitted": "In Bearbeitung", "dashboard.businessCase.collaboration.applications.search.placeholder": "Bewerbung suchen...", "dashboard.businessCase.collaboration.applications.table.columns.customerName": "Organisation", "dashboard.businessCase.collaboration.applications.table.columns.startedOn": "Datum", "dashboard.businessCase.collaboration.applications.table.columns.state": "Status", "dashboard.businessCase.collaboration.applications.table.columns.totalParticipationAmount": "Betrag", "dashboard.businessCase.collaboration.applications.table.columns.user": "<PERSON><PERSON><PERSON>", "dashboard.businessCase.collaboration.chartData.financing": "Finanzierung", "dashboard.businessCase.collaboration.chartData.myAmount": "<PERSON><PERSON>", "dashboard.businessCase.collaboration.invitations.caseStatusTag.placeholder": "Status auswählen", "dashboard.businessCase.collaboration.invitations.filter": "Filter", "dashboard.businessCase.collaboration.invitations.filters.title": "Filter", "dashboard.businessCase.collaboration.invitations.label": " Einladungen ", "dashboard.businessCase.collaboration.invitations.label.numberOf": " <PERSON><PERSON><PERSON><PERSON><PERSON> ", "dashboard.businessCase.collaboration.invitations.label.numberOf.assumed": " <PERSON><PERSON><PERSON><PERSON> ", "dashboard.businessCase.collaboration.invitations.label.numberOf.cancel": "Abgebrochen", "dashboard.businessCase.collaboration.invitations.label.numberOf.workInProgress": " In Bearbeitung ", "dashboard.businessCase.collaboration.invitations.noCustomers": " Sie haben keine Organisation ausgewählt ", "dashboard.businessCase.collaboration.invitations.sendInvite": "Einladen", "dashboard.businessCase.collaboration.invitations.status.label": "Status", "dashboard.businessCase.collaboration.invitations.table.columns.creationDate": "Einladungsdatum", "dashboard.businessCase.collaboration.invitations.table.columns.customerName": "Organisation", "dashboard.businessCase.collaboration.invitations.table.columns.status": "Status", "dashboard.businessCase.collaboration.invitations.table.columns.users": "<PERSON><PERSON><PERSON>", "dashboard.businessCase.collaboration.invitations.text2": " Es liegen derzeit keine Ergebnisse vor.\r\n", "dashboard.businessCase.collaboration.invitations.typeOfInvitation.header": "Einladungsart", "dashboard.businessCase.collaboration.invitations.uiSelect.label": "Auswählen", "dashboard.businessCase.collaboration.myPartners.disabledVisibilityMessage": " Die Schaltfläche für die Börsensichtbarkeit Ihres Falls ist inaktiv, da Sie bereits eine Bewerbung angenommen haben bzw. Ihre Einladung angenommen wurde. ", "dashboard.businessCase.collaboration.myPartners.visibilityButton.label.closed": "Privat", "dashboard.businessCase.collaboration.myPartners.visibilityButton.label.open": "<PERSON><PERSON><PERSON><PERSON>", "dashboard.businessCase.collaboration.myPartners.visibilityChange": "Börsensichtbarkeit", "dashboard.businessCase.collaboration.myPartners.visibilityChange.tooltipTip": "Der Status eines neu erstellten Finanzierungsfalls ist standardmäßig “privat”. <PERSON>n Si<PERSON> den Fall auf dem Marktplatz platzieren möchten, dann schalten Si<PERSON> den Schieberegler auf “öffentlich”.", "dashboard.businessCase.collaboration.participationAmount": " Beteiligungshöhe bearbeiten ", "dashboard.businessCase.collaboration.participationChart.header": " <PERSON>ch zu sammelnder Betrag ", "dashboard.businessCase.collaboration.participationChart.header2": " Betrag gesammelt ", "dashboard.businessCase.collaboration.participationChart.myAmount": " <PERSON><PERSON> ", "dashboard.businessCase.collaboration.participationChart.participants": " <PERSON><PERSON><PERSON><PERSON><PERSON> ", "dashboard.businessCase.collaboration.participationChartHeader": " <PERSON><PERSON> ", "dashboard.businessCase.collaboration.tab.myPartners": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dashboard.businessCase.collaboration.text.noApplications": " Es liegen derzeit keine Ergebnisse vor.\r\n", "dashboard.businessCase.collaboration.totalParticipation": " Gesamtbeteiligung ", "dashboard.businessCase.collected": "Gesammelt", "dashboard.businessCase.dataRoom": " Der unternehmensbezogene Data Room wurde zum Finanzierungsfall hinzugefügt, allerdings sind noch keine Felder im unternehmensbezogenen Data Room vorhanden. <PERSON><PERSON><PERSON> Felder zu diesem hinzugefügt werden, werden diese hier angezeigt. ", "dashboard.businessCase.dataRoom.groupPortalActions": " Kundenportalaktionen ", "dashboard.businessCase.dataRoom.groupPortalActions.notVisible": " <PERSON>cht sichtbar ", "dashboard.businessCase.dataRoom.groupPortalActions.realEstate": "Partnerportalaktionen", "dashboard.businessCase.dataRoom.groupPortalActions.visibility": " <PERSON><PERSON><PERSON> ", "dashboard.businessCase.dataRoom.groupPortalActions.visibility.notVisible": " <PERSON>cht sichtbar ", "dashboard.businessCase.dataRoom.groupPortalActions.visibilityInCustomerPortal": " Sichtbarkeit im Kundenportal ", "dashboard.businessCase.dataRoom.groupPortalActions.visibilityInCustomerPortal.realEstate": "Sichtbarkeit im Partnerportal", "dashboard.businessCase.dataRoom.placeholder": "Information suchen...", "dashboard.businessCase.dataRoom.tabs.case": " Fall ", "dashboard.businessCase.dataRoom.tabs.company": "Unternehmen", "dashboard.businessCase.dataRoomTab": "Data Room", "dashboard.businessCase.dataRoomTabAndFinancingDetails": "Data room & Finanzierungsdetails", "dashboard.businessCase.downloadDocuments": "Dokumente herunterladen", "dashboard.businessCase.financing.details.nav.financing.structure": "Finanzierungsstruktur", "dashboard.businessCase.financing.details.nav.my.participation": "<PERSON><PERSON>", "dashboard.businessCase.header.caseHolder": " <PERSON><PERSON><PERSON><PERSON> ", "dashboard.businessCase.header.onlyReadRights": " Nur Leseberechtigung", "dashboard.businessCase.header.onlyReadRights.tooltip": "Im Data Room sind keine Änderungen möglich, da der Fall abgebrochen oder abgeschlossen wurde.", "dashboard.businessCase.header.participant": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dashboard.businessCase.informationLabel.caseStateChanged": "Fallstatus geä<PERSON>", "dashboard.businessCase.invitationToParticipate": " Einladung zur Teilnahme ", "dashboard.businessCase.lender": "Kreditgeber", "dashboard.businessCase.loanAmount": "Kreditbetrag", "dashboard.businessCase.necessary": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dashboard.businessCase.pdfExport": "PDF-Export", "dashboard.businessCase.percentFinancing": "% des Finanzierungsvolumens", "dashboard.businessCase.tabs.administration": "Verwaltung", "dashboard.businessCase.tabs.financingDetails": "Finanzierungsdetails", "dashboard.businessCase.tabs.overview": "Übersicht", "dashboard.businessCase.teaserExport": "Teaserexport", "dashboard.businessCase.templateFields.timeInMonths": "Laufzeit in Monaten", "dashboard.businessCase.templateFields.timeinMonths": "Laufzeit in Monaten", "dashboard.casesTable.caseType.financing": "Finanzierung", "dashboard.casesTable.caseType.passingOn": "Weitergabe", "dashboard.casesTable.columnsFilter.buttonLabel": "Spalten", "dashboard.casesTable.columnsFilter.columnsList.title": "Spalten", "dashboard.casesTable.financingType.corporate": "Unternehmensfinanzierung", "dashboard.casesTable.financingType.miscellaneous": "Sonstiges", "dashboard.casesTable.financingType.realEstate": "Immobilienfinanzierung", "dashboard.casesTable.header.tableType.general": "Allgemein", "dashboard.casesTable.header.tableType.kpi": "KPI", "dashboard.casesTable.objectStatus.buildingPermitIssued": "Baugenehmigung erteilt", "dashboard.casesTable.objectStatus.completionDateOfConstructionPhase1": "Fertigstellung", "dashboard.casesTable.objectStatus.liquidityEffectiveFullLettingDate": "Vollvermietung", "dashboard.casesTable.objectStatus.partialBuildingPermitIssued": "Teilbaugenehmigung erteilt", "dashboard.casesTable.objectStatus.startOfConstructionPhase1": "Baubeginn", "dashboard.casesTable.objectStatus.startOfPlanning": "Planungsbeginn", "dashboard.casesTable.type.general": "Allgemein", "dashboard.casesTable.type.kpi": "KPI", "dashboard.collaboration.invitations.placeholder": "Organisationen einladen...", "dashboard.collaboration.invitations.text": " aus Organisationen, die Si<PERSON> einladen möchten: ", "dashboard.collaboration.myParteners.section.hedaer.newparticipants": " Standardeinstellungen für neue Teilnehmer ", "dashboard.collaboration.myPartners.criteria.header": " Bewerberkriterien ", "dashboard.collaboration.myPartners.criteria.placeholder": "Bedingung hinzufügen", "dashboard.collaboration.myPartners.header": " Meine Partner ", "dashboard.collaboration.myPartners.own-participation-amount": "Betrag der Beteiligung", "dashboard.collaboration.myPartners.participantRequirements": "Teilnehmeranforderungen", "dashboard.collaboration.myPartners.participationAmountModal.notDefined": "<PERSON><PERSON> defini<PERSON>", "dashboard.collaboration.myPartners.roles": " Finanzierungsbeteiligung ", "dashboard.collaboration.myPartners.roles.FAQ": " FAQ ", "dashboard.collaboration.myPartners.roles.button.create": "<PERSON><PERSON><PERSON><PERSON>", "dashboard.collaboration.myPartners.roles.changeAmountModal.changeParticpantAmountText": "Beteiligungsbetrag ändern", "dashboard.collaboration.myPartners.roles.changeAmountModal.infoText": "Der aktualisierte Beteiligungsbetrag wird auf alle Plattformseiten angezeigt wo zutreffend.", "dashboard.collaboration.myPartners.roles.changeAmountModal.participantAmountText": "Betrag", "dashboard.collaboration.myPartners.roles.chatPermissions": "Bilaterale Chats", "dashboard.collaboration.myPartners.roles.dataRoom": " Data Room ", "dashboard.collaboration.myPartners.roles.dataRoom.create": " <PERSON><PERSON><PERSON> ", "dashboard.collaboration.myPartners.roles.dataRoom2": " Data Room ", "dashboard.collaboration.myPartners.roles.invitationManagement": "Bewerbungs- / Einladungsmanagement", "dashboard.collaboration.myPartners.roles.invitationManagement.text.permission": " Einsehen ", "dashboard.collaboration.myPartners.roles.invitationManagement.text.permission.administer": "<PERSON><PERSON><PERSON><PERSON>", "dashboard.collaboration.myPartners.roles.invitationManagement.text.permission.preview": "Vorschau", "dashboard.collaboration.myPartners.roles.invitationManagement.toolTipText": "Teilnehmer kann die Bewerbungen/Einladungen\r\n      anderen Teilnehmern sehen und/oder verwalten.", "dashboard.collaboration.myPartners.roles.label.rework": " <PERSON><PERSON><PERSON> ", "dashboard.collaboration.myPartners.roles.name": " Finanzierungsbeteiligung ", "dashboard.collaboration.myPartners.roles.name.writing": " Nachrichten schreiben ", "dashboard.collaboration.myPartners.roles.permission": " <PERSON><PERSON><PERSON> ", "dashboard.collaboration.myPartners.roles.permissions": "<PERSON>nn andere <PERSON> sehen", "dashboard.collaboration.myPartners.roles.permissions.chat": " Cha<PERSON> ", "dashboard.collaboration.myPartners.roles.tooltip.text": "Standardmäßig haben Teilnehmer bilateralen Chat nur mit dem Fallinhaber. Mit dieser Einstellung erlauben Sie Ihren Partnern bilaterale Chats miteinander zu haben.", "dashboard.collaboration.myPartners.section.errorMessage": "Minimalwert darf Maximalwert nicht überschreiten", "dashboard.collaboration.myPartners.section.field": "Mindestbeteiligungssumme", "dashboard.collaboration.myPartners.section.field.errorMessage2": "Maximalwert darf Minimalwert nicht unterschreiten", "dashboard.collaboration.myPartners.section.field.tooltip": "Der maximale Betrag, mit dem Ihre Partner sich am Finanzierungsfall beteiligen können.", "dashboard.collaboration.myPartners.section.field2": "Höchstbeteiligungssumme", "dashboard.collaboration.myPartners.section.maxWarningMessage": "Maximalbetrag", "dashboard.collaboration.myPartners.section.minWarningMessage": "Minimalbetrag", "dashboard.collaboration.myPartners.section.name": " Teilnehmeranforderungen ", "dashboard.collaboration.myPartners.section.placeholder": "Der kleinstmögliche Betrag, mit dem Ihre Partner sich am Finanzierungsfall beteiligen können.", "dashboard.collaboration.myPartners.sectionHeader.details": " Finanzierungsdetails ", "dashboard.contractTemplates.version": " Version ", "dashboard.dataRoom.button.label": "Zum Unternehmen", "dashboard.dataRoom.information": " Alle Eingabefelder mit diesem Symbol sind mit einem Feld im Data Room verknüpft. Die Verknüpfung wird aufgehoben, indem Sie auf das Icon klicken. Dad<PERSON>ch kann das Feld bearbeitet und ein neuer Wert eingeben werden. Durch das Klicken auf eine beliebige Stelle auf der Seite, wird der neue Wert gespeichert und der Eingabewert dieses Feldes für immer von dem Wert im Data Room getrennt. ", "dashboard.dataRoom.information2": "Sie können den unternehmensbezogenen Data Room über die Seite des Unternehmens bearbeiten. Klicken Sie auf den Button, wenn Sie Änderungen am unternehmensbezogenen Data Room vornehmen möchten. ", "dashboard.emptyState.information": " <PERSON><PERSON><PERSON><PERSON> Si<PERSON> Ihren ersten Fall, um das gesamte Dashboard zu sehen ", "dashboard.emptyState.title": " <PERSON><PERSON> Informationen vorhanden ", "dashboard.feedback": " Ihre Nachricht konnte nicht abgesendet werden.", "dashboard.feedback.checkBoxLabel": "Supportanfrage erstellen", "dashboard.feedback.errorMessage": "<PERSON><PERSON><PERSON> starten", "dashboard.feedback.placeholder": "<PERSON><PERSON>...", "dashboard.feedback.submit": "<PERSON><PERSON><PERSON><PERSON>", "dashboard.feedback.successMessage": "Neue Nachricht senden", "dashboard.feedback.thankYouMessage": " Vielen Dank für Ihr Feedback! ", "dashboard.feedback2": " Versuchen Sie es bitte erneut.", "dashboard.filter.modal": "Status", "dashboard.filter.modal.options.active": "Aktiv", "dashboard.filter.modal.options.inactive": "Inaktiv", "dashboard.filters.caseStatus.active.title": "Aktiv", "dashboard.filters.caseStatus.closed.title": "Geschlossen", "dashboard.filters.caseStatus.title": "<PERSON><PERSON><PERSON>", "dashboard.filters.caseStatusTag.placeholder": "Status auswählen", "dashboard.filters.caseStatusTags.active.applicationSubmitted": "Finanzierungsantrag wurde gestellt", "dashboard.filters.caseStatusTags.active.caseCreated": "Finanzierungsfall angelegt", "dashboard.filters.caseStatusTags.active.financingApproved": "Finanzierung genehmigt", "dashboard.filters.caseStatusTags.active.financingDecisionPending": "Finanzierungsbeschluss ausstehend", "dashboard.filters.caseStatusTags.active.financingSuccessfullyCompleted": "Finanzierung erfolgreich durchgeführt", "dashboard.filters.caseStatusTags.active.structuringOrderIssued": "Strukturierungsauftrag erteilt", "dashboard.filters.caseStatusTags.active.termSheetAccepted": "Termsheet wurde akzeptiert", "dashboard.filters.caseStatusTags.closed.applicationSubmitted": "Fehlerhafte Еrfassung", "dashboard.filters.caseStatusTags.closed.caseCreated": "Objekt wurde verkauft", "dashboard.filters.caseStatusTags.closed.financingApproved": "Finanzierung wurde zurückgezahlt", "dashboard.filters.caseStatusTags.closed.financingDecisionPending": "Sonstiges", "dashboard.filters.caseStatusTags.closed.structuringOrderIssued": "Finanzierungsantrag wurde zurückgezogen", "dashboard.filters.caseStatusTags.closed.termSheetAccepted": "Finanzierung wurde abgelehnt", "dashboard.filters.caseType.financing.label": "Finanzierung", "dashboard.filters.caseType.passingOn.label": "Weitergabe", "dashboard.filters.caseType.title": "Falltyp", "dashboard.filters.financingType.corporate.label": "Unternehmensfinanzierung", "dashboard.filters.financingType.corporate.label.new": "Unternehmensfinanzierung", "dashboard.filters.financingType.miscellaneous.label": "Sonstiges", "dashboard.filters.financingType.realEstate.label": "Immobilienfinanzierung", "dashboard.filters.financingType.realEstate.label.new": "Immobilienfinanzierung", "dashboard.filters.financingType.title": "Finanzierungsart", "dashboard.filters.financingType.title.new": "Finanzierungsart", "dashboard.filters.title": "Filter", "dashboard.filters.totalResults": "<PERSON>älle ausgewählt", "dashboard.filters.totalResultsOne": "Fall ausgewählt", "dashboard.financing.coveredCriteria": "E<PERSON>üllt", "dashboard.financingDetails.header.creditAmount": " Kreditbetrag ", "dashboard.invitationTaB.header.creationDate": "Einladungsdatum", "dashboard.map.tooltip.assetClass": "Assetklasse", "dashboard.map.tooltip.caseId": "Fall-ID", "dashboard.map.tooltip.objectAddress": "Objektstandort", "dashboard.map.tooltip.totalInvestment": "Gesamtinvestitionkosten", "dashboard.marketplace.overviewTab.contactPerson": " <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ", "dashboard.marketplace.overviewTab.contactPerson.noContactPerson": " <PERSON><PERSON> angel<PERSON> ", "dashboard.mine.revisions.showLess": "<PERSON><PERSON> anzeigen", "dashboard.mine.revisions.showMore": "<PERSON><PERSON> anzeigen", "dashboard.overview.finiancingDetails.header.editMode": "Bearbeitungsmo<PERSON>", "dashboard.overview.header.revisions.recentChanges": " Neueste Änderungen ", "dashboard.overviewTab.contactPerson": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dashboard.propertyLocation.search.placeholder": "Objekt suchen", "dashboard.realEstate.analytics.assetClasses.tooltipPrefix": "Betrag", "dashboard.realEstate.analytics.assetClasses.tooltipSuffix": "der Gesamtinvestitionskosten aller Assetklassen bei Immobilienfinanzierungen", "dashboard.realEstate.analytics.totalInvestmentAmount.ownInvestmentAmount.tooltip": "Betrag", "dashboard.realEstate.analytics.totalInvestmentAmount.totalOwnInvesmentAmount.tooltip": "der Gesamtinvestitionskosten aller Objekttypen bei Immobilienfinanzierungen", "dashboard.realEstate.casesTable.finance.columns.caseStatus": "<PERSON><PERSON><PERSON>", "dashboard.realEstate.casesTable.finance.columns.company": "Unternehmen", "dashboard.realEstate.casesTable.finance.columns.creationDate": "Erstellungsdatum", "dashboard.realEstate.casesTable.finance.columns.debtRatio": "Fremdkapitalquote zu GIK", "dashboard.realEstate.casesTable.finance.columns.equityRatio": "Eigenkapitalquote zu GIK", "dashboard.realEstate.casesTable.finance.columns.financingType": "Finanzierungsart", "dashboard.realEstate.casesTable.finance.columns.foreignCapital": "Fremdkapital", "dashboard.realEstate.casesTable.finance.columns.ltcAtMarketValue": "LTV zum Marktwert", "dashboard.realEstate.casesTable.finance.columns.ltcAtPlannedSalesProceeds": "LTV zum Plan-Veräußerungserlös", "dashboard.realEstate.casesTable.finance.columns.ltcAtTotalInvestmentAmount": "LTC zu GIK", "dashboard.realEstate.casesTable.finance.columns.marketValue": "Marktwert Gesamt", "dashboard.realEstate.casesTable.finance.columns.objectAddress": "Objektstandort", "dashboard.realEstate.casesTable.finance.columns.ownCapital": "Eigenkapital", "dashboard.realEstate.casesTable.finance.columns.projectName": "Projektname", "dashboard.realEstate.casesTable.finance.columns.propertyDebtForAnnualIncomeActual": "Objekt-Kapitaldienstfähigkeit IST-JNKM", "dashboard.realEstate.casesTable.finance.columns.propertyDebtForAnnualIncomeTarget": "Objekt-Kapitaldienstfähigkeit SOLL-JNKM", "dashboard.realEstate.casesTable.general.columns.actualAnnualNetRentalIncomeTotal": "IST-JNKM", "dashboard.realEstate.casesTable.general.columns.assetClass": "Assetklasse", "dashboard.realEstate.casesTable.general.columns.caseStatus": "<PERSON><PERSON><PERSON>", "dashboard.realEstate.casesTable.general.columns.company": "Unternehmen", "dashboard.realEstate.casesTable.general.columns.creationDate": "Erstellungsdatum", "dashboard.realEstate.casesTable.general.columns.ecoreScoring": "ECORE-SCORING", "dashboard.realEstate.casesTable.general.columns.energyEfficiencyClass": "Energieeffizienzklasse", "dashboard.realEstate.casesTable.general.columns.esgCertified": "ESG-Zertifiziert", "dashboard.realEstate.casesTable.general.columns.financingType": "Finanzierungsart", "dashboard.realEstate.casesTable.general.columns.marketValue": "Marktwert", "dashboard.realEstate.casesTable.general.columns.objectAddress": "Objektstandort", "dashboard.realEstate.casesTable.general.columns.objectName": "Projektname", "dashboard.realEstate.casesTable.general.columns.objectStatus": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dashboard.realEstate.casesTable.general.columns.objectType": "Objekttyp", "dashboard.realEstate.casesTable.general.columns.rentalUnits": "Mieteinheiten", "dashboard.realEstate.casesTable.general.columns.targetAnnualNetRentalIncomeTotal": "SOLL-JNKM", "dashboard.realEstate.casesTable.general.columns.totalGrossFloorArea": "BGF (m²)", "dashboard.realEstate.casesTable.general.columns.totalInvestmentAmount": "Gesamtinvestitionskosten", "dashboard.realEstate.casesTable.general.columns.totalRentalSpace": "Mietfläche Gesamt (m²)", "dashboard.realEstate.casesTable.kpi.columns.caseStatus": "<PERSON><PERSON><PERSON>", "dashboard.realEstate.casesTable.kpi.columns.costsGrossFloor": "Kosten QM BGF zu GIK", "dashboard.realEstate.casesTable.kpi.columns.costsRentalSpace": "Kosten QM Mietfläche zu GIK", "dashboard.realEstate.casesTable.kpi.columns.creationDate": "Erstellungsdatum", "dashboard.realEstate.casesTable.kpi.columns.financingType": "Finanzierungsart", "dashboard.realEstate.casesTable.kpi.columns.multiplierMarketValueActual": "Multiplikator IST-JNKM zum Marktwert", "dashboard.realEstate.casesTable.kpi.columns.multiplierMarketValueTarget": "Multiplikator SOLL-JNKM zum Marktwert", "dashboard.realEstate.casesTable.kpi.columns.multiplierPlanDisposalProceedsTarget": "Multiplikator SOLL-JNKM zum Plan-VE", "dashboard.realEstate.casesTable.kpi.columns.multiplierTotalInvestmentAmountActual": "Multiplikator IST-JNK<PERSON> zu GIK", "dashboard.realEstate.casesTable.kpi.columns.multiplierTotalInvestmentAmountTarget": "Multiplikator SOLL-JNKM zu GIK", "dashboard.realEstate.casesTable.kpi.columns.objectAddress": "Objektstandort", "dashboard.realEstate.casesTable.kpi.columns.planProfit": "Plan-Projektgewinn €", "dashboard.realEstate.casesTable.kpi.columns.planProfitPercentage": "Plan-Projektgewinn %", "dashboard.realEstate.casesTable.kpi.columns.pricePerQMLand": "Preis QM Grundstück zum Kaufpreis GS", "dashboard.realEstate.casesTable.kpi.columns.projectName": "Projektname", "dashboard.realEstate.casesTable.kpi.columns.rentalSpaceActual": "IST-JNKM Gesamt pro QM", "dashboard.realEstate.casesTable.kpi.columns.rentalSpaceFactor": "Mietflächenfaktor / Flächeneffizienz", "dashboard.realEstate.casesTable.kpi.columns.rentalSpaceTarget": "SOLL-JNKM Gesamt pro QM", "dashboard.realEstate.casesTable.kpi.columns.returnAtMarketValueActual": "Rendite-Marktwert zur IST-JNKM", "dashboard.realEstate.casesTable.kpi.columns.returnAtMarketValueTarget": "Rendite-Marktwert zur SOLL-JNKM", "dashboard.realEstate.casesTable.kpi.columns.returnOfInvestmentCostsActual": "Rendite-GIK zur IST-JNKM", "dashboard.realEstate.casesTable.kpi.columns.returnOfInvestmentCostsTarget": "Rendite-GIK zur SOLL-JNKM", "dashboard.realEstate.casesTable.kpi.columns.returnOnPlanSalesProceeds": "Rendite-PlanVE zur SOLL-JNKM", "dashboard.realEstate.controls.tab.finance": " Finan<PERSON>ten ", "dashboard.realEstate.controls.tab.portfolio": "Portfolio", "dashboard.realEstate.finance.financingPartner.title": " Finanzierungspartner\r\n", "dashboard.realEstate.finance.financingPartnerWidgetTable.cases.header": "<PERSON><PERSON><PERSON>", "dashboard.realEstate.finance.financingPartnerWidgetTable.financingPartner.header": "Name", "dashboard.realEstate.finance.financingPartnerWidgetTable.participationAmount.header": "Gesamtbeteiligung", "dashboard.realEstate.finance.financingStructure.title": " Struktur der Finanzierung\r\n", "dashboard.realEstate.finance.financingStructure.tooltipPrefix": "Gesamtbelastung des Objekts incl. aller Vorlasten", "dashboard.realEstate.finance.financingStructure.tooltipSuffix": "der Gesamtinvestitionskosten bei Immobilienfinanzierungen", "dashboard.realEstate.finance.financingStructure.totalLoanAmount": "Gesamtbelastung der \r\nObjekte incl. aller Vorlasten", "dashboard.realEstate.finance.financingStructure.totalOwnCapital": "Eigenkapital Gesamt", "dashboard.realEstate.finance.kpi.cases": "<PERSON><PERSON><PERSON>", "dashboard.realEstate.finance.kpi.totalLTC": "LTC Gesamt", "dashboard.realEstate.finance.kpi.totalLTV": "LTV Gesamt", "dashboard.realEstate.finance.kpi.totalMarketValue": "Marktwert Gesamt", "dashboard.realEstate.portfolio.analytics.assetClasses.title": " Assetklassen ", "dashboard.realEstate.portfolio.analytics.energy.title": " Energieeffizienzklasse ", "dashboard.realEstate.portfolio.analytics.objectStatus.buildingPermissionGranted": "Bаugenehmigung erteilt", "dashboard.realEstate.portfolio.analytics.objectStatus.completion": "Fertigstellung", "dashboard.realEstate.portfolio.analytics.objectStatus.fullyLetOut": "Vollvermietung", "dashboard.realEstate.portfolio.analytics.objectStatus.partialBuildingPermissionGranted": "Teilbaugenehmigung erteilt", "dashboard.realEstate.portfolio.analytics.objectStatus.startOfConstruction": "Baubeginn", "dashboard.realEstate.portfolio.analytics.objectStatus.startOfPlanning": "Planungsbeginn", "dashboard.realEstate.portfolio.analytics.objectStatus.title": " <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ", "dashboard.realEstate.portfolio.analytics.subleaseClasses.commercialRentalSpace": "Mietfläche \r\nGewerbe", "dashboard.realEstate.portfolio.analytics.subleaseClasses.gastronomyRentalSpace": "Mietfläche \r\nGastronomie", "dashboard.realEstate.portfolio.analytics.subleaseClasses.hotelRentalSpace": "Mietfläche \r\nHotel", "dashboard.realEstate.portfolio.analytics.subleaseClasses.officeRentalSpace": "Mietfläche \r\nBüro", "dashboard.realEstate.portfolio.analytics.subleaseClasses.otherOperatorPropertyRentalSpace": "Mietfläche Sonstige \r\nBetreiberimmobilie", "dashboard.realEstate.portfolio.analytics.subleaseClasses.otherRentalSpace": "Mietfläche \r\nSonstige", "dashboard.realEstate.portfolio.analytics.subleaseClasses.residentialRentalSpace": "Mietfläche \r\nwohnwirtschaftlich", "dashboard.realEstate.portfolio.analytics.subleaseClasses.retailRentalSpace": "Mietfläche \r\nHandel", "dashboard.realEstate.portfolio.analytics.subleaseClasses.targetJNKMTooltip": "SOLL-JNKM", "dashboard.realEstate.portfolio.analytics.subleaseClasses.title": " Untermietklassen ", "dashboard.realEstate.portfolio.analytics.subleaseClasses.totalSpaceTooltip": "Gesamtfläche", "dashboard.realEstate.portfolio.analytics.title": " Analytische Daten ", "dashboard.realEstate.portfolio.analytics.totalInvestmentAmount.title": " Gesamtinvestitionskosten nach Objekttyp ", "dashboard.realEstate.portfolio.kpi.allRentalUnits": "Alle Mieteinheiten", "dashboard.realEstate.portfolio.kpi.cases": "<PERSON><PERSON><PERSON>", "dashboard.realEstate.portfolio.kpi.totalGrossFloorArea": "BGF Gesamt (m²)", "dashboard.realEstate.portfolio.kpi.totalInvestmentAmount": "Gesamtinvestitionskosten", "dashboard.realEstate.portfolio.kpi.totalMarketValue": "Marktwert Gesamt", "dashboard.realEstate.portfolio.kpi.totalRentalSpace": "Mietfläche Gesamt (m²)", "dashboard.realEstate.portfolio.marketValueWidget.title": " Historische Entwicklung der Gesamtmarktwerte\r\n", "dashboard.revisions.informationLabel.applicationAccepted": "Bewerbung angenommen", "dashboard.revisions.informationLabel.applicationCanceled": "Bewerbung abgebrochen", "dashboard.revisions.informationLabel.applicationCreated": "Bewerbungsmaske angesehen", "dashboard.revisions.informationLabel.applicationDeclined": "Bewerbung abgelehnt", "dashboard.revisions.informationLabel.applicationInvitationCreated": "Einladung versendet", "dashboard.revisions.informationLabel.applicationInvitationDeclined": "Einladung abgelehnt", "dashboard.revisions.informationLabel.applicationSubmitted": "Bewerbung eingereicht", "dashboard.revisions.informationLabel.businessCaseDuplicated": "Finanzierungsfall kopiert von {$PH}", "dashboard.revisions.informationLabel.consortiumCreated": "Fall erstellt", "dashboard.revisions.informationLabel.consortiumStateChanged": "Fallstatus geä<PERSON>", "dashboard.revisions.informationLabel.informationAdded": "Information hinzugefügt", "dashboard.revisions.informationLabel.informationCreated": "Information gespeichert", "dashboard.revisions.informationLabel.informationDeleted": "Information gelöscht", "dashboard.revisions.informationLabel.informationUpdated": "Information aktualisiert", "dashboard.revisions.informationLabel.invitationAccepted": "Einladung angenommen", "dashboard.revisions.informationLabel.invitationCanceled": "Einladung abgebrochen", "dashboard.revisions.informationLabel.invitationCreated": "Teilnehmerbank eingeladen", "dashboard.revisions.informationLabel.leadChange": "<PERSON><PERSON>hab<PERSON> g<PERSON>ä<PERSON>", "dashboard.revisions.informationLabel.leadChanged": "Finanzierungsvorhaben abgegeben", "dashboard.revisions.informationLabel.ndaSigned": "Vertraulichkeitsvereinbarung signiert", "dashboard.revisions.informationLabel.newDocument": "Dokument hinzugefügt", "dashboard.revisions.informationLabel.participantAdded": "Teilnehmerbank hinzugefügt", "dashboard.revisions.informationLabel.participantChanged": "Teilnahmebedingungen angepasst", "dashboard.revisions.informationLabel.participantChangedUserRemoved": "Fall verlassen", "dashboard.revisions.informationLabel.participantRemoved": "Teilnehmerbank entfernt", "dashboard.revisions.informationLabel.userAdded": "<PERSON><PERSON><PERSON>", "dashboard.revisions.informationLabel.userRemoved": "<PERSON><PERSON><PERSON> entfernt", "dashboard.revisions.informationLabel.userUpdated": "<PERSON><PERSON><PERSON> aktualisiert ", "dashboard.sidebar.marketplace.bankDefault": "Bank", "dashboard.table.header.3": "Volumen", "dashboard.widgets.emptyState.title": " <PERSON><PERSON> darstellbaren Informationen vorhanden ", "data-roomGroup.visibility.subTitle.inviteeApplication": " <PERSON><PERSON>r eingeladene Organisationen, Bewerber, Teilnehmer und Fallinhaber verfügbar ", "data-roomGroup.visibility.template": " <PERSON><PERSON>rä<PERSON> ", "data-roomGroup.visibility.title": "Privat", "data-roomGroup.visibility.title.inviteeApplication": " Fallinteressierte ", "dataExportModal.title": "Datenexport", "dataGrid.addNewRow": "Neue Zeile hinzufügen", "dataGrid.contextMenu.delete": "Löschen", "dataGrid.contextMenu.delete.shortcut": "Entf", "dataGrid.contextMenu.paste": "Einfügen", "dataGrid.contextMenu.paste.shortcut": "Strg + V", "dataGrid.contextMenu.paste.tooltip": "Verwenden Sie die Tastenkombination Strg + V zum Einfügen", "dataGrid.contextMenu.pinOnBottom": "Pin unten", "dataGrid.contextMenu.pinOnTop": "<PERSON>n oben", "dataGrid.contextMenu.pinRow": "<PERSON><PERSON><PERSON> anheften", "dataGrid.dataType.checkbox": "An<PERSON><PERSON><PERSON><PERSON>", "dataGrid.dataType.currency": "Währung", "dataGrid.dataType.date": "Datum", "dataGrid.dataType.decimal": "Dezimal", "dataGrid.dataType.number": "Numeric", "dataGrid.dataType.numeric": "<PERSON><PERSON><PERSON><PERSON>", "dataGrid.dataType.percentage": "Prozentsatz", "dataGrid.deleteColumn": "Spalte löschen", "dataGrid.deleteRow": "Zeile löschen", "dataGrid.export": "Exportieren", "dataGrid.export.csv": "CSV", "dataGrid.export.excel": "Excel", "dataGrid.formula.errors.div": "Sie können nicht durch 0 teilen.", "dataGrid.formula.errors.error": "Fehler. <PERSON><PERSON> gibt ein Problem mit einer Formel.", "dataGrid.formula.errors.na": "Fehlende oder fehlerhafte Daten.", "dataGrid.formula.errors.name": "<PERSON>s gibt keine derartige Funktion.", "dataGrid.formula.errors.null": "<PERSON>hler in ausgewählten Zellen. Pfad oder Formel prüfen.", "dataGrid.formula.errors.num": "Fehler bei Zahlenformat oder Berechnung.", "dataGrid.formula.errors.ref": "<PERSON><PERSON> der Zellen, auf die <PERSON> sich beziehen, sind nicht mehr erreich<PERSON>.", "dataGrid.formula.errors.value": "Falscher Argumenttyp. Bitte überprüfen Sie die Formel.", "dataGrid.pinLeft": "Pin links", "dataGrid.pinRight": "<PERSON>n rechts", "dataGrid.pushPin": "Spalte anheften", "dataGrid.statusBar.metrics.average": "Durchschnitt", "dataGrid.statusBar.metrics.count": "<PERSON><PERSON><PERSON>", "dataGrid.statusBar.metrics.max": "Max", "dataGrid.statusBar.metrics.min": "Min", "dataGrid.statusBar.metrics.sum": "Summe", "dataGrid.symbolOutlined": "Spaltengröße anpassen", "dataGrid.toolbar.functions.average": "MITTELWERT", "dataGrid.toolbar.functions.category.commonlyUsed": "<PERSON><PERSON><PERSON><PERSON> verwendet", "dataGrid.toolbar.functions.category.date": "Datum", "dataGrid.toolbar.functions.category.engineering": "Technik", "dataGrid.toolbar.functions.category.financial": "Fin<PERSON>zen", "dataGrid.toolbar.functions.category.information": "Informationen", "dataGrid.toolbar.functions.category.logical": "Logik", "dataGrid.toolbar.functions.category.lookupAndReference": "Nachschlagen", "dataGrid.toolbar.functions.category.math": "Mathematik", "dataGrid.toolbar.functions.category.statistics": "Statistik", "dataGrid.toolbar.functions.category.text": "Text", "dataGrid.toolbar.functions.ceiling": "OBERGRENZE", "dataGrid.toolbar.functions.commonlyUsed": "<PERSON><PERSON><PERSON><PERSON> verwendet", "dataGrid.toolbar.functions.concat": "TEXTKETTE", "dataGrid.toolbar.functions.count": "ANZAHL", "dataGrid.toolbar.functions.counta": "ANZAHL2", "dataGrid.toolbar.functions.countblank": "ANZAHLLEEREZELLEN", "dataGrid.toolbar.functions.floor": "UNTERGRENZE", "dataGrid.toolbar.functions.if": "WENN", "dataGrid.toolbar.functions.len": "LÄNGE", "dataGrid.toolbar.functions.lower": "KLEIN", "dataGrid.toolbar.functions.now": "JETZT", "dataGrid.toolbar.functions.proper": "GROSS2", "dataGrid.toolbar.functions.replace": "ERSETZEN", "dataGrid.toolbar.functions.substitute": "WECHSELN", "dataGrid.toolbar.functions.sum": "SUMME", "dataGrid.toolbar.functions.today": "HEUTE", "dataGrid.toolbar.functions.trim": "GLÄTTEN", "dataGrid.toolbar.functions.upper": "GROSS", "dataGrid.toolbar.horizontalAlignment.center": "mitte", "dataGrid.toolbar.horizontalAlignment.justify": "blocksatz", "dataGrid.toolbar.horizontalAlignment.left": "links", "dataGrid.toolbar.horizontalAlignment.right": "rechts", "dataGrid.toolbar.tooltips.backgroundColor": "Hintergrundfarbe", "dataGrid.toolbar.tooltips.checkbox": "An<PERSON><PERSON><PERSON><PERSON>", "dataGrid.toolbar.tooltips.currency": "Währung", "dataGrid.toolbar.tooltips.export": "Exportieren", "dataGrid.toolbar.tooltips.functions": "Funktionen", "dataGrid.toolbar.tooltips.horizontalAlignment": "Horizontale Ausrichtung", "dataGrid.toolbar.tooltips.more": "<PERSON><PERSON>", "dataGrid.toolbar.tooltips.percentage": "Prozentwert", "dataGrid.toolbar.tooltips.textColor": "Textfarbe", "dataGrid.toolbar.tooltips.verticalAlignment": "<PERSON><PERSON><PERSON><PERSON>", "dataGrid.toolbar.tooltips.wrapText": "Textumbruch", "dataGrid.withoutPin": "kein <PERSON>", "dataRoom.accessModal.column": " Organisation ", "dataRoom.accessModal.columnR": "<PERSON><PERSON>", "dataRoom.accessModal.label": " <PERSON><PERSON><PERSON> ", "dataRoom.accessModal.label2": " Eingeladene, Bewerber, Chatkontakte ", "dataRoom.accessModal.label3": " Standardmäßig sichtbar für neue ", "dataRoom.accessModal.text": "Verwalten Sie die Sichtbarkeit für jede Data Room Gruppe und legen Sie Standardeinstellungen für neue Teilnehmer, Eingeladene, Bewerber und Chatkontakte fest.", "dataRoom.accessModal.title": " Zugriffsrechte verwalten ", "dataRoom.addDataText": "<PERSON><PERSON>", "dataRoom.crawlError.label": "Das Handelsregister ist derzeit nicht erreichbar. Der Download wird später\r\n      fortgesetzt.", "dataRoom.customHandler.toast.success": "Dokument erfolgreich aktualisiert", "dataRoom.default.defaultControlTitle.explicitlyShared": "Einstellungen für neue Direkt Geteilte", "dataRoom.default.defaultControlTitle.interestedCustomer": "Einstellungen für neue Fallinteressierte", "dataRoom.default.defaultControlTitle.participant": "Einstellungen für neue Teilnehmer", "dataRoom.delete.input.warning": "Sind <PERSON> sicher, dass Sie die Eingabe löschen möchten?", "dataRoom.delete.synced.input.warning": "Wenn Sie die Eingabe löschen, wird die Synchronisierung mit den Finanzierungsdetails gestoppt, und Sie können die Synchronisierung nicht wiederherstellen. ", "dataRoom.documentCategory.label": "Dokumentenkategorie auswählen", "dataRoom.draggableFields.boolean": "<PERSON><PERSON>/<PERSON><PERSON>-<PERSON><PERSON>", "dataRoom.draggableFields.currency": "Geldbetrag", "dataRoom.draggableFields.date": "Datum", "dataRoom.draggableFields.document": "Dokument", "dataRoom.draggableFields.folder": "<PERSON><PERSON><PERSON>", "dataRoom.draggableFields.integer": "<PERSON><PERSON><PERSON><PERSON>", "dataRoom.draggableFields.location": "<PERSON><PERSON>", "dataRoom.draggableFields.month": "Monate", "dataRoom.draggableFields.percent": "Prozentwert", "dataRoom.draggableFields.select": "Drop-Down", "dataRoom.draggableFields.table": "<PERSON><PERSON><PERSON>", "dataRoom.draggableFields.tag": "Dezimal", "dataRoom.draggableFields.title": "Kurzer Text", "dataRoom.draggableFields.titlePlus": "Langer Text", "dataRoom.noFieldsInGroups.beforeBtn": "Wechseln Sie im linken Bereich zu ", "dataRoom.onboardingTip.text.documentDragAndDrop": "<PERSON>e können <PERSON>, welche in die Inbox hochgeladen wurden, durch Drag & Drop zum Finanzierungsfall hinzufügen.", "dataRoom.onboardingTip.text.documentUpload": "Sie können Dateien durch Drag & Drop in die Inbox hochladen.", "dataRoom.onboardingTip.text.documentsIntoGroups": "<PERSON>e können Dateien aus der Liste auswählen und diese durch Drag & Drop zu Gruppen hinzufügen.", "dataRoom.onboardingTip.title.documentDragAndDrop": "Dateien zum Finanzierungsfall hinzufügen", "dataRoom.onboardingTip.title.documentUpload": "Dateien in die Inbox hochladen", "dataRoom.onboardingTip.title.documentsIntoGroups": "<PERSON><PERSON> sortieren", "dataRoom.participantFieldRequest.infoMessage": "<PERSON><PERSON> <PERSON><PERSON> au<PERSON> \"An<PERSON><PERSON>\" klicken, werden alle Nutzer dieses Teilnehmers unmittelbar informiert. Sie müssen mindestens einen Nutzer auswählen.", "dataRoom.participantFieldRequest.title": "<PERSON>utzer auswählen", "dataRoom.selectAll": "Alle auswählen", "dataRoom.singleFile.upload": "Dateiname  {$INTERPOLATION}", "dataRoom.tabs.mine": " <PERSON><PERSON> ", "dataRoom.tabs.shared": " Geteilte ", "dataRoom.visibilityState.cadrParticipantDescription": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> und Direkt Geteilte verfügbar", "dataRoom.visibilityState.cadrParticipantDescription2": "<PERSON><PERSON><PERSON>", "dataRoom.visibilityState.cadrPrivateDescription": "<PERSON><PERSON><PERSON>aber verfügbar", "dataRoom.visibilityState.cadrPublicDescription": "Alle Organisationen mit Zugriff auf diesen Datenraum können diese Gruppe sehen", "dataRoom.visibilityState.cardParticipantTitle": "Teilnehmer und Direkt Geteilte", "dataRoom.visibilityState.caseDrParticipantDescription": "<PERSON><PERSON><PERSON> und Fallinhaber verfügbar", "dataRoom.visibilityState.caseDrPublicDescription": "Alle Organisationen mit Zugriff auf diesen Fall können diese Gruppe sehen", "dataRoomTemplate.information": " Information hinzufügen ", "dataType.audio": "Audio", "dataType.configuration": "Konfiguration", "dataType.diagram": "Diagramm", "dataType.document": "Dokument", "dataType.image": "Bild", "dataType.presentation": "Präsentation", "dataType.spreadsheet": "Spreadsheet", "dataType.video": "Video", "datagrid.addNewColumn": "Neue Spalte hinzufügen", "dateRangeField.label.from": "<PERSON>", "dateRangeField.label.till": "Bis", "dateRangeField.label.to": "Bis", "dateService.timePastBefore": "vor", "dateService.timePastSince": "seit", "deleteButtonLabel.areYouSure": " Wirklich löschen?", "detachMasterCase.dialog.label.no": "<PERSON><PERSON>", "detachMasterCase.dialog.label.yes": "<PERSON><PERSON>, möchte ich", "detachMasterCase.dialog.message": "<PERSON>n <PERSON> dieses Feld unabhä<PERSON> machen, wird es von dem <PERSON> en<PERSON>, der von der Organisation angegeben wurde, die den Fall für Sie freigegeben hat.", "detachMasterCase.dialog.text": " Möcht<PERSON> Si<PERSON> wirklich in den Bearbeitungsmodus wechseln? Wenn Sie ein Feld zu einem unabhängigen Eingabefeld machen, trennen Sie dessen Eingabewert von dem damit verlinktem Wert im Data Room. ", "detachMasterCase.dialog.title": "Möchten Sie den Bearbeitungsmodus aktivieren?", "details.label": "Details", "deviceAuthentication.authenticate": "Authentifizieren", "deviceAuthentication.deviceName": "Ger<PERSON>eb<PERSON><PERSON><PERSON><PERSON>ng", "deviceAuthentication.message": "Sie haben die Mail noch nicht erhalten? Versuchen Sie, nach {$START_TAG_STRONG}{$INTERPOLATION}{$CLOSE_TAG_STRONG} eine neue E-Mail zu senden.", "deviceAuthentication.resendButtonText": "Versuchen Sie es erneunt.", "deviceAuthentication.resendMessage": "Sie haben keine Authentifizierungsmail erhalten? Veruschen Sie es erneunt in {$START_TAG_SPAN}{$INTERPOLATION}{$CLOSE_TAG_SPAN}.", "deviceAuthentication.securityCode": "Sicherheitscode", "deviceAuthentication.text": "Benennen und authentifizieren Sie Ihr Gerät um fortzufahren.", "deviceAuthentication.timeOutMessage": "Sie haben keine Authentifizierungsmail erhalten?", "deviceAuthentication.timeOutMessage2": "Senden Si<PERSON> eine neue.", "deviceAuthentication.validationError": "<PERSON><PERSON><PERSON> Sie den Namen Ihres Geräts ein", "deviceAuthentication.verification": "Sicherheitscode senden", "digitalSignature.dashboard.header": " Signaturübersicht ", "digitalSignature.dashboard.heading": " <PERSON><PERSON> Dokumente zu signieren ", "digitalSignature.dashboard.heading.signer": " Unterzeichner ", "digitalSignature.notificationModal.button.label": "<PERSON><PERSON>", "digitalSignature.notificationModal.mainText": " Wir haben einen Signatur-Authentifizierungscode an", "digitalSignature.notificationModal.mainText2": " gesendet.", "digitalSignature.notificationModal.smallPrint": " Bitte verwenden Sie den Code, um das Dokument im nächsten Schritt zu überprüfen und zu signieren. ", "digitalSignature.signature.signatureAuthCodeFailed.smallPrint": " Bitte verwenden Sie den Code, um das Dokument im nächsten Schritt zu überprüfen und zu signieren. ", "digitalSignature.signature.signatureAuthCodeFailed.text": "Wir haben einen Signatur-Authentifizierungscode an", "digitalSignature.signature.signatureAuthCodeFailed.text2": "gesendet", "digitalSignature.signatureSessionError.info": " Entschuldigung! Beim Signieren des Dokuments ist ein Problem aufgetreten. ", "digitalSignature.signatureSessionError.label.redirectToDashboard": "Zurück zur Signaturübersicht", "digitalSignature.signatureSessionError.label.tryAgain": "<PERSON><PERSON><PERSON> versuchen", "digitalSignature.signatureSessionExpired.button.label.redirectToDashboard": "Zurück zur Signaturübersicht", "digitalSignature.signatureSessionExpired.button.label.tryAgain": "<PERSON><PERSON><PERSON> versuchen", "digitalSignature.signatureSessionExpired.info": " Ups... Ihre Session ist abgelaufen. ", "digitalSignature.signatureSessionSuccess.additionalInfo": "<PERSON>te beachten Sie, dass die Bereitstellung der signierten Kopie eventuell einige Minuten dauern kann.", "digitalSignature.signatureSessionSuccess.button.label.redirectToDashboard": "Zurück zur Signaturübersicht", "digitalSignature.signatureSessionSuccess.info": " Sehr gut! Sie haben das Dokument erfolgreich unterzeichnet. ", "digitalSignature.sortFields.title": "Dateiname", "document.display.errorMessage.isDocumentMIssingError": " Die Datei ist nicht mehr verfügbar", "document.display.errorMessage.isNotSentToInboxSuccessful": " <PERSON><PERSON><PERSON> ho<PERSON>n ", "document.display.successMessage": " Weiterleitung erfolgreich ", "document.invalid-type.error": "Die von Ihnen hochgeladene Datei wurde als potentiell gefährlich eingestuft und wieder entfernt.", "document.pdf.type.error": "Nicht unterstützter Dateityp. Unterstüzte Dateitypen sind: PDF", "documentDisplay.tooltip.text": "Zur Inbox weiterleiten", "documentField.label.at": "um", "documentField.manageHelper.toast.error": "Problem beim <PERSON>", "documentField.message.missingDescription": "<PERSON>ine Beschreibung hinzugefügt", "documentField.upload.description": "oder per Drag-and-Drop hier ablegen", "documentField.upload.download": " Download ", "documentField.upload.failure": "<PERSON>i konnte nicht hochgeladen werden", "documentField.upload.inProgress": "<PERSON>i wird hoch<PERSON>n, bitte warten <PERSON>e", "documentField.upload.modalSubTitle": " <PERSON>den Si<PERSON> eine neue Datei hoch.", "documentField.upload.modalTitle": " Dokument hochladen ", "documentField.validationError.documentNameExist": "Der Name wird bereits für ein Dokumentfeld verwendet. Bitte wählen Si<PERSON> einen anderen", "documentFiller.button.label": "Automatisch generieren", "documentFiller.button.label.inProgress": "Antwort wird generiert...", "documentFiller.button.label.retry": "<PERSON><PERSON> gene<PERSON>", "documentFiller.error.gibberishOrLatin": "Fehler bei der Erstellung von AI-Vorschlägen. Bitte versuchen Si<PERSON> es mit einer anderen Datei.", "documentFiller.error.minimalText": "Bei der Verarbeitung der PDF-Datei ist ein Fehler aufgetreten. Bitte versuchen Si<PERSON> es mit einer Datei mit mehr Textinhalt.", "documentFiller.error.moralError": "Fehler bei der Erstellung von AI-Vorschlägen. Bitte versuchen Si<PERSON> es mit einer anderen Datei.", "documentFiller.error.noText": "Bei der Verarbeitung der PDF-Datei ist ein Fehler aufgetreten. Die Datei muss auf den ersten Seiten Textinhalt enthalten.", "documentFiller.instructions": "Wussten Si<PERSON> schon? neoshare AI kann Ihnen jetzt Titel- und Beschreibungsvorschläge für PDFs mit Textinhalt liefern.", "documentFiller.label": "Generieren Sie Titel und Beschreibung mit neoshare AI", "documentFiller.labelMaxAttemptsReached": "neoshare AI-Vorschläge sind auf 3 begrenzt.", "documentFiller.revert.button": "Z<PERSON>ücksetzen", "documentINbox.title": " Inbox ", "documentInbox.applyToDataRoom": "In Data Room speichern", "documentInbox.classificationDocument.originalFileName": "Ursprünglicher Dateiname: {$INTERPOLATION}", "documentInbox.classificationWarning": "Platzhalter wird bereits von einem Dokument besetzt", "documentInbox.copyMail.message": "In die Zwischenablage kopierte E-Mail-Adresse", "documentInbox.countSelected": "{$INTERPOLATION} ausgewählt", "documentInbox.dateOfUpload": "Hochgeladen am", "documentInbox.delete": "Löschen", "documentInbox.deleteModalTitle": "Ausgewählte Dateien löschen?", "documentInbox.download": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "documentInbox.dragAndDrop": "<PERSON><PERSON> per Drag & Drop hier ablegen", "documentInbox.edit": "Umbenennen", "documentInbox.emptyStateDescription": "oder auf “<PERSON>laden” klicken", "documentInbox.failedZipUpload.error.prefix": "von", "documentInbox.failedZipUpload.error.suffix": "<PERSON><PERSON> konnten nicht hochgeladen werden.", "documentInbox.fileName": "Namen", "documentInbox.filePresentPlaceholder": "<PERSON><PERSON> ho<PERSON>", "documentInbox.fileWithVirus.prefix": "<PERSON> von Ihnen hochgeladene Datei", "documentInbox.fileWithVirus.suffix": "wurde als potentiell gefährlich eingestuft und wieder entfernt", "documentInbox.folder": "<PERSON><PERSON><PERSON>", "documentInbox.groupTitle": " Dateien nach Gruppen sortieren ", "documentInbox.inboxAddress": "Inbox-E-Mail-Adresse", "documentInbox.label.deleteAll": "Alle löschen", "documentInbox.label.uploadFiles": "Hochladen", "documentInbox.mailTooltip.message": "Um Dateien per E-Mail an die Inbox zu senden, verwenden Sie bitte diese E-Mail-Adresse.", "documentInbox.message": "<PERSON>hre Datei wurde erfolgreich hochgeladen.", "documentInbox.more": "<PERSON><PERSON>", "documentInbox.noDocument": "Aktuell sind keine Unterlagen hochgeladen.", "documentInbox.noFilePlaceholder": "<PERSON><PERSON>", "documentInbox.onRemove.message": "Ausgewählte Dateien löschen?", "documentInbox.organization": "Organisation", "documentInbox.organize": "Organisieren", "documentInbox.originalFileName": "Ursprünglicher Dateiname", "documentInbox.palaceholder.search": "Suchen...", "documentInbox.placeholder": "Unterlagen suchen...", "documentInbox.preview": "Vorschau", "documentInbox.previewDataRoomStructure": "Vorschau der Datenraumstruktur für Fall ID: {$INTERPOLATION}", "documentInbox.previewDataRoomStructureDescription": "Überprüfen Sie vorab, an welcher Stelle Ihre Dateien abgelegt werden. Wählen Sie die entsprechenden Elemente aus, und bestätigen Sie mit „Übernehmen“.", "documentInbox.regenerate": "<PERSON><PERSON> gene<PERSON>", "documentInbox.rejectUpload.message": "Die von Ihnen hochgeladene Datei wurde als potentiell gefährlich eingestuft und wieder entfernt.", "documentInbox.search": "Suchen...", "documentInbox.selectedDocument": "ausgewählt", "documentInbox.selectedDocuments.files": " <PERSON><PERSON> ", "documentInbox.source": "<PERSON><PERSON>", "documentInbox.table.fileName": "Dateiname", "documentInbox.table.uploadedBy": "<PERSON><PERSON><PERSON><PERSON><PERSON> von", "documentInbox.table.uploadedOn": "Hochgeladen am", "documentInbox.timeoutMessage": "AI klassifiziert Speicherort und Bezeichnung von {filename}…", "documentInbox.title": "Inbox", "documentInbox.toggles.all": "Alle auswählen", "documentInbox.toggles.nameRework": "Namen bearbeiten", "documentInbox.toggles.sortGroup": "Gruppen sortieren", "documentInbox.tour.stepOne.descriptionOneText": "neoshare AI wählt automatisch den optimalen Ort für Ihre Dateien im Datenraum.", "documentInbox.tour.stepOne.descriptionThreeText": "Sie können mehrere Dateien gleichzeitig hochladen und im Datenraum strukturieren.", "documentInbox.tour.stepOne.descriptionTwoText": "Dokumente lassen sich einfach und präzise organisieren und platzieren.", "documentInbox.tour.stepOne.header": "Entdecken Sie die neue Inbox", "documentInbox.tour.stepOne.nextButtonText": "Entdecken", "documentInbox.tour.stepOne.subHeader": "Intelligent, schnell, m<PERSON><PERSON><PERSON>", "documentInbox.tour.stepThree.header": "Uploads vor der Übertragung prüfen", "documentInbox.tour.stepThree.nextButtonText": "Inbox betreten", "documentInbox.tour.stepThree.subHeader": "Kontrollieren Sie mithilfe der Vorschau, was fehlt oder bereits hochgeladen wurde.", "documentInbox.tour.stepTwo.header": "Laden Si<PERSON> mehrere Dateien gleichzeitig hoch", "documentInbox.tour.stepTwo.subHeader": "neoshare AI schlägt automatisch den passenden Namen und Speicherort vor.", "documentInbox.transferDocumentsSuccess": "Ausgewählte Dokumente erfolgreich gespeichert", "documentInbox.transferError": "Ein Fehler ist aufgetreten. Bitte versuchen Sie es erneut", "documentInbox.transferError.dataRoomStructure": "Die Dokumentenverarbeitung ist aufgrund von Änderungen in der Ordnerstruktur des Datenraums fehlgeschlagen. Bitte versuchen Sie es erneut", "documentInbox.transferError.unassignedDocument": "Nicht zugewiesene Dokumente können nicht im Datenraum gespeichert werden", "documentInbox.upload.error": "Problem beim <PERSON>", "documentInbox.uploadDate": "Uploaddatum", "documentInbox.uploadOptions.uploadFiles": "<PERSON><PERSON>", "documentInbox.uploadOptions.uploadFolder": "Ordner hochladen", "documentInbox.uploadedBy": "<PERSON><PERSON><PERSON><PERSON><PERSON> von", "documentPLaceHolder.confirmLabel": "Fortfahren", "documentPLaceholder.model.missingLabelMapping": "<PERSON><PERSON><PERSON><PERSON> Daten", "documentPLaceholder.model.missingLabelMapping.position": "Fehlende Position", "documentPLaceholder.model.missingLabelMapping.salutation": "<PERSON><PERSON><PERSON><PERSON>", "documentPLaceholder.model.missingLabelMapping.title": "<PERSON><PERSON><PERSON>", "documentPlaceHolder.text.message.confirmation": "<PERSON>n Sie den Editor für die Signaturplatzierung schließen, gehen alle Ihre Änderungen verloren. Sind <PERSON><PERSON> sic<PERSON>, dass Si<PERSON> fortfahren möchten?", "documentPlaceHolder.text.message.missingProperties": "<PERSON><PERSON> Unterzeichner muss entweder ein Platzhalter für die Signatur oder für seine Initialen zugewiesen werden.", "documentPlaceHolder.text.message.missingProperties.confirmLabel": "Schließen", "documentPlaceHolder.text.title.missingProperties": "Folgenden Unterzeichnern wurde kein Platzhalter für die Signatur zugewiesen.", "documentPlaceholder.emptyState.editFields": " <PERSON><PERSON> ", "documentPlaceholder.emptyState.emptyInfo": " <PERSON><PERSON>hen Sie einen Platzhalter von links in das Dokument oder wählen Sie einen bereits verwendeten Platzhalter aus. ", "documentPlaceholder.fullName": "vollständiger Name", "documentPlaceholder.groupHeader": " Signaturfelder ", "documentPlaceholder.groupHeader.alignment": " Ausrichtung ", "documentPlaceholder.groupHeader.autofill": " Autofill-Felder ", "documentPlaceholder.groupHeader.formatting": " Formatierung ", "documentPlaceholder.groupHeader.scaling": " Skalierung ", "documentPlaceholder.groupHeader.signerList": " Unterzeichnerliste ", "documentPlaceholder.label.email": "Email address*", "documentPlaceholder.label.initials": "<PERSON>en", "documentPlaceholder.label.name": "Name", "documentPlaceholder.label.organization": "Organisation", "documentPlaceholder.label.position": "Position", "documentPlaceholder.label.salutation": "<PERSON><PERSON><PERSON>", "documentPlaceholder.label.signature": "Signatur", "documentPlaceholder.label.signed": "Unterzeichnet am", "documentPlaceholder.label.title": "Titel", "documentPlaceholder.modal.title": " Signaturplatzierung ", "documentPlaceholder.model.company": "Organisation", "documentPlaceholder.model.signature": "Signatur", "documentPlaceholder.model.title": "Titel", "documentPreview.email": "E-Mail", "documentPreview.noPreview": " <PERSON><PERSON> Vorschau für diesen Dateityp verfügbar. ", "documentPreview.noPreview2": "<PERSON><PERSON>", "documentPreview.notApplicable": "N/A", "documentPreview.notRegisteredCompany": "Nicht registrierte Organisation", "documentPreview.organisation": " Organisation: ", "documentPreview.toast.info": "Dokumentenexport gestartet. Ihr Download sollte in wenigen Sekunden verfügbar sein.", "documentPreview.type": "Quelle:", "documentPreview.uploadedBy": " <PERSON><PERSON><PERSON><PERSON><PERSON> von: ", "downloadFilesAsZip": "Dokumentenexport", "downloadFilesAsZip.fileName.case": "Archiv-Fall-{$PH}.zip", "downloadFilesAsZip.fileName.company": "Archiv-company-{$PH}.zip", "downloadFilesAsZip.toast.info": "Dokumentenexport gestartet. Ihr Download sollte in wenigen Sekunden verfügbar sein.", "dracoonIntegration.toast.error": "Die Daten sind nicht korrekt", "dracoonMessage": "DRACOON ist als Cloud-, Hybrid- und On-premise-Lösung (auf Anfrage) verfügbar und bietet alle nötigen Komponenten für eine sichere Datenübertragung.", "drag.drop.here": "Drag-and-Drop <PERSON><PERSON> hier", "dragAndDrop": " Drag & Drop ", "dragAndDrop.subtitle": " Lade Dateien durch Drag-and-drop in dieses Fenster hoch ", "dragAndDrop.title": "Drag-and-Drop", "draggableInboxDocument.warning": " Dokument bereits vorhanden. ", "draggableInboxDocument.warning2": " Dokument bereits vorhanden: ", "dropDownOptions.config.addNewOption": "Option hinzufügen", "duplicate-case.header": " Finanzierungsfall kopieren ", "duplicate-case.tabNav.tab.administration": "Verwaltung", "duplicate-case.tabNav.tab.basicInfo": "Basisinformationen", "duplicate-case.tabNav.tab.dataRoom": "Data Room", "duplicate-case.tabNav.tab.kollaboration": "Kollaboration", "duplicateBusinessCase.financingDetailsGroups": "Wenn Sie alle Gruppen auswählen, übernehmen Sie deren Inhalt in den neuen Finanzierungsfall. Andernfalls legen Sie den neuen Fall mit einer leeren Finanzierungsstruktur an.", "duplicateBusinessCase.financingDetailsGroups.title": "Finanzierungsdetails Gruppen", "duplicateCase.participant.label.amount": " Gesamtbeteiligung ", "duplicateCase.participant.label.copy": " <PERSON><PERSON><PERSON>s ", "duplicateCollaboration.applicantCriteria.header": " Bewerberkriterien & Teilnehmeranforderungen ", "duplicateCollaboration.myPartners.header": " Meine Partner ", "duplicateDataRoom.contentHeader.date": " Hochgeladen am ", "duplicateDataRoom.contentHeader.fileName": " Dateiname ", "duplicateDataRoom.contentHeader.name": " <PERSON><PERSON><PERSON><PERSON><PERSON> von ", "duplicateDataRoom.contentInformation.description": " Einstellungen für neue Teilnehmer: ", "duplicateDataRoom.contentInformation.description2": " Einstellungen für neue Fallinteresierte: ", "duplicateDataRoom.contentInformation.visibility.notVisible": "<PERSON><PERSON>", "duplicateDataRoom.group.title": " Data Room Gruppen ", "duplicateDataRoom.header.description": "<PERSON>n nicht ausgewählt, werden die Standardeinstellungen für neue Teilnehmer angewendet", "duplicateDataRoom.header.title": " Aktuelle Gruppensichtbarkeit für jeden Teilnehmer, der kopiert wird, beibehalten ", "duplicateDataRoom.inboxDocument.header.description": "<PERSON><PERSON><PERSON>, um die Dokumente auszuwählen, welche Si<PERSON> kopieren möchten", "duplicateDataRoom.inboxDocument.header.title": " Fall Data Room Inbox kopieren ", "duplicateDataRoom.information": " Data Room Einstellungen ", "duplicateDataRoom.mainInformationMessage": " Es werden immer alle Felder des Data Rooms in den neuen Finanzierungsfall kopiert. Bei ausgewählten Feldern werden auch die Informationen und Anhänge kopiert. ", "duplicateManagement.emptyManagement": " Es sind keine Informationen vorhanden. ", "duplicateManagement.sectionTitle.FAQ": " FAQ ", "duplicateManagement.sectionTitle.customerPortalUser": " Kundenportalnutzer ", "duplicateManagement.sectionTitle.customerPortalUser.realEstate": "Partnerportalnutzer", "edit.customer.user.snapshot.modal.title": "Kundennutzer bearbeiten", "edit.snapshot.title": "Allgemeine Informationen bearbeiten", "editField.existingChatWarning.text": " <PERSON><PERSON> dieser Information gibt es eine laufende Diskussion. <PERSON><PERSON> Sie das Feld löschen, wird die Diskussion archiviert. ", "editField.label": "Bezeichnung", "editField.mirroredField": "<PERSON><PERSON>ld ist mit den Finanzierungsdetails synchronisiert und kann daher nicht bearbeitet werden", "editFinStructFieldErrorMsg": " Beschreibung konnte nicht festgelegt werden", "email.adress.optional": "E-Mail-Adresse (optional)", "error.notFound.description": "Seite nicht gefunden", "error.notFound.text": "<PERSON><PERSON> 404", "errorMessage.companyEmailTaken": "Diese E-Mail-Adresse wird bereits für einen Ansprechpartner von {$PH} verwendet. Bitte geben Si<PERSON> eine andere E-Mail-Adresse an oder entfernen Sie den Ansprechspartner.", "errorMessage.organisationEmailTaken": "Diese E-Mail-Adresse wird bereits für ein Nutzerkonto von {$PH} verwendet. Bitte geben Sie eine andere E-Mail-Adresse an.", "excelSpreadsheetDownload.toast.error": "Es ist ein Fehler aufgetreten, bitte versuchen Si<PERSON> es erneut.", "excelSpreadsheetDownload.toast.info": "Excel-Export gestartet. Ihr Download sollte in wenigen Sekunden verfügbar sein.", "existingChat.dialog.message": "<PERSON><PERSON> in den Chat aufgenommen werden möchten, wenden <PERSON> sich bitte an den Fallinhaber.", "existingChat.dialog.title": "Es gibt bereits einen Chat zu diesem Thema.", "expandedSelect.list.toggleAll": "Alle auswählen", "facilityAmounts.facilityTotal.label": " Summe Fazilitäten ", "facilityAmounts.stats.totalAmount": "Gesamtbetrag angleichen", "facilityAmounts.targetTotal.label": " Festgelegter Gesamtbetrag ", "facilityField.actionsMenu.item.editField": "<PERSON><PERSON> bear<PERSON>ten", "facilityField.actionsMenu.item.revisions": "Revisionen", "facilityField.basicInfo.guaranteeCompanySearch": " Garantiegeber", "facilityField.basicInfo.loanPurpose": " Verwendungszweck", "facilityField.basicInfo.targetCompany": " Zielgesellschaft", "facilityField.edit.field": "<PERSON><PERSON>", "facilityField.revisionsTable.columns.hours": "Uhrzeit", "faq.checkbox.text": "Ansprechpartner aller laufenden Bewerbungen und Einladungen über Änderung benachrichtigen", "faq.control.answer": "Antwort", "faq.control.question": "Frage", "faq.deleteFaq": "Möchten Sie diese Frage löschen?", "faq.toast.error.create": "Fehler beim Erstellen des FAQs", "faq.toast.error.edit": "Fehler beim Aktualisieren des FAQs", "faq.toast.success.create": "FAQ erfolgreich angel<PERSON>t", "faq.toast.success.edit": "FAQ erfolgreich aktualisiert", "faq.toastMessage.deleteMessage.error": "Fehler beim Löschen des FAQs", "faq.toastMessage.deleteMessage.success": "FAQ erfolgreich gel<PERSON>t", "fieldINformation.tooltipText": "Dieses Eingabefeld ist mit einem Wert aus dem Data Room verknüpft. Wenn Si<PERSON> auf das Icon klicken, wird die Bearbeitung des Feldes freigeschaltet. Bei der Eingabe eines neuen Wertes wird die Verknüpfung mit dem Data Room des Hauptfalls aufgehoben.", "fieldInformation.companyPortalAdditionalTooltip.notFieldVisibleCompanyPortal": "<PERSON><PERSON>r Nutzer des Kundenportals nicht sichtbar", "fieldInformation.companyPortalAdditionalTooltip.notFieldVisibleCompanyPortal.realEstate": "<PERSON><PERSON><PERSON>er des Partnerportals nicht sichtbar", "fieldInformation.companyPortalAdditionalTooltip1": "Vom Nutzer des Kundenportals benötigte Informationen", "fieldInformation.companyPortalAdditionalTooltip1.realEstate": "Vom Nutzer des Partnerportals benötigte Informationen", "fieldInformation.companyPortalAdditionalTooltip111.notVisible.realEstate": " <PERSON><PERSON><PERSON>er des Partnerportals nicht sichtbar", "fieldInformation.companyPortalAdditionalTooltip111.visible": " <PERSON><PERSON>r Nutzer des Kundenportals sichtbar", "fieldInformation.companyPortalAdditionalTooltip111.visible.realEstate": " <PERSON><PERSON><PERSON>utzer des Partnerportals sichtbar", "fieldInformation.companyPortalAdditionalTooltip112.notVisible": " <PERSON><PERSON>r Nutzer des Kundenportals nicht sichtbar", "fieldInformation.companyPortalAdditionalTooltip2": "'Vom Nutzer des Kundenportals bereitgestellte Informationen", "fieldInformation.companyPortalAdditionalTooltip2.realEstate": "'Vom Nutzer des Partnerportals bereitgestellte Informationen", "fieldInformation.companyPortalAdditionalTooltip3": "<PERSON><PERSON>r Nutzer des Kundenportals sichtbar", "fieldInformation.companyPortalAdditionalTooltip3.realEstate": "<PERSON><PERSON><PERSON>utzer des Partnerportals sichtbar", "fieldInformation.populatedFromFieldAdditionalInfo": "Diese Daten können bei Bedarf erneut angefordert werden", "fieldInformation.populatedFromFieldLabel": "Daten bereitgestellt von:", "fieldInformation.requestFieldLabel": "<PERSON><PERSON>", "fieldInformation.requestedFromFieldAdditionalInfo": "Die Anforderung kann zurückgenommen werden", "fieldInformation.requestedFromFieldLabel": "<PERSON><PERSON> ange<PERSON>ert von:", "fieldInformation.syncedWithDataRoom": "<PERSON><PERSON> synchronisiert", "fieldInformation.syncedWithDataRoom:Mit Datenraum synchronisiert": "<PERSON><PERSON> synchronisiert", "fieldInformation.syncedWithFinancingDetails": "Mit Finanzierungsdetails synchronisiert", "fieldInformation.syncedWithFinancingDetails:Mit Finanzierungsdetails\n        synchronisiert": "Mit Finanzierungsdetails synchronisiert", "fieldInformation.tooltip.main": " Kundenportaleinstellungen ", "fieldInformation.tooltip.main.realEstate": "Partnerportaleinstellungen", "fieldModal.addFieldLabel": "<PERSON><PERSON>", "fieldModal.fieldRework": "<PERSON><PERSON> bear<PERSON>ten", "fieldModal.revision": "Revisionen", "fileUpload.success": "wurde erfolgreich hochgeladen.", "fileUploader.toast.error.fileAlreadySelected": "Datei bereits ausgewählt.", "fileUploader.toast.error.maxFileCount.prefix": "Maximale Dateianzahl erreicht. Es können maximal", "fileUploader.toast.error.maxFileCount.suffix": "gleichzeitig hochgeladen werden.", "fileUploader.toast.error.unsupportedType.prefix": "Nicht unterstützter Dateityp. Unterstützte Dateitypen sind:", "fin-structure.remove-block.warning": " <PERSON>n <PERSON> fort<PERSON>hren, werden alle Daten zu dem Finanzierungsbaustein endgültig gelöscht. Sind Si<PERSON> sich sicher, dass Sie den Finanzierungsbaustein löschen möchten? ", "financialStructure.table.columns.availabilityPeriod": "Inanspruchnahmezeitraum", "financialStructure.table.columns.facility": "Fäzilitat", "financialStructure.table.columns.interestRate": "Zinssatz / Avalprovision", "financialStructure.table.columns.loanDuration": "Laufzeit", "financing.details.hint": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Gesamtzinssatz p.a., Gesamtlaufzeit bis, 1. Zinsbindung bis und Valutierung sind Pflichtfelder für den Datenexport ins Kernbankensystem.", "financingPartners.addPartner": "Finanzierungspartner hinzufügen", "financingPartners.alreadyAddedPartner": "Der Finanzierungspartner wurde bereits zum Finanzierungsbaustein hinzugefügt.", "financingPartners.emptyMessage": "<PERSON><PERSON>er wurde noch kein Finanzierungspartner hinzugefügt.", "financingPartners.financingPartner": "Finanzierungspartner", "financingPartners.financingPartner.tooltip": "Als Finanzierungspartner gelten alle kreditgebenden Personen, Unternehmen und Institute sowie der Kreditgeber selbst. Sobald ein Finanzierungspartner hinzugefügt wurde, wird er aus den Auswahlmöglichkeiten entfernt.", "financingPartners.forExample": "b.z. 30 %", "financingPartners.participation": "<PERSON><PERSON><PERSON><PERSON> ", "financingPartners.participationBelowThreshold": "Der Gesamtbetrag der finanziellen Beteiligung liegt derzeit {$START_TAG_STRONG}unter 100%.{$CLOSE_TAG_STRONG}", "financingPartners.updateFailure": "Daten des Finanzierungspartners konnten nicht aktualisiert werden", "financingPartners.updateSuccess": "Daten des Finanzierungspartners erfolgreich aktualisiert", "financingStructure.controlLabel": " <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ", "firmekunden.overview.editCompany.contactPeople.list": "Ansprechpartner anzeigen", "firmenkunden.companyEdit.checkbox": "Zugang zum Kundenportal", "firmenkunden.companyEdit.checkbox.realEstate": "Zugang zum Partnerportal", "firmenkunden.companyEdit.nonExistingIndrustry": "Branche hinzufügen", "firmenkunden.companyEdit.nonExistingLegalForm": "Rechtsform hinzufügen", "firmenkunden.companyEdit.partner.errorMessage.invalidDate": "Datum ist ungültig", "firmenkunden.companyEdit.partner.errorMessage.unacceptableSymbol": "Unzulässiges Symbol", "firmenkunden.companyEdit.partner.errorMessage.unacceptableSymbol1": "Unzulässiges Symbol", "firmenkunden.companyEdit.partner.info.button.addContactPerson": "<PERSON><PERSON>", "firmenkunden.companyEdit.partner.info.otherRoleSelected": "<PERSON><PERSON> e<PERSON>ben", "firmenkunden.companyEdit.partner.info.removeContactPerson": "Löschen", "firmenkunden.companyEdit.partner.info1": "Vorname*", "firmenkunden.companyEdit.partner.info2": "Nachname*", "firmenkunden.companyEdit.partner.info3": "Telefonnummer", "firmenkunden.companyEdit.partner.info4": "E-Mail-Adresse*", "firmenkunden.companyEdit.partner.info5.dropdown": "<PERSON><PERSON>", "firmenkunden.companyEdit.table.field1": "Unternehmen", "firmenkunden.companyEdit.table.field10": "Steuer-ID", "firmenkunden.companyEdit.table.field11": "Steuern<PERSON>mer", "firmenkunden.companyEdit.table.field12": "Umsatzsteuer-ID", "firmenkunden.companyEdit.table.field13": "Gründungsdatum", "firmenkunden.companyEdit.table.field2": "Unternehmensname", "firmenkunden.companyEdit.table.field3": "Rechtsform", "firmenkunden.companyEdit.table.field4": "Branche", "firmenkunden.companyEdit.table.field5": "Registergericht", "firmenkunden.companyEdit.table.field6": "Registernummer", "firmenkunden.companyEdit.table.field7": "<PERSON><PERSON><PERSON>", "firmenkunden.companyEdit.table.field8": "Eingetragen am", "firmenkunden.companyEdit.table.field9": "Auszug vom", "firmenkunden.companyEdit.table.info.exportFields": "Steuer-ID, Steuernummer und Gründungsdatum sind Pflichtfelder für den Datentransfer ins Kernbankensystem.", "firmenkunden.contactPeople.title": " <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ", "firmenkunden.editCompany.title1": "Unternehmen bearbeiten", "firmenkunden.editCompany.title2": "Unternehmen anlegen", "firmenkunden.overview.button.label": "Unternehmen anlegen", "firmenkunden.overview.noDataMessage": "<PERSON><PERSON> gefunden", "firmenkunden.overview.placeholder": "Unternehmen suchen...", "firmenkunden.table.header1": "Status", "firmenkunden.table.header2": "Firmenname", "firmenkunden.table.header3": "<PERSON><PERSON><PERSON>", "fluidTableCheckbox.emptyMessage": "<PERSON><PERSON>", "folderDeleteModal.description": "Alle beinhalteten Dateien und Unterordner werden permanent entfernt und\r\n      können nicht wiederhergestellt werden.", "folderDeleteModal.title": "Sind <PERSON> sic<PERSON>, dass Sie den Ordner löschen möchten?", "folderStructure.actions.showInEnclosing": "Im beigefügten Ordner anzeigen", "folderStructure.deleteFolderModal.toast.success": "Ordner erfolgreich <PERSON>t", "folderStructure.dragDropPlaceholder.afterBtn": ", um Dokumente und Ordner per Drag and <PERSON> hi<PERSON>uzufügen, oder laden Si<PERSON> eine Datei von Ihrem Computer hoch.", "folderStructure.dragDropPlaceholder.filterActive": "Hinzufügen ist bei aktiven Filtern deaktiviert. Entfernen Sie die Filter, um Elemente hinzuzufügen.", "folderStructure.dragDropPlaceholder.searchActive": "Hinzufügen ist bei aktiver Suche deaktiviert. Entfernen Sie den Suchbegriff, um Elemente hinzuzufügen.", "folderStructure.dragDropPlaceholder.searchAndFilterActive": "Hinzufügen ist bei aktiven Filtern und Suche deaktiviert. Entfernen Sie beide, um Elemente hinzuzufügen.", "folderStructure.emptyFolder": "Dieser Ordner ist leer", "folderStructure.folder.actions.delete": "Löschen", "folderStructure.folder.actions.move": "Verschieben", "folderStructure.folder.actions.rename": "Umbenennen", "folderStructure.listView.columns.dateUpdated": "Aktualisiert am", "folderStructure.listView.columns.fileSize": "Dateigröße", "folderStructure.listView.columns.name": "Name", "folderStructure.manageFolderModal.folderName.errors.doesNotEndWith": "Datei- oder Ordnernamen dürfen nicht enden mit: {$INTERPOLATION}", "folderStructure.manageFolderModal.folderName.errors.folderNameUnique": "Es existiert bereits ein Ordner / Dokument mit gleichem Namen. Bitte wählen Si<PERSON> einen anderen", "folderStructure.manageFolderModal.header.add": "Ordner hinzufügen", "folderStructure.manageFolderModal.header.rename": "Ordner umbenennen", "folderStructure.manageFolderModal.toast.createSuccess": "Ordner erfolgreich erstellt", "folderStructure.manageFolderModal.toast.renameSuccess": "Ordner erfolgreich umbenannt", "folderStructure.move.toast.failure.generalMessage": "Fehler beim Verschieben des Dokuments oder des Ordners", "folderStructure.move.toast.success": "Ordner / Dokument erfolgreich verschoben", "folderStructure.sectionTitle": "Dokumente", "folderStructure.toast.failure.folderCannotBeMoved": "Ordner kann nicht in sich selbst oder einer seiner Unterordner verschoben werden", "folderStructure.toast.failure.folderNotExist": "Ordner wurde bereits gelöscht", "folderStructure.toast.failure.missingPermission": "Unzureichende Berechtigungen. Bitte wenden Si<PERSON> sich an den Fallinhaber für weitere Informationen", "folderStructure.toast.failure.sameLocation": "Ordner oder Dokument befindet sich bereits im Zielordner", "folderStructure.viewModeToggle.gridView": "Gitteransicht", "folderStructure.viewModeToggle.listView": "Listenansicht", "footer.text": "Weisungsempfänger", "footer.text.copyright": "Copyright © 2025 neoshare AG. Alle Rechte vorbehalten.", "footer.text.imprint": "Impressum", "footer.text.privacy-policy": "Datenschutz", "fs.selectParticipant": "Organisation auswählen", "fs.subGroups": "Subgruppen", "global.multiple.active": "aktiv", "global.single.active": "aktiven", "globalTemplateTranslation": "Vorlage", "graph.amounts.1.5mrd": "1 - 1.5 Mrd. Euro", "graph.amounts.100mil": "50 - 100 Mio. Euro", "graph.amounts.10mil": "5 - 10 Mio. Euro", "graph.amounts.1mrd": "500 Mio. - 1 Mrd. Euro", "graph.amounts.20mil": "10 - 20 Mio. Euro", "graph.amounts.2mrd": "1.5 - 2 Mrd. <PERSON>", "graph.amounts.300mil": "100 - 300 Mio. Euro", "graph.amounts.500mil": "300 - 500 Mio. Euro", "graph.amounts.50mil": "20 - 50 Mio. Euro", "graph.amounts.5mil": "0 - 5 Mio. Euro", "graph.amounts.over2mrd": "> 2 Mrd. Euro", "graph.graphFilters.button.label.apply": "<PERSON><PERSON><PERSON>", "graph.graphFilters.button.name": "close", "graph.graphFilters.label": "Beziehung", "graph.graphFilters.label.role": "<PERSON><PERSON>", "graph.graphFilters.label.showSharesFilter": " Aktienanteil ", "graph.graphFilters.title": "Netzwerkfilter", "graph.graphFilters.uiSelect.allRoles": "Alle Rollen", "graph.graphFilters.uiSelect.placeholder": "Alle Beziehungen", "graph.legend.header": "<PERSON><PERSON>", "graph.legend.label": " Großaktionär (> 50%) ", "graph.legend.label2": " Kleinaktionär (<= 50%) ", "graph.legend.label3": "Inaktiv", "graph.legend.label4": "Person", "graph.legend.label5": "Unternehmen", "graph.legend.label6": "Inaktive Person", "graph.legend.label7": " Inaktives Unternehmen ", "graph.legend.label8": "% Aktienanteil", "graph.legend.sectionTitle": " Art des Eingentums ", "graph.legend.sectionTitle2": " Art der Eigentümer ", "graph.tutorialOverlay.message": " Sie können das Firmennetzwerk mit der Maus oder der Zoomfunktion auf der rechten Seite vergrößern. ", "graph.uboGraph.noDataMessage": "Eine zweifelsfreie Ermittlung des/der wirtschaftlich Berechtigten ist auf Grund der vorliegenden Informationen nicht möglich.", "groupModal.navLink": "Gruppe bearbeiten", "groupPortal.actions.modal.all": "Alle Gruppen", "groupPortal.actions.modal.text": " Kontrollieren Sie die Datensichtbarkeit, fordern Sie Informationen für Bereiche in Ihrem Datenraum an und überprüfen Sie deren Status. ", "groupPortal.modal.title": "Kundenportal- Freigabe", "groupPortal.modal.title.realEstate": "Partnerportalfreigabe", "groupPortal.tooltip.text": "Kundenportalaktionen", "groupPortal.tooltip.text.realEstate": "Partnerportalaktionen", "groupVisibility.card.span": "<PERSON><PERSON><PERSON> verwalten", "groupVisibility.statusInfo.inviteeAndApplicant.tooltipDescription": "<PERSON><PERSON>r eingeladene Organisationen, Bewerber, Teilnehmer und Fallinhaber verfügbar", "groupVisibility.statusInfo.inviteeAndApplicant.tooltipTitle": "Fallinteressierte", "groupVisibility.statusInfo.private.tooltipDescription": "<PERSON><PERSON><PERSON>", "groupVisibility.statusInfo.private.tooltipDescription.isCadr": "<PERSON><PERSON><PERSON>aber verfügbar", "groupVisibility.statusInfo.tooltipDescription.isCadr": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> und Direkt Geteilte verfügbar", "groupVisibility.statusInfo.tooltipTitle.isCadr": "Teilnehmer und Direkt Geteilte", "groupVisibilityStatusInfo.tooltipDescription": "Alle Organisationen mit Zugriff auf diesen Fall können diese Gruppe sehen", "groupVisibilityStatusInfo.tooltipDescription.isCadr": "Alle Organisationen mit Zugriff auf diesen Datenraum können diese Gruppe sehen", "groupVisibilityStatusInfo.tooltipDescription.participant": "<PERSON><PERSON><PERSON> und Fallinhaber verfügbar", "groupVisibilityStatusInfo.tooltipTitle": "<PERSON><PERSON>rä<PERSON>", "imageCropper.adjustProfilePhoto.message.tooltip": "<PERSON><PERSON><PERSON> zum Bewegen", "integrationForm.backButton.label": "<PERSON><PERSON>", "integrationForm.clientId": "Client ID", "integrationForm.clientSecret": "Client Secret", "integrationForm.container": "Kunden-Instanz", "integrationForm.header": " Verbinden Sie Dracoon in neoshare ", "integrationGuide.clickOnTheseButtons": "Klicken Sie auf diese Buttons in folgender Reihenfolge:", "integrationGuide.clientId": "Client ID", "integrationGuide.clientSecret": "Client Secret", "integrationGuide.exampleText": "z.B. : SsyLWUseK[...]saUCXZBiznGgZL", "integrationGuide.field": " <PERSON><PERSON><PERSON> Sie Dracoon in einem neuen Tab\r\n", "integrationGuide.field.copyDracoon": "Kopieren Sie den folgenden Link und fügen Sie diesen in das Eingabefeld für die Weiterleitungs-URL in Dracoon ein", "integrationGuide.field.copyNeoshare": "<PERSON><PERSON>ren Sie die folgenden Werte und fügen Sie diese in die Eingabefelder auf neoshare ein:", "integrationGuide.guideStep.label": "Einstellungen", "integrationGuide.guideStep.label.apps": "Apps", "integrationGuide.guideStep.label.apps.custom": "Benutzerdefinierte Apps", "integrationGuide.header": " So verbinden Sie Ihren neoshare-Account mit Dracoon\r\n", "integrationGuide.instance": "Kunden-Instanz", "integrationLoading.button.label": "Verstanden", "integrationLoading.text": " Derzeit wird die Bewerbung zu Ihrem neoshare Konto hinzugefügt ", "integrationLoading.text.basicFlow": " <PERSON>te beachten Si<PERSON>, dass jede Änderung der hochgeladenen Dokumente in Dracoon die Verbindung beschädigt und Sie diese wahrscheinlich verlieren werden. Jede neue Synchronisierung löscht Ihre vorherigen Uploads. Die Synchronisation der Dokumente kann bis zu 10-15 <PERSON>uten dauern. ", "interactiveList.placeHolder": "Bedingung hinzufügen", "internalBilateral.modal.placeholder": "<PERSON>utzer eingeben oder auswählen...", "internalChat.title": "Internen Cha<PERSON> erstellen", "invalid.email.error": "Ungültige E-Mail-Adresse", "invalid.email.wrongFormat": "Ungültige E-Mail-Adresse", "invalid.username.error": "Ungültige E-Mail-Adresse", "invitation.accepted.status": "Einladung angenommen", "invitation.canceled.status": "Einladung abgebrochen", "invitation.declined.status": "Einladung abgelehnt", "invitation.expired.status": "Einladung abgelaufen", "invitation.not.available.error.message": "Die Einladung ist nicht mehr verfügbar", "invitation.pending.status": "Einladung geschickt", "invitation.receive.status": "Einladung erhalten", "invitationOrApplication.submitted.status": "In Bearbeitung", "invitations.sortFields.creationDate": "Erstellungsdatum", "invitations.sortFields.customerName": "<PERSON><PERSON>ame", "invite.person.message": "Wenn Sie die Annahme oder Ablehnung von Einladungen zur Teilnahme an einem Projekt umleiten möchten, können Sie eine Person aus Ihrer Organisation einladen, die das Projekt verwalten kann.", "invite.person.passing.on.case": "Sie können die Einladung zur Übernahme des Projekts an eine weitere Person aus Ihrer Organisation weiterleiten. Diese Person kann die Einladung annehmen oder ablehnen und die Projektleitung übernehmen", "invite.to.apply.hint.message": "Sie sind zu diesem Finanzierungsfall eingeladen worden. Wenn Sie daran teilnehmen möchten, klicken Sie auf \"Bewerbung abschicken\".", "invite.to.apply.next.button.label": "<PERSON><PERSON>", "inviteToNeoSHare": "<PERSON><PERSON> neoshare e<PERSON>n", "invited.users.label": "EINGELADENE NUTZER", "invited.users.without.permissions.error": "Die eingeladenen Nutzer haben keine Rechte, <PERSON><PERSON><PERSON> einzu<PERSON>hen und an denen teilzunehmen", "invoiceTable.confirmLabel": "Rechnung stornieren", "invoiceTable.message": "<PERSON>te beachten Sie, dass der Vorgang ist nicht rückgängig gemacht werden kann.", "invoiceTable.noDataMessage": "keine Rechnungen vorhanden.", "invoiceTable.title": "Rechnung stornieren", "key1": "existing translation de 1", "key4": "existing translation de 4", "keyInformationExtraction.autofill.popover.description": "Vorschläge für {$INTERPOLATION}-Felder sind zum automatischen Ausfüllen bereit.", "keyInformationExtraction.autofill.popover.dismiss": "<PERSON><PERSON><PERSON><PERSON>", "keyInformationExtraction.autofill.popover.title": "Autofill", "keyInformationExtraction.autofill.popover.use": "Verwenden", "keyInformationExtraction.infoMessage.dismiss": "<PERSON><PERSON><PERSON>", "keyInformationExtraction.infoMessage.label": "Akzeptieren Sie oder lehnen Sie Feldeingabevorschläge ab und sehen Si<PERSON> deren Quellen an.", "keyInformationExtraction.source": "Quelle:", "kpis.button.label.disable": "Deaktivieren", "kpis.button.label.enable": "Aktivieren", "kpis.confirmationDialog.disable.additionalInfo": "Ausgeschaltete KPIs können im Reiter \"Inaktiv\" wieder aktiviert werden.\r\n\r\n", "kpis.confirmationDialog.disable.successToast": "KPI wurde erfolgreich deaktiviert", "kpis.confirmationDialog.disable.title": "Sind Si<PERSON> sicher, dass Sie diesen KPI deaktivieren wollen?", "kpis.confirmationDialog.enable.additionalInfo": "Aktivierte KPIs finden Sie im Reiter \"Aktiv\".", "kpis.confirmationDialog.enable.successToast": "KPI wurde erfolgreich aktiviert", "kpis.confirmationDialog.enable.title": "Sind <PERSON> sicher, dass Si<PERSON> diesen KPI aktivieren wollen?", "kpis.confirmationDialog.message": "Eine Benachrichtigung wird an alle Plattformnutzer Ihrer Organisation gesendet.", "label.data": "Daten", "label.group": "Gruppe", "label.inProgress": "In Bearbeitung", "label.item": "{$INTERPOLATION} Element", "label.items": "{$INTERPOLATION} Elemente", "label.placeholder": "Platzhalter", "label.selectGroup": "Gruppe auswählen", "label.userType": "<PERSON><PERSON><PERSON><PERSON>", "labels.appraiser": "<PERSON><PERSON><PERSON><PERSON>", "labels.collaborator": "Collaborator", "labels.comment": "Kommentieren", "labels.description": "Beschreibung", "labels.invitation-details": "Details zur Einladung", "labels.leader": "Leader", "labels.participant": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "labels.participation-type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "labels.structurer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "labels.theme": "<PERSON>a", "labels.user": "<PERSON><PERSON><PERSON>", "layout.mainLayout.createBusinessCaseTxt": "Fall erstellen", "layout.mainLayout.icon.add": "add", "layout.mainLayout.tooltip.text": "Neuer Fall", "linkedCases.table.config.linked": "Verlinkt", "locationField.placeholder": "<PERSON><PERSON><PERSON> e<PERSON>", "locationPreview.noAddress": " <PERSON><PERSON> ange<PERSON>ben ", "locationPreview.showOnMap": "<PERSON><PERSON> anzeigen", "login-title": "Anmelden", "login.currentPasswordIncorrect": "Ungültige Passwort!", "login.currentPasswordIsIncorrect": "Das Passwort kann nicht geändert werden, da das alte Passwort falsch ist.", "login.duplicateDeviceName": "Ändern Sie bitte den Namen Ihres Geräts", "login.entityCard.change": "Ändern", "login.entityCard.device": "G<PERSON><PERSON>", "login.entityCard.hello": "Hall<PERSON>", "login.errorMessage.expired.confirm.device.link": "Ihr Authentifizierungslink ist abgelaufen. Fordern Sie bitte einen neuen an.", "login.errorMessage.resetPasswordFromDifferentDevice": "<PERSON><PERSON>, das Passwort von einem anderen Gerät aus zurückzusetzen. Kehren Sie zum ursprünglichen Gerät zurück oder beginnen Si<PERSON> erneut.", "login.errorMessage.wrongAuthenticationDevice": "<PERSON><PERSON>, von einem anderen Gerät aus zuzugreifen.  Bitte verwenden Sie das ursprüngliche Gerät oder starten Sie den Vorgang erneut.", "login.errorMessage.wrongDevice": "<PERSON><PERSON>, von einem anderen Gerät aus zuzugreifen. Bitte verwenden Sie das ursprüngliche Gerät oder starten Sie den Vorgang erneut.", "login.footer.text": "Leistungsstarker Verschlüsselungsschutz", "login.footer.text.migrated.users": "Um Ihnen weiterhin ein Höchs<PERSON>ma<PERSON> an Sc<PERSON>tz zu bieten, haben wir unsere Sicherheitsmaßnahmen aktualisiert. Detaillierte Informationen zu diesenUpdates finden Sie in unserem", "login.generalError": "Ein Fehler ist aufgetreten. Versuchen Sie es bitte erneut.", "login.invalid.email.address": "Ungültige E-Mail-Adresse", "login.invalidDeviceAuth": "Ihr Browser/Ihr Gerät ist nicht authentifiziert. Bitte kopieren Sie den Link und fügen Si<PERSON> ihn in einen Browser oder ein Gerät ein, bei dem Sie authentifiziert sind.", "login.mfaCodeExpired": "Sicherheitscode abgelaufen. Sie haben noch {$INTERPOLATION} Versuche.", "login.mfaCodeExpiredSingular": "Sicherheitscode abgelaufen. Sie haben noch 1 Versuch.", "login.mfaCodeRequested": "Ihr Sicherheitscode wurde gesendet! Bitte prüfen Sie Ihre E-Mail.", "login.passwordCannotBeSameAsOld": "Das neue Passwort muss sich von dem vorherigen Passwort unterscheiden.", "login.passwordConfirmBtn": "Übernehmen", "login.registerNewDeviceSuccess": "Wenn ein Konto mit dieser E-Mail-Adresse existiert, erhalten Sie eine Authentifizierungs-E-Mail für Ihr Gerät.", "login.requestResetPasswordSuccess": "<PERSON>e sollten in Kürze eine E-Mail mit weiteren Anweisungen erhalten.", "login.reset-password.current-password": "Aktuelles Passwort", "login.reset-password.new-password": "Neues Passwort", "login.reset-password.one-lower-case": "Ein Kleinbuchstabe", "login.reset-password.one-number": "<PERSON><PERSON>", "login.reset-password.one-special-symbol": "Ein Sonderzeichen", "login.reset-password.one-upper-case": "Ein Großbuchstabe", "login.reset-password.repeat-password": "Passwort bestätigen", "login.reset-password.requirements-not-met": "Passwort entspricht nicht den Sicherheitsanforderungen", "login.reset-password.rules.title": "Das Passwort muss Folgendes enthalten:", "login.reset-password.title": "Passwort zurücksetzen", "login.reset-password.twelve-letters": "Mindestens 12 Zeichen", "login.reset-password.unauthorize-devices-modal": "Nach der Aktualisierung Ihres Passworts melden Sie sich bitte sofort ab, um Ihr Konto mit den neuen Zugangsdaten zu sichern.", "login.reset.password.logout.all.devices": "Alle Geräte abmelden", "login.reset.password.logout.device": "Gerät abmelden", "login.resetPassword.passwordsDoNotMatch": "Passwort stimmt nicht überein.", "login.resetPasswordFailure": "Das Passwort ist zu schwach oder wird regelmäßig verwendet. Bitte verwenden Sie ein stärkeres Passwort", "login.resetPasswordSuccess": "Password erfolgreich zurückgesetzt", "login.selectOrganizationLabel": "Organisation wählen", "login.termOfUse.privacy-policy": "Rechtsgrundlage der Datenverarbeitungen ist Ihre Einwilligung nach Art.6 Abs. 1 S. 1 lit. a DSGVO. Weitere Informationen zur Datenverarbeitung finden Sie in der {$START_LINK}Datenschutzerklärung{$CLOSE_LINK}. ", "login.termOfUse.title": "Einwilligungserklärung", "login.unlockAccountSuccess": "Ihr Konto wurde erfolgreich entsperrt!", "login.user.validation.forgot.password": "Sie haben Ihr Passwort vergessen?", "login.user.validation.password": "Passwort", "manageGroup.addGroup": "Gruppe hinzufügen", "manageGroup.label": "Gruppenname", "manageGroup.label.footer": "Gruppe", "manageLinkedCases.description": "<PERSON>e können diesen Finanzierungsfall nicht verwalten, da <PERSON>e darin nicht als Nutzer registriert sind.", "manageLinkedCases.title": "Verlinkte Fälle", "manageLinkedCases.visibilityChange.tooltip.unmanaged": " <PERSON>cht verwaltet ", "markeplace.filter.switch1": "Finanzierungsvolumen", "markeplace.filter.switch2": "Laufzeit", "markeplace.filter.switch5": "Zinsertrag", "markeplace.filter.switch7": "Finanzierungsvolumen", "marketplace.advanced.filters2": "Investitionsstandort", "marketplace.businessCase.card.button": "Zum Fall", "marketplace.businessCase.card1": "Finanzierungsvolumen", "marketplace.businessCase.hover": "Weitere Firmeninformationen", "marketplace.filter.switch3": "Umkreissuche", "marketplace.filter.switch4": "Zinsertrag", "marketplace.filter.switch6": "Laufzeit", "marketplace.filters.1": "<PERSON><PERSON><PERSON>", "marketplace.filters.2": "Ergebnisse", "marketplace.filters.4": "Entfernung", "marketplace.filters.advanced": "<PERSON><PERSON><PERSON>", "marketplace.filters.advanced.filters1": "Suchfilter", "marketplace.filters.sortFields.createdOn": "Erstellt am", "marketplace.overview": "Ergebnisse", "marketplace.overview.businessCase.breakdown1": "<PERSON><PERSON><PERSON><PERSON>", "marketplace.overview.businessCase.breakdown2": "Partner", "marketplace.overview.businessCase.breakdown3": " noch offen ", "marketplace.overview.businessCase.breakdown4": " bereits platziert ", "marketplace.statistics.completedCases": " Abgeschlossene Fälle ", "marketplace.statistics.ongoingCases": "Laufende Fälle", "marketplace.statistics.totalParticipation": " Gesamtbeteiligung ", "messageNotRed": "<PERSON><PERSON><PERSON><PERSON>", "mfaAuthentication.timeOutMessage": "Sie haben den Sicherheitscode noch nicht erhalten?", "mfaAuthentication.timeOutMessage2": "Sicherheitscode senden.", "mfaAuthentication.timerMessage": "Sie haben keinen Sicherheitscode erhalten? Versuchen Sie es erneut in {$START_TAG_STRONG}{$INTERPOLATION}{$CLOSE_TAG_STRONG}.", "months": " Monate", "multiFactorAuth.toast.error": "Multifaktor-Authentifizierung konnte nicht aktiviert werden.", "multiFactorAuth.toast.error.inactive": "Multifaktor-Authentifizierung konnte nicht deaktiviert werden.", "multiFactorAuth.toast.success": "Multifaktor-Authentifizierung aktiv", "multiFactorAuth.toast.success.inactive": "Multifaktor-Authentifizierung inaktiv", "multiselectDropdown.filter.placeholder": "Filter beim <PERSON>", "multiselectDropdown.switchLabel": "Bewerberfilter", "navigationPanel.collapse.btn.tooltip": "Ausblenden", "navigationPanel.expand.btn.tooltip": "<PERSON><PERSON><PERSON><PERSON>", "nda.label": "Vertraulichkeitsvereinbarung", "neoGpt.onboarding.header.financing.step1": "Willkommen bei neoshare AI", "neoGpt.onboarding.header.step1": "Willkommen bei neoshare AI!", "neoGpt.onboarding.header.step2": "Verstehen Si<PERSON> j<PERSON>", "neoGpt.onboarding.header.step3": "Antworten auf alle Ihre Fragen", "neoGpt.onboarding.step1": "Unser innovativer KI-Assistent steht bereit, um Ihnen einen nahtlosen Zugang auf alle benötigten Informationen zu ermöglichen - schnell und unkompliziert.", "neoGpt.onboarding.step2": "neoshare AI vereinfacht das Durchsuchen langer Dokumente und ermöglicht Ihnen, in Sekunden wichtige Informationen zu erfassen.", "neoGpt.onboarding.step3": "neoshare AI liefert relevante und präzise Antworten, die Sie bei der erfolgreichen Verwaltung Ihrer Dokumentation unterstützen.", "neoGpt.thankYou.forRating.msg": " Vielen Dank für Ihre Antwort! Wir werden versuchen, unseren Service auf der Grundlage Ihres Feedbacks zu verbessern! ", "neoGptChat.negative.feedback.placeholder": "Bitte teilen Si<PERSON> uns Ihre Erfahrungen oder Gedanken mit.", "neogpt.button.label": "neoshare AI fragen", "neogpt.chat.errorMessage": "Es scheint Komplikationen bei der Beantwortung der Frage zu geben. Wir entschuldigen uns für die Unannehmlichkeiten. Bitte versuchen Sie es später erneut.", "neogpt.chat.input.placeholder": "neoshare AI anschreiben", "neogpt.chat.message.check": "<PERSON><PERSON>", "neogpt.chat.message.checkMultiple": "<PERSON><PERSON>", "neogpt.chat.message.document.unavailable": " Dokument nicht verfügbar ", "neogpt.chat.message.document.unavailable ": "Dokument nicht verfügbar", "neogpt.chat.message.documentLimitedReady": "Verarbeitung der Bilder. Dies kann eine Weile dauern.", "neogpt.chat.message.documentPendingMsg": "Das Dokument wird gerade bearbeitet, bitte warten <PERSON>.", "neogpt.chat.message.documentReadyMsg": "Dokument erfolgreich bearbeitet.", "neogpt.chat.message.documents.label": "Dokumente:", "neogpt.chat.message.field.unavailable": "<PERSON>ld nicht verfügbar", "neogpt.chat.message.fields.label": "Felder:", "neogpt.chat.message.fields.updated": "<PERSON><PERSON> a<PERSON>", "neogpt.chat.message.initial": "Wie kann ich Ihnen heute helfen?", "neogpt.chat.message.noDataAnswer": "Wir konnten keine passende Antwort finden. <PERSON><PERSON> können versuchen, Ihre Frage neu zu formulieren und die Schlüsselwörter zu überprüfen. Wenn Sie weiterhin Unterstützung benötigen, stellen Si<PERSON> gerne eine andere Frage, und wir stehen Ihnen zur Verfügung.", "neogpt.chat.message.page": "Seite", "neogpt.chat.message.sources.showLess": "<PERSON><PERSON> anzeigen", "neogpt.chat.message.sources.showMore": "<PERSON><PERSON> anzeigen", "neogpt.chat.message.summary": "<PERSON><PERSON><PERSON>nfassung", "neogpt.chat.message.switchContext": "Wir konnten in diesem Dokument keine Antwort auf Ihre Anfrage finden.", "neogpt.chat.message.switchContextToDataRoom": "Wir konnten in diesem Dokument keine Antwort auf Ihre Anfrage finden. Sollen wir die Suche erweitern?", "neogpt.chat.pending.message": "Das Dokument wird verarbeitet, bitte warten.", "neogpt.chat.selectQuestion": "Wählen Sie eine Beispielfrage aus der Liste oder formulieren Sie Ihre eigene Frage.", "neogpt.feature.autofill": "Autofill-Felder", "neogpt.feature.chat": "<PERSON><PERSON>-<PERSON><PERSON><PERSON>", "neoshare.AI.disable.chat.history": "Wenn Sie die Funktion deaktivieren, werden alle vorherigen Nachrichten gelöscht", "neoshare.AI.onboarding.financing.step1": "Unser innovativer KI-Assistent bietet Ihnen nahtlosen Zugang zu spezifischen Informationen, die Sie über Ihre Finanzierungsstruktur benötigen. <PERSON><PERSON> beachten Si<PERSON>, dass der Assistent ausschließlich für Ihre Finanzierungsstruktur eingesetzt wird.", "neoshare.AI.onboarding.header.step2": "Fragen Sie nach Dokumenten oder Feldern", "neoshare.AI.onboarding.settings": "Passen Sie Ihre Einstellungen an", "neoshare.AI.onboarding.step1": "Unser Assistent bietet Ihnen schnellen Zugriff auf wichtige Informationen. Hinweis: neoshare AI kann Fehler machen. Bitte prüfen Sie wichtige Antworten sorgfältig.", "neoshare.AI.onboarding.step2": "Mit dem KI Assistenten können Sie alle Felder und unterstützten Dokumente innerhalb des Datenraums durchsuchen. Aktuell werden die folgenden Formate unterstützt: PDF, TXT, DOCX, JPG/JPEG und viele mehr.", "neoshare.AI.onboarding.step3": "Verwalten Sie Ihre Chat-Historie und sehen Sie unterstützte Formate ein.", "neoshare.AI.onboarding.text.step2": "neoshare AI durchsucht alle Felder und Dokumente in Ihrem Datenraum und Ihrer Finanzierungsstruktur.", "neoshareAI.currently.supported.files.message": " Die derzeit unterstützten Dateiformate sind:", "neoshareAI.deleteHistoryDialog.text": "Dauerhaftes Löschen des Chatverlaufs:<br />Diese Aktion kann nicht rückgängig gemacht werden. Sind Si<PERSON> sicher?", "neoshareAI.limited.capabilities.message": "Der Assistent liefert gelegentlich ungenaue Ergebnisse für Dokumente oder Felder.", "neoshareAI.settings.deleteChatHistory": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "neoshareAI.settings.saveChatHistory": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "neoshareAI.supportedTypes.showLess": "<PERSON><PERSON> anzeigen", "neoshareAI.supportedTypes.showMore": "<PERSON><PERSON> anzeigen", "network.button.label.Ubo": "<PERSON><PERSON><PERSON>", "network.button.label.network": "Netzwerk", "networkGraph.noDataMessage": "<PERSON><PERSON> Netzwerkinformation vorhanden.", "newDocumentUpload": " Neues Dokument hier ablegen\r\n", "nextFolderSynchronization.modal.buttonLabel": "Data room aufrufen", "nextFolderSynchronization.modal.select.placeholder": "<PERSON><PERSON> ausgewählt", "nextFolderSynchronization.modal.subTitle": "<PERSON><PERSON> können den Status von jedem Dokument im Data room verfolgen", "nextFolderSynchronization.modal.title": "Synchronisierung wird durchgeführt", "nextfolder.message.error": "Nicht synchronisiert", "nextfolder.message.pending": "Synchronisierung ausstehend", "nextfolder.message.success": "Synchronisiert", "nextfolderIntegration.caseSync.integration.description": "Sie haben erfolgreich Ihren Finanzierungsfall mit NextFolder synchronisiert!", "nextfolderIntegration.caseSync.success.button": "Zum Finanzierungsfall", "nextfolderIntegration.description": "Sie haben erfolgreich NextFolder mit Ihrem neoshare Account integriert!", "nextfolderIntegration.success.button": "<PERSON><PERSON>", "nextfolderIntegration.toast.credentials.error": "Diese <PERSON>aten werden bereits von einer anderen Organisation verwendet.", "nextfolderIntegration.toast.error": "Verbindung nicht hergestellt. Versuchen Sie es noch einmal oder starten Sie den Vorgang später", "nextfolderIntegrationForm.backButton.label": "<PERSON><PERSON>", "nextfolderIntegrationForm.businessCase.label": "Vorgangsname auswählen (optional)", "nextfolderIntegrationForm.description": "<PERSON>d <PERSON> sic<PERSON>, dass Sie die Integration von NextFolder mit Ihrem neoshare Account abbrechen möchten?", "nextfolderIntegrationForm.emailAddress": "E-Mail-Adresse", "nextfolderIntegrationForm.header": " Verbinden Sie NextFolder mit neoshare ", "nextfolderIntegrationForm.infoText": "Indem Sie auf “Verbinden” <PERSON>, er<PERSON><PERSON>, NextFolder als Cloud-Provider für alle Ihre hochgeladenen Dokumente zu verwenden. ", "nextfolderIntegrationForm.nextfolder.label": "NextFolder Vorlage", "nextfolderIntegrationForm.password": "Passwort", "nextfolderIntegrationGuide.description": " Unterstützte Dateiformate: PNG, JPG, PDF bis 100MB. Für einen idealen Workflow verarbeiten Sie PDF-Daten im DIN A4 Format. ", "nextfolderIntegrationGuide.header": " <PERSON><PERSON><PERSON><PERSON> Dinge, die man vorher wissen sollte:\r\n", "nextfolderIntegrationGuide.message": "Um eine erfolgreiche Verbindung zwischen Ihrem Datenraum und NextFolder herzustellen, müssen Si<PERSON> nach der Verknüpfung ca. 15 Minuten warten.", "nextfolderIntegrationGuide.title": " <PERSON><PERSON> ", "nextfolderMessage": "Nextfolder hilft Ihnen als browserbasierte Anwendung dabei, Eingangsdokumente schneller und papierlos vom Eingang bis ins Archivsystem zu verarbeiten.", "nextfolderSynchronizationForm.button.synchronization": "Synchroni<PERSON><PERSON>", "nextfolderSynchronizationForm.card.button": "Zum Fall", "nextfolderSynchronizationForm.description": "Sind <PERSON> sicher, dass Sie die Synchronisation zwischen Ihrem Finanzierungsfall und NextFolder trennen wollen?", "nextfolderSynchronizationForm.footer.description": "Indem Sie auf “Synchronisieren” klicken, ermöglichen Sie die Verbindung zwischen Ihrem Finanzierungsfall und einem Vorgang bei NextFolder. ", "nextfolderSynchronizationForm.title": "Synchronisieren Sie Ihren Finanzierungsfall mit NextFolder", "ngb.alert.close": "Close", "ngb.carousel.next": "Next", "ngb.carousel.previous": "Previous", "ngb.carousel.slide-number": " Slide {$INTERPOLATION} of {$INTERPOLATION_1} ", "ngb.datepicker.next-month": "Next month", "ngb.datepicker.previous-month": "Previous month", "ngb.datepicker.select-month": "Select month", "ngb.datepicker.select-year": "Select year", "ngb.pagination.first": "««", "ngb.pagination.first-aria": "First", "ngb.pagination.last": "»»", "ngb.pagination.last-aria": "Last", "ngb.pagination.next": "»", "ngb.pagination.next-aria": "Next", "ngb.pagination.previous": "«", "ngb.pagination.previous-aria": "Previous", "ngb.progressbar.value": "{$INTERPOLATION}", "ngb.timepicker.AM": "{$INTERPOLATION}", "ngb.timepicker.HH": "HH", "ngb.timepicker.MM": "MM", "ngb.timepicker.PM": "{$INTERPOLATION}", "ngb.timepicker.SS": "SS", "ngb.timepicker.decrement-hours": "Decrement hours", "ngb.timepicker.decrement-minutes": "Decrement minutes", "ngb.timepicker.decrement-seconds": "Decrement seconds", "ngb.timepicker.hours": "Hours", "ngb.timepicker.increment-hours": "Increment hours", "ngb.timepicker.increment-minutes": "Increment minutes", "ngb.timepicker.increment-seconds": "Increment seconds", "ngb.timepicker.minutes": "Minutes", "ngb.timepicker.seconds": "Seconds", "ngb.toast.close-aria": "Close", "no.added.signers.error": "<PERSON>e müssen mindestens einen Unterzeichner pro Organisation hinzufügen, bevor Sie die Einladung abschicken", "no.existing.email.error": "Es existiert kein Nutzer mit dieser E-Mail-Adresse.", "no.invited.organization.message": "Sie haben keine Organisation eingeladen", "noMatched.results.message": "Es gibt keine Ergebnisse die Ihren Suchkriterien entsprechen", "noUserMessage": "Sie haben keine spezifischen Nutzer dieser Organisation angegeben.", "noUserMessageOld": "<PERSON>e haben keine Organisationsnutzer", "none.label": "<PERSON><PERSON>", "notificationSystem.header.actions.markAllAsRead": "Alle als gelesen markieren", "notificationSystem.header.actions.settings": "Einstellungen", "notificationSystem.header.filters.all": "Alle", "notificationSystem.header.filters.unread": "<PERSON><PERSON><PERSON><PERSON>", "notificationSystem.header.title": "Benachrichtigungen", "notificationSystem.notification.actions.hide": "Ausblenden", "notificationSystem.notification.actions.markAsRead": "Als gelesen markieren", "notificationSystem.notification.actions.markAsUnread": "<PERSON>s ungelesen markieren", "notificationSystem.notification.chatRelated.archivedAutomatically": "Im Finanzierungsfall ${this.boldBusinessCaseName} wurde ein Chat automatisch archiviert.", "notificationSystem.notification.chatRelated.archivedManually": "{$PH} hat einen Chat im Finanzierungsfall {$PH_1} archiviert.", "notificationSystem.notification.chatRelated.createdAutomatically": "Im Finanzierungsfall {$PH} wurde ein Chat automatisch erstellt.", "notificationSystem.notification.chatRelated.messageSent": "{$PH} hat im Finanzierungsfall {$PH_1} eine neue Nachricht gesendet.", "notificationSystem.notification.chatRelated.reactivatedAutomatically": "Im Finanzierungsfall {$PH} wurde ein Chat automatisch reaktiviert.", "notificationSystem.notification.chatRelated.reactivatedByUser": "{$PH} hat einen Chat im Finanzierungsfall {$PH_1} reaktiviert.", "notificationSystem.notification.chatRelated.userTagged": "{$PH} hat Sie im Chat im Finanzierungsfall {$PH_1} getaggt.", "notificationSystem.notification.elapsedTime.days": " {$ICU} ", "notificationSystem.notification.elapsedTime.hours": " {$ICU} ", "notificationSystem.notification.elapsedTime.minutes": " {$ICU} ", "notificationSystem.notification.elapsedTime.weeks": " {$ICU} ", "notificationSystem.notification.manualTodo.todoCanceled": "Eine Aufgabe für {$PH} wurde von {$PH_1} storniert.", "notificationSystem.notification.manualTodo.todoReassigned": "Eine Aufgabe für {$PH} wurde von  {$PH_1}. <PERSON>ür diese Aufgabe ist keine Aktion Ihrerseits mehr erforderlich.", "notificationSystem.notification.noPermissionsToEnterCase": "Vorgang kann wegen fehlender Daten oder Berechtigungen nicht fortgesetzt werden.", "notificationSystem.notificationList.allTab.empty": " Derzeit gibt es keine neuen Benachrichtigungen. ", "notificationSystem.notificationList.unreadTab.empty": " Derzeit gibt es keine ungelesenen Benachrichtigungen. ", "notificationSystem.notificationList.upToDate": " Sie sind auf dem neuesten Stand. ", "notificationSystem.onboardingTip.image.customizations": "./assets/images/notification-system/tips-notification-system-customizations.svg", "notificationSystem.onboardingTip.text.customizations": "<PERSON>e entscheiden was für Si<PERSON> wichtig ist und können Ihre Benachrichtigungseinstellungen jederzeit anpassen.", "notificationSystem.onboardingTip.text.intro": "Prüfen Sie alle Aktivitäten seit Ihrem letzten Besuch über unser Benachrichtigungssystem.", "notificationSystem.onboardingTip.title.customizations": "Verwalten Sie Ihre Benachrichtigungen", "notificationSystem.onboardingTip.title.intro": "Verpassen Sie keine Informationen", "notificationSystem.settings.chat": "Cha<PERSON>", "notificationSystem.settings.title": "Einstellungen", "notificationSystem.settings.todos": "To-do-Liste", "number.abbreviation.bil": "Mrd.", "number.abbreviation.mil": "<PERSON><PERSON>.", "number.abbreviation.tsd": "Tsd.", "officers.heading1": "Weisungsempfänger", "organizationField.prefix": "Organization hinzufügen", "otherBusinessCases.noDataAvailable.message": "Derzeit gibt es keine Finanzierungsfälle zu diesem Unternehmen, auf die Si<PERSON> Zugriff haben.", "ownCompany.dataRoom.addGroup": " Gruppe hinzufügen ", "ownCompany.dataRoom.button.openLInkedCases": "Verlinkte Fälle", "ownCompany.dataRoom.empty": " Es wurden noch keine Felder zum unternehmensbezogenen Data Room hinzugefügt. Um welche anzulegen, aktivieren Sie bitte den Bearbeitungsmodus und fügen Sie Felder hinzu oder erstellen sie eine Vorlage für den unternehmensbezogenen Data Room unter “Vorlagen”. ", "ownCompany.dataRoom.emptyGroup.text": " Fügen Sie Felder durch Drag & Drop hinzu ", "ownCompany.dataRoom.emptyGroup.textSub": " <PERSON><PERSON><PERSON> dafür die Felder aus der rechten Spalte in diesen Bereich. ", "ownCompany.dataRoom.prefix": " Information zu", "ownCompany.dataRoom.suffix": "hinzufügen", "paginator.compactRangeLabel": "{$PH} von {$PH_1} Dateneinträge", "paginator.goToPageLabel": "Gehe zur Seite:", "paginator.rangeLabel": "Zeigt {$PH} - {$PH_1} von {$PH_2} Einträgen", "paginator.showLabel": "Zeigen:", "participation-page.errors.dfs-value-exceeding-total-expected-amount": "<PERSON>te beachten Sie, dass die Summe der Finanzierungsbausteine über den beantragten Finanzierungsbetrag liegt.", "participation-page.labels.amount-distribution": "Betragsverteilung", "participation-page.labels.building-blocks-distribution-text": "Nachfolgend sehen Sie die Verteilung Ihres Betrags auf die Finanzierungsbausteine enthalten in der Finanzierungsstruktur.", "participation-page.labels.my-financing-structure": "Eigene Finanzierungsstruktur", "participation-page.labels.own-investment-amount": "<PERSON>igene Beteil<PERSON>", "participation-page.labels.shared-financing-structures": "Geteilte Strukturen", "participation.warning.message.notCoveredCriteria": "nicht erfüllt", "participation.warning.message.participant.whoAre": "<PERSON><PERSON><PERSON><PERSON><PERSON>:", "participation.warning.message.youHave": "<PERSON><PERSON> haben", "participationAmount.toast.error": "Fehler beim aktualiseren des Teilnehmeranforderungen", "participationAmount.toast.success": "Teilnehmeranforderungen erfolgreich aktualisiert", "passwordPolice.toast.error": "Passwortrichtlinien konnten nicht gespeichert werden.", "passwordPolice.toast.success": "Passwortrichtlinien erfolgreich gespeichert.", "pastInvoice.dateInfo": "<PERSON> / <PERSON><PERSON>", "pastInvoice.placeholder": "Rechnung suchen...", "pdfExport.toast.info": "PDF-Export gestartet. Ihr Download sollte in wenigen Sekunden verfügbar sein.", "perceptions.financingApplicationSubmitted": "Finanzierungsanfrage wurde gestellt", "perceptions.financingApplicationWithdrawn": "Finanzierungsantrag wurde zurückgezogen", "perceptions.financingApproved": "Finanzierung bewilligt", "perceptions.financingCaseCreated": "Fall angelegt", "perceptions.financingCaseJoined": "Fall beigetrenen", "perceptions.financingDecisionPending": "Finanzierungsbeschluss ausstehend", "perceptions.financingRejected": "Finanzierung wurde abgelehnt", "perceptions.financingRepaid": "Finanzierung wurde zurückgezahlt", "perceptions.financingRepaidActive": "Finanzierung wurde zurückbezahlt", "perceptions.financingRepaidInactive": "Finanzierung wurde zurückgezahlt", "perceptions.financingSuccessfullyCompleted": "Finanzierung erfolgreich durchgeführt", "perceptions.fullEvaluationCarriedOut": "Vollvalutierung erfolgt", "perceptions.incorrectEntry": "Fehlerhafte Еrfassung", "perceptions.objectSold": "Objekt wurde verkauft", "perceptions.other": "Sonstiges", "perceptions.structuringOrderIssued": "Strukturierungsauftrag erteilt", "perceptions.termSheetAccepted": "Termsheet wurde akzeptiert", "portalFields.actions.switch.label": "Sichbar", "privateBusinessCase.toast.error": "<PERSON>e haben keinen Zugriff, da der Finanzierungsfall nicht mehr aktiv ist. Eine Teilnahme ist nicht möglich.", "privateBusinessCase.toast.failedToLoad": "Die von Ihnen angegebene URL ist nicht korrekt.", "privateBusinessCase.toast.genericError": "Vorgang kann wegen fehlender Daten oder Berechtigungen nicht fortgesetzt werden.", "privateBusinessCase.toast.ndaNotSignedByAllParties": "Alle Unterzeichner aus Ihrer Organisation müssen die NDA unterschreiben, damit <PERSON><PERSON> auf den Fall zugreifen können.", "realEstate.dashboard.exportExcel.finance.kpi.filename.suffix": "Finanzdaten - KPI", "realEstate.dashboard.exportExcel.portfolio.general.filename.suffix": "Portfolio - Allgemein", "realEstate.dashboard.exportExcel.portfolio.kpi.filename.suffix": "Portfolio - KPI", "refs.accordion.infoBadge.tooltip": "<PERSON><PERSON> synchronisiert", "refs.addFinancingBlockModal.alreadyUsedPurposeOfUse": "Verwendungszweck wird bereits verwendet", "refs.addFinancingBlockModal.title": "<PERSON>ustein hinzufü<PERSON>", "refs.financingBlockModal.financingBuildingBlock": "Finanzierungsbaustein", "refs.financingBlockModal.reason": "Geben Sie den Grund für die Erstellung des Finanzierungsbausteins an", "refs.financingBlockModal.selectFromList": "Bitte wählen Sie einen Finanzierungsbaustein aus der Liste aus", "refs.stats.requestedFundingAmount": "Beantragter Finanzierungsbetrag", "refs.stats.sumOfFinancingBuildingBlocks": "Summe Finanzierungsbausteine", "refs.stats.tooltip.buildingBlocks": "Finanzierungsbausteine", "resend.label": "<PERSON><PERSON><PERSON> e<PERSON>n", "restrictedAccess.text": "Folgende Personen aus Ihrer Organisation nehmen am Fall teil:", "restrictedAccess.title": "Eingeschränkter Zugang zum Fall {$INTERPOLATION}.", "revisions.hours.oClock": "<PERSON><PERSON>", "search": "<PERSON><PERSON>", "search.autocomplete.noResults": "<PERSON>ine Ergebnisse gefunden. Versuchen Sie andere Schlüsselwörter oder überprüfen Sie die Rechtschreibung", "search.financingStructure.typeahead.hint": "Geben Sie 3 Buchstaben, 1 Ziffer oder 1 Sonderzeichen ein, um die Suche zu starten.", "search.resultCount": "{$INTERPOLATION} Ergebnisse", "search.resultMessage": "<PERSON><PERSON><PERSON><PERSON>", "search.resultsMessage": "Ergebnisse", "searchFilter.placeholder": "Suche...", "sectionApplication.exceedFinancialVolumeText": "Der eingegebene Betrag überschreitet die beantragte Finanzierungssumme", "sectionApplication.header.label.financingCase": " Finanzierungstruktur ", "sectionApplication.header.label.financingCase.participationData": " Beteiligungshöhe bearbeiten ", "sectionApplication.header.label.passingCase": "Bewerbung", "sectionApplication.header.label.total": " Gesamtbeteiligung ", "sectionApplication.outOfRangeContribution": " Der Betrag liegt über die Höchstbeteiligungssumme. ", "sectionApplication.rangeErrorText": " Der Betrag liegt unter der Mindestbeteiligungssumme. ", "sectionApplication.submitApplication": "Bewerbung abschicken", "sectionApplication.submitted.text": "Ihre Bewerbung wurde erfolgreich abgeschickt und wird nun vom Fallinhaber geprüft.", "sectionInvitation.label": "Einladung", "select.initialPlaceHolder": "<PERSON><PERSON> ausgewähl<PERSON>", "select.noSelected": "<PERSON>inen ausgewählt", "select.notFoundText": "<PERSON><PERSON> gefunden", "select.notfoundText": "<PERSON><PERSON> gefunden", "select.status.label": "Status wählen", "select.typeToSearchText": "Text eingeben zum Suchen", "selectExternal.title": "<PERSON><PERSON><PERSON> beenden", "selectPartnerUsers.partnerUsersFieldRequest.infoMessage": " <PERSON><PERSON><PERSON> auf Anfordern klicken, werden die ausgewählten Nutzer dieses Teilnehmers benachrichtigt. ", "selectPeriod.customRange": "Individueller Zeitraum", "selectPeriod.last30Days": "Letzte 30 Tage", "selectPeriod.last3Months": "Letzte 3 Monate", "selectPeriod.last7Days": "Letzte 7 Tage", "selectPeriod.sinceYesterday": "Seit gestern", "selectUsers.contentHeader.fileName": " Name ", "selectUsers.contentHeader.landlineNumber": " Telefon ", "selectUsers.contentHeader.mobile": " Mobil ", "selectUsers.contentHeader.name": " E-Mail ", "serModel.title.Ing.": "Ing.", "shareCadr.modal.CustomerExplicitShare.title": " <PERSON><PERSON><PERSON> get<PERSON>t ", "shareCard.modal.title": "Data Room teilen", "shareCardModal.button.label": "Teilen", "shareCardModal.errorMessage": "Der unternehmensbezogene Data Room wurde bereits mit dieser Organisation geteilt.", "shareCardModal.title.sharedVia": " Geteilt via Finanzierungsfall ", "shareCardNodal.title": " Organisationen auswählen ", "sharedCompany.data.rooms.button.label.removed": "Entfernen", "sharedCompany.data.rooms.placeholder": "Einen mit mir geteilten Data Room auswählen", "sharedCompany.data.rooms.text": " Es wurde noch kein Data Room mit Ihnen geteilt. Sobald eine andere Organisation ihren Data Room zu diesem Unternehmen mit Ihnen teilt, wird dieser hier angezeigt. ", "sharedCompany.dataRooms.direct": " Direkt", "sharedCompany.dataRooms.viaCase": " via Fall", "sharingModal.information": "Steuern Sie die Sichtbarkeit, fordern Sie Informationen an und verfolgen Sie Statusaktualisierungen für ausgewählte Bereiche innerhalb Ihrer Finanzierungsstruktur.", "sideNav.button.add": "Neue Gruppe hinzufügen", "sidebar.caseRelated": "Fallbezogen", "sidebar.companyRelated": " Unternehmensbezogen ", "sidebar.noTemplates": "Noch keine Vorlagen vorhanden.", "sidenav.companiesPage": "<PERSON><PERSON><PERSON>", "sidenav.companiesPage2": "Firmenkunden", "sidenav.items.accountManagement.customers": "<PERSON><PERSON>", "sidenav.items.accountManagement.usageContracts": " Nutzungsverträge ", "sidenav.items.accountManagement.users": "<PERSON><PERSON><PERSON>", "sidenav.items.apps": "Apps", "sidenav.items.businessCaseTemplateManagement": "Vorlagen", "sidenav.items.cases": "<PERSON><PERSON><PERSON>", "sidenav.items.customerMasterData": "Stammdaten", "sidenav.items.customerOrganisationData": "Organisationsdaten", "sidenav.items.dashboard": "Dashboard", "sidenav.items.demos": "Snapshot", "sidenav.items.documents": "Verträge", "sidenav.items.receipt": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sidenav.items.signature": "Signaturübersicht", "sidenav.items.userManagement": "Nutzerverwaltung", "singleFileUpload.button.choose": "<PERSON><PERSON> ho<PERSON>n", "singleFileUpload.button.delete": "<PERSON><PERSON>", "singleFileUpload.button.upload": "Drag-and-Drop", "singleFileUpload.heading": "<PERSON><PERSON>", "singleSelectInput.placeholder": "Organisationen auswählen", "snapshot.actions.copy": "<PERSON><PERSON><PERSON>", "snapshot.actions.copy.modalError": "<PERSON>ählen Sie eine Option, um fortzufahren", "snapshot.actions.copy.modalHeading": "<PERSON><PERSON>hlen Sie einen Snapshot-Status zum Kopieren aus", "snapshot.actions.copy.modalOptions.modifiedSnapshot.description": "Enth<PERSON>lt alle Informationen, die der Umgebung seit der Erstellung des Snapshots hinzugefügt wurden.", "snapshot.actions.copy.modalOptions.modifiedSnapshot.title": "Modifizierter Snapshot", "snapshot.actions.copy.modalOptions.unmodifiedSnapshot.description": "Enthält alle Informationen zum Zeitpunkt der Erstellung des Snapshots.", "snapshot.actions.copy.modalOptions.unmodifiedSnapshot.title": "Ursprünglicher Snapshot", "snapshot.actions.delete": "Löschen", "snapshot.actions.delete.modalHeading": "Das permanente Löschen des Snapshots \"{$INTERPOLATION}\" kann aktive Kunden-Demos unterbrechen. Sind Si<PERSON> sicher?", "snapshot.actions.deploy": "Bereitstellen", "snapshot.actions.deploy.errorMessage": "Fehler beim Bereitstellen des Snapshots", "snapshot.actions.deploy.modalHeading": "Wenn ein Snapshot in der Umgebung bereitgestellt wird, können sich die Nutzer der Kunden anmelden und die Plattform nutzen.", "snapshot.actions.deploy.modalText": "Dies kann einige Zeit in Anspruch nehmen. Bitte warten Sie.", "snapshot.actions.deploy.successMessage": "Snapshot erfolgreich bereitgestellt.", "snapshot.actions.deploy.tooltip": "Die Bereitstellung ermöglicht Kundenanmeldungen.", "snapshot.actions.recall": "Abrufen", "snapshot.actions.recall.confirmButton": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "snapshot.actions.recall.errorMessage": "Fehler beim Zurücknehmen des Snapshots. \r\nBitte versuchen Sie es erneut.", "snapshot.actions.recall.modalHeading": "Das Zurücknehmen des Snapshots kann aktive Kundendemos unterbrechen.", "snapshot.actions.recall.modalText": "Bitte beachten Sie, dass alle Informationen, die der Umgebung seit der Erstellung des Snapshots hinzugefügt wurden, verloren gehen. Möchten Si<PERSON> fortfahren? \r\nDies kann einige Zeit in Anspruch nehmen. Bitte warten Sie.", "snapshot.actions.recall.successMessage": "Snapshot wird zurückgenommen", "snapshot.actions.recall.tooltip": "Entfernt den Snapshot aus der Umgebung und setzt ihn  in seinen ursprünglichen Zustand zurück.", "snapshot.actions.reset": "Z<PERSON>ücksetzen", "snapshot.actions.reset.confirmButton": "Zurückrufen", "snapshot.actions.reset.errorMessage": "Fehler beim Zurücksetzen des Snapshots.  \r\nBitte versuchen Sie es erneut.", "snapshot.actions.reset.modalHeading": "Das Zurücksetzen des Snapshots kann aktive Kundendemos unterbrechen.", "snapshot.actions.reset.modalText": "Bitte beachten Sie, dass alle Informationen, die der Umgebung seit der Erstellung des Snapshots hinzugefügt wurden, verloren gehen. Möchten Si<PERSON> fortfahren? \r\nDies kann einige Zeit in Anspruch nehmen. Bitte warten Sie.", "snapshot.actions.reset.successMessage": "Snapshot wird zurückgesetzt", "snapshot.actions.reset.tooltip": "Das Zurücksetzen stellt den Snapshot in seinen ursprünglichen Zustand zurück und macht ihn sofort einsatzbereit.", "snapshot.actions.view": "<PERSON><PERSON><PERSON>", "snapshot.copy.title": "Snapshot kopieren", "snapshot.create.autoDeletionDateInfoMessage": "<PERSON><PERSON> kann keine automatische Löschung festgelegt werden.", "snapshot.create.changeCompanyLabel": "Unternehmen ändern", "snapshot.create.companiesAndCases": "Unternehmen und Geschäftsfälle", "snapshot.create.companyInformation.businessCases": "Geschäftsfälle", "snapshot.create.customerInCollaborationWith": "In Kooperation mit:", "snapshot.create.customerUsers": "Benutzer des Kunden", "snapshot.create.deployErrorToast": "Fehler beim Bereitstellen des Snapshots", "snapshot.create.emptyCompanies": "Noch keine Unternehmen und Fälle hinzugefügt", "snapshot.create.emptyUsers": "Noch keine Nutzer auf Kundenseite hinzugefügt", "snapshot.create.errorToast": "Fehler bei der Snapshot-Erstellung. Bitte versuchen Sie es erneut.", "snapshot.create.noSelectedCustomersError": "Sie müssen mindestens einen Kunden auswählen, um fortzufahren.", "snapshot.create.selectCustomersLimitMessage": "<PERSON><PERSON><PERSON> ausgewählte Kunden (max. {$INTERPOLATION}):", "snapshot.create.stepper.titles.step1": "Allgemeine Snapshot-Informationen", "snapshot.create.stepper.titles.step2": "Kunden auswählen", "snapshot.create.stepper.titles.step3": "Detaillierte Kundeninformationen", "snapshot.create.stepper.titles.step4": "Zusammenfassung des Schnappschusses", "snapshot.create.successToast": "Erstellter Snapshot wird bereitgestellt", "snapshot.deleteModal.errorToast": "Fehler beim Löschen des Snapshots.\r\nBitte versuchen Sie es erneut.", "snapshot.deleteModal.successToast": "Snapshot erfolg<PERSON><PERSON>", "snapshot.details.headingBackButton": "Snapshots", "snapshot.details.labels.autoDeletionDate": "Wird automatisch gelöscht am", "snapshot.details.labels.cases": "<PERSON><PERSON><PERSON>", "snapshot.details.labels.comment": "Kommentar", "snapshot.details.labels.company": "Unternehmen", "snapshot.details.labels.createdBy": "<PERSON><PERSON><PERSON><PERSON> von", "snapshot.details.labels.customer": "Kunde", "snapshot.details.labels.customerCollaborators": "Organisation in Kooperation mit:", "snapshot.details.labels.dateCreated": "Erstellungsdatum", "snapshot.details.labels.dateDeployed": "Datum des Einsatzes", "snapshot.details.labels.dateRecalled": "Datum des Rückrufs", "snapshot.details.labels.dateUpdated": "Aktualisiert am", "snapshot.details.labels.deployedBy": "Eingesetzt von", "snapshot.details.labels.id": "ID", "snapshot.details.labels.lastUsed": "Zuletzt verwendet", "snapshot.details.labels.linkedSnapshots": "Verlinkte Snapshots", "snapshot.details.labels.name": "Name", "snapshot.details.labels.noCases": "<PERSON><PERSON>", "snapshot.details.labels.noCompanies": "<PERSON><PERSON>", "snapshot.details.labels.noPartnerships": "<PERSON><PERSON>", "snapshot.details.labels.potentialCustomerName": "Name des potentiellen Kunden", "snapshot.details.labels.recalledBy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> von", "snapshot.details.labels.snapshotCopies": "Kopien des aktuellen Snapshots", "snapshot.details.labels.sourceSnapshot": "Quellsnapshot", "snapshot.details.labels.status": "Status", "snapshot.details.labels.type": "<PERSON><PERSON>", "snapshot.details.labels.updatedBy": "<PERSON><PERSON><PERSON><PERSON><PERSON> von", "snapshot.details.labels.version": "Version", "snapshot.details.switchViewState.modified": "Modifizierter", "snapshot.details.switchViewState.modified.tooltip": "Enth<PERSON>lt alle Informationen, die der Umgebung seit der Erstellung des Snapshots hinzugefügt wurden.", "snapshot.details.switchViewState.unmodified": "Ursprünglicher", "snapshot.details.switchViewState.unmodified.tooltip": "Enthält alle Informationen zum Zeitpunkt der Erstellung des Snapshots.", "snapshot.details.titles.companiesAndCases": "Unternehmen und Fälle", "snapshot.details.titles.generalInformation": "Allgemeine Snapshot-Informationen", "snapshot.snapshot.createButton": "Snapshot erstellen", "snapshot.snapshot.demoTypes.customerDemo": "Kundendemo", "snapshot.snapshot.demoTypes.customerTemplate": "Kundenvorlage", "snapshot.snapshot.demoTypes.master": "Master", "snapshot.snapshot.demoTypes.pitchDemo": "Pitch-Demo", "snapshot.snapshot.demoTypes.pitchTemplate": "Pitch-Vorlage", "snapshot.snapshot.noResult.description": "Die erstellten Snapshots werden hier angezeigt", "snapshot.snapshot.noResult.title": "<PERSON><PERSON> erste<PERSON>t", "snapshot.snapshot.notFound.title": "<PERSON><PERSON> gefunden", "snapshot.snapshot.table.action": "Aktion", "snapshot.snapshot.table.autoDeletionDate": "Auto-Löschung", "snapshot.snapshot.table.createdBy": "<PERSON><PERSON><PERSON><PERSON> von", "snapshot.snapshot.table.dateDeployed": "Bereitgestellt am", "snapshot.snapshot.table.name": "Name", "snapshot.snapshot.table.potentialCustomerName": "Potenzieller Kunde", "snapshot.snapshot.table.status": "Status", "snapshot.snapshot.table.type": "<PERSON><PERSON>", "snapshot.status.created": "<PERSON><PERSON><PERSON><PERSON>", "snapshot.status.deployed": "Bereitgestellt", "snapshot.status.deploying": "Wird bereitgestellt", "snapshot.status.failed": "Fehlgeschlagen", "snapshot.status.recalled": "Zurückgenommen", "snapshot.status.recalling": "Wird zurückgenommen", "snapshot.status.resetting": "Wird zurückgesetzt", "sortableList.label.addNew": "Hinzufügen", "sortableList.label.checkbox": "Sicherheit", "spinner.loadingMessage": "Wird geladen", "state.branch": "Zweigniederlassung", "state.branch2": "Zweigstelle", "state.branchOffice": "Niederlassung", "state.filial": "Filiale", "state.headOffice": "Hauptniederlassung", "stateModal.confirmation.text.conclude": "abschließen", "stateModal.confirmation.text.interrupt": "abbrechen", "statistic.tooltip.case": "  <PERSON><PERSON><PERSON>", "successfully.invited.guest-users.details": "Die eingeladenen Benutzer werden benachrichtigt und erhalten Zugang, sobald sie die Einladung akzeptieren", "successfully.invited.guest-users.message": "Einladung erfolgreich versendet", "successfully.invited.organization.message": "Erfolgreich! Sie haben die folgende Organisation eingeladen", "support.canceledInvite.button.label": "Jetzt anfragen", "support.canceledInvite.hint": " Um weitere Hilfe zu erhalten, kö<PERSON>n", "support.canceledInvite.info": " Ihre Einladung wurde storniert. ", "support.canceledInvite.info2": " Bringen Sie jetzt Ihre Organisation auf ", "support.canceledInvite.ticket": "<PERSON>e ein Ticket", "support.canceledInvite.ticket2": "erstellen und unser Support wird sich bei Ihnen melden!", "support.contactSupport.button.label.submit": "<PERSON><PERSON><PERSON> senden", "support.contactSupport.errorMessage.mail": "Ungültige E-Mail-Adresse", "support.contactSupport.errorMessage.phoneNumber": "Telefonformat ist nicht korrekt", "support.contactSupport.fieldsList.label": " E-Mail-Adresse ", "support.contactSupport.heading": " Einrichtung eines Kontos für Ihre Organisation ", "support.contactSupport.label": " Gesprächspartner ", "support.contactSupport.label.phone": " Telefonnummer ", "support.contactSupport.subHeading": " Hinterlassen Sie uns hier Ihre Kontaktdaten, damit unsere Kooperationsmanager auf Sie zukommen können, um weitere Details mit Ihnen zu besprechen. ", "support.contactSupport.textInput.placeholder": "E-Mail-Adresse", "support.contactSupport.textInput.placeholder.firstName": "<PERSON><PERSON><PERSON>", "support.contactSupport.textInput.placeholder.lastName": "Nachname", "support.contactSupport.textInput.placeholder.phone": "+49 89 *********", "table.footer.message": "Zeigt {$INTERPOLATION} - {$INTERPOLATION_1} von {$INTERPOLATION_2} Einträgen", "table.noDataAvailable.message": "<PERSON><PERSON> vorhand<PERSON>.", "tableColumns.invoiceNumber": "Rechnungsnummer", "tableField.modal.table": "<PERSON><PERSON><PERSON>", "tableField.modal.title": "<PERSON><PERSON><PERSON>", "tableField.modal.title2": "<PERSON><PERSON><PERSON>", "targetCompanyField.select.placeholder": "<PERSON><PERSON><PERSON><PERSON> suchen", "teaserConfigModal.content": "Bitte wählen Sie welche Informationen im Export angezeigt werden sollen.", "teaserConfigModal.message": "Wählen Sie die Organisation und die entsprechenden Gruppen für den Export aus. Beachten Sie, dass Gruppen ohne Daten deaktiviert sind und  nicht exportiert werden können.", "teaserConfigModal.noGroups": " Sie haben keine aktiven Abschnitte zum Herunterladen ", "teaserConfigModal.settingName": " Private Informationen ", "teaserConfigModal.settingName.TOC": "Inhaltsverzeichnis", "teaserConfigModal.settingName.dataRoomGroups": " Data Room-Gruppen ", "teaserConfigModal.settingName.generalDetails": "Allgemeine Angaben", "teaserConfigModal.settingName.isRepresentingLeadPartner": " <PERSON><PERSON><PERSON> Fallinteressierte sichtbare Informationen ", "teaserConfigModal.settingName.mainPage": "Deckblatt", "teaserConfigModal.settingName2": " <PERSON><PERSON><PERSON> sichtbare Informationen ", "teaserConfigModal.settingName3": " Aufteilung Finanzierungsvolumen ", "teaserConfigModal.settingName4": " Fallinhaberinformationen ", "teaserConfigModal.settingName5": " Firmeninformation ", "teaserConfigModal.settingName6": " <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ", "teaserConfigModal.title": "PDF-Export", "teaserDownload.toast.info": "Teaserexport gestartet. Ihr Download sollte in wenigen Sekunden verfügbar sein.", "template.model.lastModifiedDate": "Zuletzt aktualisiert", "template.model.status": "Zustand", "template.model.title": "Vorlagentitel", "templateCard.switch.draft": "<PERSON><PERSON><PERSON><PERSON>", "templateCard.switch.published": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "templateEditor.toast.error.cannotEndWith": "Datei- oder Ordnernamen dürfen nicht enden mit: .", "templateEditor.toast.error.fieldNotInFieldList": "Feldschlüssel {$PH} fehlt in der Feldliste, ist aber mit Gruppe {$PH_1} verknüpft", "templateEditor.toast.error.fieldNotLinked": "Feld {$PH} ist mit keiner Gruppe verknüpft. Entfernen Sie das Feld, oder ordnen Si<PERSON> es einer anderen Gruppe zu", "templateEditor.toast.error.fieldsNotDefinedAsDocumentFields": "Ordner in Gruppe {$PH} enthalten Nicht-Dokumentfelder", "templateEditor.toast.error.folderDuplicatedName": "Dokumentfeld aus Gruppe {$PH} wird mehrfach in Ordnern verwendet", "templateEditor.toast.error.folderExceedsMaxDepth": "<PERSON><PERSON> können nicht mehr als 10 Unterordner anlegen", "templateEditor.toast.error.folderExistsInStructure": "Ordner {$PH} mit ID {$PH_1} wird bereits verwendet", "templateEditor.toast.error.folderFileExist": "Der Name wird bereits für einen Ordner verwendet. Bitte wählen Si<PERSON> einen anderen", "templateEditor.toast.error.folderStructureChanged": "Ordnerstruktur wurde beim Verschieben / Löschen des Dokuments oder des Ordners geändert", "templateEditor.toast.error.invalidCharacter": "Unzulässiges Symbol", "templateEditor.toast.error.message.ifNoErrorDisplayed": "Vorlage konnte nicht aktualisert werden. Bitte überprüfen Sie Ihre Vorlage.", "templateEditor.toast.error.message.ifNoErrorDisplayed2": "Vorlage konnte nicht angelegt werden. Bitte überprüfen Sie Ihre Vorlage.", "templateEditor.toast.error.missingDocumentFieldsInFolderStructure": "Dokumentfelder aus Gruppe {$PH} fehlen in den zugehörigen Ordnern", "templateEditor.toast.error.missingFolderStructureFields": "Dokumentfelder fehlen in Gruppe {$PH}", "templateEditor.toast.error.nameMaxLengthReached": " Der Ordnername muss weniger als 100 Zeichen enthalten", "templateEditor.toast.error.newTemplate.prefix": "Vorlage konnte nicht angelegt werden. Folgendes Problem wurde erkannt:", "templateEditor.toast.error.newTemplateExistingName": "Eine Vorlage mit demselben Namen ist bereits vorhanden.", "templateEditor.toast.error.prefix": "Vorlage konnte nicht aktualisert werden. Folgendes Problem wurde erkannt:", "templateEditor.toast.error.wrongOrdinalValue": "Ordnungszahl für Ordner {$PH} fehlt oder hat einen falschen Wert", "templateEditor.toast.success.suffix": "er<PERSON><PERSON><PERSON><PERSON><PERSON> angel<PERSON>.", "templateEditor.toast.success.update.suffix": "erfolgreich aktualisiert.", "templateField.composite": "<PERSON>e können den Wert hier nur solange ändern, bis Sie eine Einladung verschickt haben oder eine Bewerbung erhalten haben.", "templateField.dropdownComponent.warningMessage": "Bitte setzen Sie mindestens 2 Dropdown-Optionen", "templateField.mirrored-field.participant": "Kann nur vom Fallinhaber bearbeitet werden", "templateField.showOnMap": "Au<PERSON> der Karte anzeigen", "termsAndConditions.acceptTerms.button.label": "Akzeptieren", "termsAndConditions.acceptTerms.info": " Bitte stimmen Sie den Nutzungsbedingungen zu. ", "termsAndConditions.acceptTerms.read": "Bitte lesen Sie die ", "termsAndConditions.firstPart": "Allgemeinen Geschäftsbedingungen", "termsAndConditions.secondPart": ", bevor <PERSON> diese akzeptieren", "test": "test", "text.copied": "<PERSON><PERSON><PERSON>", "textArea.editField.enterInfo": "um eine neue Zeile einzufügen", "textArea.editField.enterInfo2": "um eine Zeile einzufügen", "thankYou.title": " Vielen herzlichen Dank für Ihre Unterstützung! ", "themedModal.participantsHint": "Bitte aktivieren Sie die Einstellung „Kann andere Teilnehmer sehen“ unter „Meine Partner“, um Teilnehmer zum Chat hinzuzufügen.", "themedModal.theme": "<PERSON>a", "to.signature.overview.tab.text": "Zum Signaturübersicht", "toast.message.error": "Es ist ein Fehler aufgetreten", "toast.message.reloaded": "Seite wird neu geladen", "toast.message.success": "Aktion erfolgreich ausgeführt", "toast.message.warning": "<PERSON>n fehler<PERSON>", "toastMessage.error.wrongDate": "Datum ist ungültig", "toaster.message.success": "Fall erfolgreich angelegt", "todo.status.archived": "<PERSON><PERSON><PERSON><PERSON>", "todo.status.cancel": "Abgebrochen", "todo.status.completed": "<PERSON><PERSON><PERSON><PERSON>", "todo.status.pending": "<PERSON><PERSON><PERSON><PERSON>", "todoItem.completed.message": "Aufgabe erledigt", "todoList": "To-do-Liste", "todoList.summary.noNewTasks": " <PERSON><PERSON> Aufgaben ", "todoModal.update.button.label": "Aktualisieren", "todos.badge.provideData": "Informationen bereitstellen", "todos.badge.reviewContract": "Vertrag prüfen", "todos.closedBy.system": "System", "todosList.openTodoPage.button": "To-do-Seite öffnen", "todosManagement.cancelToDo.toast.success": "To-do erfolgreich storniert", "todosManagement.cancelToModal.label": "<PERSON><PERSON>cht<PERSON> Sie dieses To-do stornieren?", "todosManagement.closed": "Geschlossen", "todosManagement.completeToDo.toast.success": "To-do erfolg<PERSON>ich erledigt", "todosManagement.completed": "<PERSON><PERSON><PERSON><PERSON>", "todosManagement.createToDoModal.controlLabel.assignee": "<PERSON><PERSON><PERSON><PERSON><PERSON> Person*", "todosManagement.createToDoModal.controlLabel.case": "Fall*", "todosManagement.createToDoModal.controlLabel.description": "Beschreibung*", "todosManagement.createToDoModal.controlPlaceholder.assignee": "Zugeordnete Person auswählen", "todosManagement.createToDoModal.controlPlaceholder.case": "Fall auswählen ID", "todosManagement.createToDoModal.controlPlaceholder.company": "Unternehmen auswählen", "todosManagement.createToDoModal.headerLabel": "To-do erstellen", "todosManagement.createToDoModal.toast.success": "To-do erfolgreich erstellt", "todosManagement.delegated": "<PERSON><PERSON><PERSON><PERSON>", "todosManagement.dropdown.prefix": "Wird ange<PERSON>t:", "todosManagement.filters.sortBy": "Sortieren nach", "todosManagement.loadMoreButton": "Mehr anzeigen {$START_TAG_FIN_ICON}{$CLOSE_TAG_FIN_ICON}", "todosManagement.myTasks": "<PERSON><PERSON>", "todosManagement.noCaseRelated": "Nicht fallbezogen", "todosManagement.noTasks.header.message.closed": "<PERSON><PERSON> geschlossenen Aufgaben", "todosManagement.noTasks.header.message.completed": "<PERSON><PERSON> er<PERSON> Au<PERSON>ben", "todosManagement.noTasks.header.message.emptySearch": "<PERSON><PERSON>", "todosManagement.noTasks.header.message.pending": "<PERSON><PERSON> ausstehenden Aufgaben", "todosManagement.noTasks.regular.message.closed": "<PERSON><PERSON><PERSON> eine Aufgabe geschlossen wurde, wird sie hier angezeigt.", "todosManagement.noTasks.regular.message.completed": "<PERSON><PERSON><PERSON> eine Aufgabe erledigt wurde, wird sie hier angezeigt.", "todosManagement.noTasks.regular.message.emptySearch": "<PERSON>ür die angewandten Kriterien gibt es keine Ergebnisse.", "todosManagement.noTasks.regular.message.pending": "<PERSON><PERSON><PERSON> eine Aufgabe aussteht, wird sie hier angezeigt.", "todosManagement.pending": "<PERSON><PERSON><PERSON><PERSON>", "todosManagement.showTask": "Aufgabe anzeigen", "todosManagement.sortingOptions.asc": "<PERSON><PERSON><PERSON>", "todosManagement.sortingOptions.desc": "<PERSON><PERSON><PERSON>", "todosManagement.summary.description.manualAssignment": "<PERSON><PERSON><PERSON> bearbeiten", "todosManagement.summary.description.provideData": "<PERSON><PERSON><PERSON> im Data Room Informationen für das Feld {$START_TAG_SPAN}{$INTERPOLATION}{$CLOSE_TAG_SPAN} bereit.", "todosManagement.summary.description.reviewApplicationFrom": "Überprüfung des Antrags von", "todosManagement.summary.description.reviewContract": "Prüfen Sie Vertrag {$START_TAG_SPAN}“{$INTERPOLATION}”.{$CLOSE_TAG_SPAN}", "todosManagement.summary.dueDate": " Zu erledigen bis: {$START_TAG_SPAN}{$INTERPOLATION}{$CLOSE_TAG_SPAN}", "todosManagement.summary.tasksCount": "{$PH} Aufgabe", "todosManagement.summary.tasksCounts": "{$PH} Aufgaben", "todosManagement.table.columns.assignedBy": "<PERSON><PERSON><PERSON><PERSON><PERSON> von", "todosManagement.table.columns.assignedTo": "<PERSON><PERSON><PERSON><PERSON><PERSON> an", "todosManagement.table.columns.closedBy": "<PERSON><PERSON><PERSON><PERSON><PERSON> von", "todosManagement.table.columns.closedOn": "Geschlossen am", "todosManagement.table.columns.completedBy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> von", "todosManagement.table.columns.completedOn": "Abgeschlossen am", "todosManagement.table.columns.description": "Beschreibung", "todosManagement.table.columns.dueDate": "Zu erledigen bis", "todosManagement.table.columns.status": "Status", "todosManagement.table.columns.type": "Aufgabe", "todosManagement.table.description.reviewContract.prefix": "Prüfen Sie Vertrag {$START_TAG_SPAN}\"{$INTERPOLATION} / {$INTERPOLATION_1}\" {$CLOSE_TAG_SPAN}", "todosManagement.tasks": "Aufgaben", "todosManagement.toDo.toast.success": "To-do erfolgreich aktualisiert", "todosManagement.toDo.update.toast.error": "Ein Fehler ist aufgetreten", "todosManagement.tooltip.archivedAfter60days": "Aufgaben werden nach 60 Tagen automatisch archiviert.", "todosManagement.updateToDoModal.error.minDate.message": "Das Datum darf nicht in der Vergangenheit liegen", "todosManagement.updateToDoModal.headerLabel": "To-do bearbeiten", "topic.pipe.miscellaneous": "Sonstige Änderung", "topic.pipe.visibilityChanged": "Sichtbarkeit geändert", "typeToSearchText": "<PERSON><PERSON>", "unregistered.user.error": "<PERSON>utz<PERSON>, den Si<PERSON> e<PERSON>laden möchten, ist auf der neoshare Plattform nicht registriert.", "upload.file.size.error": "Die Datei ist zu groß. Die maximale Dateigröße beträgt 15 MB", "uploadOrganizationLogoModal.dropZoneTitle": "<PERSON>ä<PERSON>en Si<PERSON> eine PNG Datei aus", "uploadOrganizationLogoModal.title": "Logo hochladen", "usageContract.preview.modal.voidContract": "<PERSON><PERSON><PERSON><PERSON>", "usageContract.previewModal.title": " Nutzungsvertrаg Daten ", "usageContractCard.text": "Unterzeichnungstag", "usageContractList.createAgreements": "Einwilligung erstellen", "usageContracts.button.sign": "Signieren", "usageContracts.description": "<PERSON>te beachten Si<PERSON>, dass dadurch das gesamte Dokument annulliert wird, auch wenn es bereits von anderen Parteien signiert wurde.", "usageContracts.title": "Möchten Sie die Signierung ablehnen?", "user-menu.option1": "<PERSON><PERSON>", "userAuthentication.accountIsLockedText": "Ihr Konto ist derzeit gesperrt. Prüfen Sie Ihre E-Mail für weitere Anweisungen.", "userAuthentication.message": "Sie haben den geheimen Schlüssel immer noch nicht erhalten? Versuchen Si<PERSON>, einen neuen Schlüssel nach", "userAuthentication.message2": "zu senden.", "userAuthentication.text": "Sie müssen die Zwei-Faktor-Authentifizierung durchlaufen", "userAuthentication.timeOutMessage": "Sie haben den geheimen Schlüssel immer noch nicht erhalten?", "userAuthentication.timeOutMessage2": "Senden Si<PERSON> einen neuen.", "userAuthentication.wrongCodeText": "Falscher Code. Sie haben noch {$INTERPOLATION} Versuche übrig.", "userAuthentication.wrongCodeTextSingle": "Falscher Code. Sie haben noch 1 Versuch.", "userAuthentication.wrongCodeTextSingular": "Falscher Code. Sie haben noch 1 Versuch.", "userComponentMessage": "Achtung! Es existiert aktuell kein Nutzer mit der Rolle Ansprechpartner. Einladungen zu Finanzierungsvorhaben gehen somit verloren.", "userComponentSearchEmptyState": "Wir konnten keine Nutzer für die angewandten Kriterien finden.", "userData.toast.error": "Nutzerdaten konnten nicht gespeichert werden. Bitte überprüfen Sie Ihre Eingabe.", "userData.toast.success": "Nutzerdaten erfolgreich gespeichert.", "userDataForm.accountSecurity.authenticatedDevices": "Authentifizierte Geräte", "userDataForm.accountSecurity.code.description": "<PERSON><PERSON> wird ein 6-stelliger Code an Ihre in neoshare registrierte E-Mail geschickt.", "userDataForm.accountSecurity.description": "Zum besseren Schutz vor unbefugtem Zugriff müssen die Nutzer zwei oder mehr Authentifizierungsmethoden angeben. Wir empfehlen, diese Sicherheitsmaßnahme aktiviert zu lassen.", "userDataForm.accountSecurity.enabled": "Aktivieren", "userDataForm.accountSecurity.header": "Multi-Faktor-Authentifizierung", "userDataForm.accountSecurity.password.description": "Um Ihr Passwort zu ändern, werden Sie zum neoshare-<PERSON>gin weitergeleitet und können es von dort aus mit einem neuen Passwort zurücksetzen.", "userDataForm.accountSecurity.settings": "Authentifizierungsoptionen", "userDataForm.authenticatedDevices.label.current": "Aktuelles Gerät", "userDataForm.button.label.leaveForLater": "Später fortfahren", "userDataForm.button.label.logOut": "Abmelden", "userDataForm.control.label": "<PERSON><PERSON><PERSON>", "userDataForm.control.label.language": "<PERSON><PERSON><PERSON>", "userDataForm.control.label.lastName": "Nachname*", "userDataForm.control.label.location": "Standort*", "userDataForm.control.label.mobile": "Telefonnummer (Mobil)", "userDataForm.control.label.multiFactorAuthentication": "Multifaktor-Authentifizierung", "userDataForm.control.label.name": "Vorname*", "userDataForm.control.label.title": "Titel", "userDataForm.dialog.delete": "<PERSON><PERSON>, ich möchte", "userDataForm.dialog.title": "Sind <PERSON> sic<PERSON>, dass Si<PERSON> {$PH} aus Ihrer Liste der authentifizierten Geräte entfernen möchten?", "userDataForm.email": "E-Mail-Adresse", "userDataForm.errorMessage.mobileNumber": "Nummer in dieses Feld eintragen", "userDataForm.errorMessage.multiFactorSettings": "Sie können die SMS-Option nicht ohne Telefon nutzen.", "userDataForm.errorMessage.phoneNumber": "Die Telefonnummer muss eine Landesvorwahl enthalten, z.B. '+4965478955'", "userDataForm.errorMessage.requiredField4": "Die Telefonnummer muss eine Landesvorwahl enthalten, z.B. '+4965478955'", "userDataForm.errorMessage.unacceptableSymbol": "Unzulässiges Symbol", "userDataForm.errorMessage.unacceptableSymbol2": "Unzulässiges Symbol", "userDataForm.errorMessage.wrongMail": "Ungültige E-Mail-Adresse", "userDataForm.label.department": "Abteilung", "userDataForm.label.position": "Position", "userDataForm.label.tel": "Telefonnummer (Festnetz)", "userDataForm.modal.title": "Änderungen gespeichert! Sie haben {$PH} als primäre Authentifizierung gewählt. Bitte loggen Sie sich aus, um sie zu übernehmen.", "userDataForm.tabs.accountSecurity": "Kontosicherheit", "userDataForm.tabs.personalInformation": "Kontoinformationen", "userDataForm.tabs.preferences": "Präferenzen", "userDataForm.text.pleaseEnter": "Bitte verwenden Sie ausschließlich \"+\" und Zahlen", "userDataForm.title": "Persönliche Daten", "userDataForm.toast.error.message": "Fügen Sie eine Handynummer hinzu oder ändern Sie die Option der Mehrfaktor-Authentifizierung", "userDataForm.toast.success.logOut": "Die Änderungen werden gespeichert. Sie werden in 10 Sekunden abgemeldet", "userDataForm.toolTipContent2": "Bitte verwenden Sie ausschließlich \"+\" und Zahlen", "userEditor.createdUserMessage": "<PERSON>utzer erfolgreich angelegt", "userEditor.creatingUserError": "Fehler beim erstellen des Nutzers", "userEditor.mailExistsError": "Die E-Mail Adresse ist bereits registriert.", "userEditor.title": "Nutzerinformationen", "userEditor.title.new": "Neuen Nutzer erstellen", "userEditor.title.newUser": "<PERSON><PERSON><PERSON>utzer anlegen", "userEditor.updatedUserMessage": "Nutzer erfolgreich aktualisiert", "userEditor.updatingUserError": "Fehler beim Aktualisieren des Nutzers", "userEditorDeleteUser.message": "Hier<PERSON> stimmen Sie der Pseudonymisierung des Nutzers und der Auflösung seines Accounts zu. Die Löschung erfolgt eigenverantwortlich durch den Daten- bzw. Nutzerverantwortlichen", "userEditorDeleteUser.permanentDeleteEmailMatch": "E-Mail-Adresse des Nutzers", "userEditorDeleteUser.permanentDeleteEmailMatchError": "Ungültige E-Mail-Adresse", "userEditorDeleteUser.title": "Möchten Sie diesen Nutzer dauerhaft löschen?", "userExperience.feedback.invite.text": " <PERSON><PERSON><PERSON><PERSON> Si<PERSON> uns bitte Ihre Meinung sagen? Sind Sie mit der Antwort zufrieden? ", "userExperience.feedback.submit.button.label": "Feed<PERSON> abs<PERSON><PERSON>n", "userImport.toaster.message.success": "Nutzer erfolgreich importiert.", "userList.createUser": "<PERSON><PERSON><PERSON> er<PERSON>", "userManagement.list.title": "Nutzerverwaltung", "userManagement.tabs.accountLogs": "Kontoprotokolle", "userManagement.tabs.multiFactorAuth": "Multifaktor-Authentifizierung", "userManagement.tabs.password": "Passwortrichtlinien", "userManagement.tabs.signer": "<PERSON><PERSON><PERSON>", "userManagement.tabs.userInformation": "Nutzerinformationen", "userManagement.tooltip.delete": "Es muss zuerst eine weitere Administratorrolle vergeben werden, da mindestens zwei Administratoren vorhanden sein müssen, um einen löschen zu können.", "userManagement.user.column.firstName": "<PERSON><PERSON><PERSON>", "userManagement.user.column.lastName": "Nachname", "userManagement.user.column.mail": "E-Mail-Adresse", "userManagement.user.column.status": "Status", "userManagement.user.delete.success": "Der Nutzer wurde erfolgreich gelöscht", "userManagement.user.label.export": "Nutzer exportieren", "userManagement.user.label.import": "Nutzer importieren", "userManagement.user.label.openCreate": "<PERSON><PERSON><PERSON> an<PERSON>en", "userManagement.user.label.placeholder": "Nutzer suchen...", "userManagement.user.label.rework": "bearbeiten", "userManagement.user.userEditor.caseParticipation.soloCase": "Alleinigen Fallteilnehmer anzeigen: {$INTERPOLATION}", "userManagement.user.userEditor.caseParticipation.title": "Fallteilnahme", "userManagement.user.userEditor.caseParticipation.totalCases": "Alle Fälle: {$INTERPOLATION}", "userManagement.user.userEditor.checkbox.admin": "Administrator", "userManagement.user.userEditor.checkbox.backOffice": "Plattformnutzer", "userManagement.user.userEditor.checkbox.contact": "Kontaktperson", "userManagement.user.userEditor.checkbox.legalOfficer": "Rechtsreferent", "userManagement.user.userEditor.checkbox.manager": "Kooperationsmanager", "userManagement.user.userEditor.checkbox.platformManager": "Plattformmanager", "userManagement.user.userEditor.checkbox.usageContractSigner": "Nutzungsvertragszeichner", "userManagement.user.userEditor.delete": "Löschen", "userManagement.user.userEditor.deleteSolePlatformManagerTooltip": "<PERSON>ser Nutzer kann nicht gelö<PERSON>t werden, da es in Ihrer Organisation derzeit keinen Plattformmanager gibt.", "userManagement.user.userEditor.label": "Aktiv", "userManagement.user.userEditor.label.academicTitle": "Titel", "userManagement.user.userEditor.label.accessLevel": " Zugangsberechtigungen ", "userManagement.user.userEditor.label.department": "Abteilung", "userManagement.user.userEditor.label.firstName": "<PERSON><PERSON><PERSON>", "userManagement.user.userEditor.label.landLineNumber": "Festnetznummer", "userManagement.user.userEditor.label.lastName": "Nachname", "userManagement.user.userEditor.label.mail": "E-Mail-Adresse", "userManagement.user.userEditor.label.mobileNumber": "<PERSON><PERSON><PERSON><PERSON>", "userManagement.user.userEditor.label.name": "<PERSON><PERSON><PERSON>", "userManagement.user.userEditor.label.organisation": "Organisation", "userManagement.user.userEditor.label.position": "Position", "userManagement.user.userEditor.multiFactor.label.active": "Multifaktor-Authentifizierung aktiv", "userManagement.user.userEditor.password.label.UpperCase": "Mindestens ein Groß­buch­sta­be", "userManagement.user.userEditor.password.label.digits": "Mindestens eine Zahl", "userManagement.user.userEditor.password.label.expiration": " Passwort läuft ab (Tage) ", "userManagement.user.userEditor.password.label.length": "Minimale Länge (8 Zeichen)", "userManagement.user.userEditor.password.label.lowerCase": "Mindestens ein Kleinbuchstabe", "userManagement.user.userEditor.password.label.notUserName": "<PERSON><PERSON> nicht die E-Mail-Adresse enthalten", "userManagement.user.userEditor.password.label.specialChars": "Mindestens ein Sonderzeichen", "userManagement.user.userEditor.platformUserSolePlatformManagerTooltip": "Die Nutzerrolle kann nicht geändert werden weil Ihre Organisation derzeit keinen Plattformmanager hat.", "userManagement.user.userEditor.solePlatformManagerTooltipForDeleteButton": "<PERSON>ser Nutzer kann nicht gelö<PERSON>t werden, da es in Ihrer Organisation derzeit keinen Plattformmanager gibt.", "userManagement.user.userEditor.solePlatformManagerTooltipForPlatformUserCheckbox": "Die Nutzerrolle kann nicht geändert werden weil Ihre Organisation derzeit keinen Plattformmanager hat.", "userManagement.user.userEditor.solePlatformManagerTooltipForUserStatus": "Der Nutzer kann nicht deaktiviert werden weil Ihre Organisation derzeit keinen Plattformmanager hat.", "userManagement.user.userEditor.status.description": "Hinweis: Deaktivierte Nutzer haben keinen Zugriff auf neoshare.", "userManagement.user.userEditor.status.title": "Nutzerstatus", "userManagement.user.userEditor.statusSolePlatformManagerTooltip": "Der Nutzer kann nicht deaktiviert werden weil Ihre Organisation derzeit keinen Plattformmanager hat.", "userManagement.user.userEditor.svgDeleteAction.message": " <PERSON>lick<PERSON> Sie auf \"Löschen\", um den Nutzer endgültig zu löschen. Diese Aktion kann nicht mehr rückgängig gemacht werden. ", "userManagement.user.userEditor.svgDeleteAction.title": "Dauerhaft löschen", "userManagement.user.userEditor.unregistered": "<PERSON>re<PERSON><PERSON><PERSON>", "userManagement.userImportModal.button.label.import": "Nutzer importieren", "userManagement.userImportModal.modalSubTitle": " Laden Sie eine neue Nutzerliste ", "userManagement.userImportModal.modalTitle": " Nutzer importieren ", "userManagement.userImportModal.singleFileUpload.placeholder": "Nachname e<PERSON>ben", "userManagement.users.noData": "Daten werden geladen...", "userModel.title.Ddr": "Ddr.", "userModel.title.Dipl.-Ing.oderDI": "Dipl. -Ing. oder DI", "userModel.title.Dkfm": "Dkfm.", "userModel.title.Dr.": "Dr.", "userModel.title.Ing.": "Ing.", "userModel.title.MMag.": "MMag.", "userModel.title.Mag": "Mag.", "userModel.title.Prof.": "Prof.", "userModel.title.Prof. Dr.": "Prof. Dr.", "userModels.language.english": "<PERSON><PERSON><PERSON>", "userModels.language.german": "De<PERSON>ch", "userModels.salutations.1": "Ddr.", "userModels.salutations.2": "Dipl. -Ing. oder DI", "userModels.salutations.3": "Dkfm.", "userModels.salutations.4": "Dr.", "userModels.salutations.5": "Ing.", "userModels.salutations.6": "Mag.", "userModels.salutations.7": "MMag.", "userModels.salutations.8": "Prof.", "userModels.salutations.9": "Prof. Dr.", "userModels.salutations.mister": "<PERSON>", "userModels.salutations.mrs": "<PERSON><PERSON>", "userSettings.button.label.logout": "Abmelden", "userSettings.button.label.updatePass": "Passwort ändern", "userSettings.deletePhoto.tooltip": "Foto löschen", "userSettings.feedback": "<PERSON><PERSON><PERSON>", "userSettings.label.notVerfied": "Nicht verifiziert", "userSettings.label.verfied": "Verifiziert", "userSettings.language.english": "<PERSON><PERSON><PERSON>", "userSettings.language.german": "De<PERSON>ch", "userSettings.preferences.regionalSettings.currencyFormat": "Währungsformat:", "userSettings.preferences.regionalSettings.dateFormat": "Datumsformat:", "userSettings.preferences.regionalSettings.numberFormat": "Zahlenformat:", "userSettings.preferences.regionalSettings.percentageFormat": "Prozentformat:", "userSettings.preferences.regionalSettings.region.germany": "Deutschland", "userSettings.preferences.regionalSettings.region.unitedKingdom": "Vereinigtes Königreich", "userSettings.preferences.regionalSettings.regionSelectLabel": "Wählen Sie Ihre bevorzugte Region:", "userSettings.preferences.regionalSettings.title": "Sprache & Region", "userSettings.preferences.termsOfUse.GDPR.content": "Rechtsgrundlage der Datenverarbeitungen ist Ihre Einwilligung nach Art.6 Abs. 1 S. 1 lit. a DSGVO. {$LINE_BREAK}Weitere Informationen zur Datenverarbeitung finden Sie in der {$START_LINK}Datenschutzerklärung{$CLOSE_LINK}.", "userSettings.preferences.termsOfUse.GDPR.title": "GDPR", "userSettings.preferences.termsOfUse.analysisPanel.content": "Ich stimme zu, dass meine Interaktionen mit der neoshare Plattform analysiert werden, um die Plattform zu verbessern und weiterzuentwickeln. Die Einwilligung zur Aufzeichnung von Plattforminteraktionen können Sie jederzeit unter dem Reiter „Präferenzen“ in „Mein Konto“ widerrufen.", "userSettings.preferences.termsOfUse.analysisPanel.contentTitle": "Daten und Analysen zur Nutzerfreundlichkeit", "userSettings.preferences.termsOfUse.analysisPanel.title": "Daten und Analysen zur Nutzerfreundlichkeit", "userSettings.preferences.termsOfUse.marketingPanel.content": "Ich stimme zu, dass die neoshare AG meine Daten (Anrede, Vorname, Nachname, dienstliche E-Mail-Adresse, Unternehmen, Position zum Unternehmen, Land) nutzt, um mich zielgruppenorientiert per E-Mail werblich anzusprechen und mich über ihre Produkte, Dienstleistungen, Angebote, Veranstaltungen und Events zu informieren. Ich willige ein, dass die Daten sowie Angaben dazu, ob ich die E-Mails erhalten habe und in welchem Umfang ich mit dem Inhalt interagiert habe, genutzt werden, um die Wirksamkeit von E-Mail-Kampagnen zu messen.  \r\n\r\nDie Einwilligung in den Bezug von Marketing-E-Mails und die Auswertung Ihrer Interaktionen mit diesen kann jederzeit über den Abmeldelink in den E-Mails, oder unter dem Reiter „Präferenzen“ in „Mein Konto“ widerrufen werden. ", "userSettings.preferences.termsOfUse.marketingPanel.title": "Marketing-E-Mails", "userSettings.preferences.termsOfUse.organisationsWarning": "<PERSON>te beach<PERSON> Si<PERSON>, dass die Einwilligungserklärungen ausschließlich für Organisationen gelten, die mit Ihrem Login verbunden sind.", "userSettings.preferences.termsOfUse.subtitle": "<PERSON><PERSON>r ein nahtloses und sicheres Erlebnis.", "userSettings.preferences.termsOfUse.title": "Einwilligungserklärung", "userSettings.preferences.termsOfUse.warning": "Bitte akzeptieren Sie unsere Nutzungsbedingungen", "userSettings.resetPasswordModal.label": " <PERSON>e werden in 15 Sekunden automatish ausgeloggt. Bitte loggen Sie sich erneut ein, um ihr neues Passwort festzulegen. ", "userSettings.resetPasswordModal.title": " Die Passwortänderung wurde initiiert. ", "userSettings.sessionForm.feedback": "Feedback-Schaltfläche anzeigen", "userSettings.settingsSection.authMode": "Authentifizierungs-App", "userSettings.settingsSection.button.label.code": "Code erneut senden", "userSettings.settingsSection.button.label.verify": "Verifizieren", "userSettings.settingsSection.header": " Multifaktor-Authentifizierung (MFA) ", "userSettings.settingsSection.mail": " E-Mail", "userSettings.settingsSection.page.header": "<PERSON><PERSON>", "userSettings.settingsSection.phoneHint": " Bitte geben Sie Ihre Mobilnummer beginnend mit Ländercode für Deutschland (+49) ein: ", "userSettings.settingsSection.placeholder.phone": "Mobilnummer (+49...)", "userSettings.settingsSection.subTitle": " Authentifikationsmethode ", "userSettings.settingsSection.textInput.placeholder": "SMS Verifizierungscode", "userSettings.settingsection.button.label": "Änderungen speichern", "userSettings.title": " Benutzerdaten bearbeiten ", "userSettings.toast.error": "Das Passwort konnte nicht zurückgesetzt werden.", "userSettings.toast.error.multiFactor": "Die Multifaktor-Authentifikationsmethode konnte nicht aktualisiert werden.", "userSettings.toast.error.resendSMS": "<PERSON>hnen konnte keine SMS zugesendet werden.", "userSettings.toast.success": "Die Multifaktor-Authentifikationsmethode wurde aktualisiert.", "userSettings.toast.success.resendSMS": "<PERSON>hnen wurde erneut ein SMS zugesendet.", "userSettings.toast.warning": "Ihr Eingabe ist fehlerhaft. Bitte korrigieren Sie Ihre Eingabe.", "userSettings.toastMessage.error.mobileNumber": "Ihre Mobilnummer konnte nicht verifiziert werden.", "userSettings.toastMessage.error.wrongSMSCOde": "Falscher SMS-Code. Bitte überprüfen Sie Ihre Eingabe.", "userSettings.toastMessage.success": "Ihre Mobilnummer wurde erfolgreich verifiziert!", "userSettings.uploadPhoto.tooltip": "<PERSON>il<PERSON><PERSON> ho<PERSON>n", "userSettings.uploadPhotoModal.dropZoneTitle": "<PERSON><PERSON>, JPEG oder PNG Datei auswählen", "userSettings.uploadPhotoModal.dropZoneTitle2": "oder nutzen Sie Drag & Drop", "userSettings.uploadPhotoModal.fileTooLarge": "Die Datei ist zu groß. Die maximale Dateigröße beträgt 10 MB.", "userSettings.uploadPhotoModal.title": "<PERSON>il<PERSON><PERSON> ho<PERSON>n", "userSettings.userRoles.roles": " Zugewiesene Rollen", "userSettings.verificationCode": " Wir haben eine SMS an die angegeben Nummer gesendet, um diese zu verifizieren. Bitte geben Sie den Code ein: ", "userSigning.asideLayout.logoTitle": "Sicherheit der nächsten Generation angetrieben durch KI", "userSigning.asideLayout.title": " Will<PERSON>mmen bei ", "userSigning.language.label": "Sprache auswählen:", "userSigning.leftLayout.title": " Will<PERSON>mmen bei ", "userSigning.mainPage.eightSectionText": " Sie können die Unterzeichnung des Dokuments jederzeit ablehnen, indem Sie auf Ablehnen klicken. <PERSON>te beachten Sie, dass dadurch das gesamte Dokument annulliert wird, auch wenn es bereits von anderen Parteien unterzeichnet wurde. ", "userSigning.mainPage.fifthSectionText": " <PERSON>e wird so erstellt, dass der Unterzeichner die Kontrolle behält; ", "userSigning.mainPage.firstSectionText": " Digitale Signaturen sind wie elektronische \"Fingerabdrücke\". Sie sind eine besondere Art der elektronischen Signatur (E-Signatur). In den nächsten Schritten können Sie Ihr Dokument mit Hilfe der ", "userSigning.mainPage.firstSectionText2": " überprüfen und unterzeichnen, einem Standard, der von", "userSigning.mainPage.firstSectionText3": " dem Rechtsrahmen der Europäischen Union für elektronische Signaturen, gere<PERSON>t wird und von allen EU-Mitgliedstaaten übernommen wurde. ", "userSigning.mainPage.firstSectionText4": " Advanced Electronic Signature (AdES) ", "userSigning.mainPage.fourthSectionText": " Sie ist eindeutig mit dem Unterzeichner verknüpft und in der Lage, ihn zu identifizieren; ", "userSigning.mainPage.secondSectionText3": "<PERSON><PERSON> der", "userSigning.mainPage.secondSectionText4": " Advanced Electronic Signature (AdES) ", "userSigning.mainPage.seventhSectionText": " <PERSON><PERSON> auf ", "userSigning.mainPage.seventhSectionText2": "<PERSON><PERSON> ", "userSigning.mainPage.seventhSectionText3": " <PERSON><PERSON><PERSON>, erhalten Sie einen Signatur-Authentifizierungscode per E-Mail. Bitte verwenden Sie den Code, um Ihre Identität zu verifizieren, das Dokument zu überprüfen und im nächsten Schritt zu signieren.", "userSigning.mainPage.sixtSectionText": " <PERSON>e ist so mit dem Dokument verknü<PERSON>t, dass jede nachträgliche Änderung der Daten erkennbar ist. ", "userSigning.mainPage.thirdSectionText": " handelt es sich um eine elektronische Signatur, die zusätzlich zur ", "userSigning.mainPage.thirdSectionText2": " \"einfachen\" elektronischen Signatur", "userSigning.mainPage.thirdSectionText3": "die folgenden Hauptmerkmale aufweist:", "userSigning.mainPage.title": " Signieren Sie rechtsverbindliche Dokumente digital mit neoshare, unterstütz<PERSON> von ", "userSigning.reject.description": "Alle beteiligten Parteien wurden benachrichtigt.", "userSigning.reject.title": "Sie haben die Signierung des Dokuments abgelehnt.", "userSigning.sessionExpired.title": "Ups... Ihre Sitzung ist abgelaufen.", "userSigning.signLater.description": "<PERSON>n Si<PERSON> bereit sind, das Dokument zu unterschreiben, starten <PERSON> bitte den Prozess ab der ersten E-Mail.", "userSigning.signLater.title": "<PERSON><PERSON> spät<PERSON>.", "userSigning.success.description": "Nachdem alle unterschrieben haben, erhalten Sie die finale Kopie per E-Mail.", "userSigning.success.title": "Sie haben das Dokument unterschrieben.", "userSigning.tryAgain": "<PERSON><PERSON><PERSON> versuchen", "userSigning.voided.description": "<PERSON>ür nähere Informationen wenden Si<PERSON> sich bitte an den Nutzer, der die Unterzeichnung des Dokuments beantragt hat. Diese Information wurde in der Einladungsmail angegeben.", "userSigning.voided.title": "Die Signierung des Dokuments wurde annulliert.", "userTable.columns.createdOn": "Erstellt am", "userTable.columns.name": "Name", "userTable.noRole": "<PERSON><PERSON> Rolle zugewiesen", "userValidation.currentPasswordIncorrect": "Falsches Passwort!", "userValidation.migrated-users-warning": "Hinweis: Im Rahmen unseres Sicherheitsupgrades haben wir Ihnen ein neues Passwort per E-Mail zugesandt. Bitte verwenden Sie dieses neue Passwort um sich einzuloggen und mit der neoshare-Plattform fortzufahren.", "userValidation.remainingAttempts": "Sie haben noch {$INTERPOLATION} Versuche.", "userValidation.remainingAttemptsSingular": "Sie haben noch 1 Versuch.", "userValidation.wrongPassword": "Ungültiges Passwort. Sie haben {$INTERPOLATION} Versuche übrig.", "userValidation.wrongPasswordSingular": "Ungültiges Passwort. Sie haben noch 1 Versuch.", "users.removeUser.tooltipMessage": "Es muss zuerst eine weitere Administratorrolle vergeben werden,\r\nda mindestens zwei Administratoren vorhanden sein müssen,\r\num einen löschen zu können.", "users.roles.disabledPlatformUser": "Plattformnutzer kann nicht abgewählt werden, wenn Kontaktperson oder Plattformmanager zugewiesen wurde.", "users.usersEdit.errorMessage.emailTaken": "Dieser E-Mail-Adresse ist vergeben", "validationError.ascendingOrder": "Die Werte der Intervall-Felder müssen in aufsteigender Reihenfolge sein", "validationError.atleastOneUserSelected": "Es muss mindestens ein Nutzer ausgewählt sein", "validationError.invalidDate": "Datum ist ungültig", "validationError.invalidSymbol": "Unzulässiges Symbol", "validationError.mailTaken": "Die E-Mail-Adresse ist bereits registriert.", "validationError.message": "Die E-Mail Adresse ist bereits registriert.", "validationError.nameNotUnique": "Dieser Gruppenname wird bereits verwendet.", "validationError.requiredField": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "validationError.wrongEmailAddress": "Ungültige E-Mail-Adresse", "volksbankGroups.annualFinancialStatements": "Jahresabschl./GuV-Berechnungen", "volksbankGroups.articlesOfAssociationLegal": "Gesellschaftsverträge (Rechtsverhältnisse)", "volksbankGroups.articlesOfAssociationLegitimation": "Gesellschaftsverträge (Legitimation)", "volksbankGroups.authorizationOfEasements": "Bewilligung Dienstbarkeiten", "volksbankGroups.buildingEncumbrances": "Baulasten-/Altlastenkataster", "volksbankGroups.buldingController": "Baucontroll<PERSON>", "volksbankGroups.bwa": "BWA", "volksbankGroups.collateralManagementLoan": "Kredit Sicherheitenverwaltung", "volksbankGroups.collateralManagementLoanOther": "Kredit Sicherheiten Sonstiges", "volksbankGroups.contracts": "Verträge", "volksbankGroups.contractsDeeds": "Verträge / Urkunden", "volksbankGroups.creditworthiness": "Bonität", "volksbankGroups.economicConditionsLoan": "Kredit Wirtsch. Verhältnisse", "volksbankGroups.economicConditionsLoanOther": "Kredit WV Sonstiges", "volksbankGroups.enforceableCopies": "Vollstreckbare Ausfertigungen", "volksbankGroups.floorPlansParcelMaps": "Grundrisse, Flurkarten", "volksbankGroups.generalInformation": "Auskünfte", "volksbankGroups.gsDeclarationsOfPurpose": "GS-Zweckerklärungen", "volksbankGroups.hrExtractsShareholdersLegal": "HR-<PERSON><PERSON><PERSON><PERSON>, Gesellschafter (Rechtsverhältnisse)", "volksbankGroups.hrExtractsShareholdersLegitimation": "HR-<PERSON><PERSON><PERSON><PERSON>, Gesellschafter (Legitimation)", "volksbankGroups.income": "Einkommen", "volksbankGroups.incomeTaxDocuments": "ESt.Unterlagen", "volksbankGroups.industryInformation": "Brancheninformation", "volksbankGroups.ioBalanceSheet": "E/A Bilanz", "volksbankGroups.kwgDocumentation": "§18 KWG-Dokumentationen", "volksbankGroups.landChargeLetters": "Grundschuld-Briefe", "volksbankGroups.landChargeLoan": "<PERSON><PERSON><PERSON>", "volksbankGroups.landChargeLoanOther": "Kredit Grundschuld Sonstiges", "volksbankGroups.landRegisterExtracts": "Grundbuchauszüge", "volksbankGroups.leaseholdContracts": "Erbbaurechtsverträge", "volksbankGroups.liquidityCalculations": "Liquiditätsberechnungen", "volksbankGroups.otherCreditworthinessDocuments": "Sonstige Bonitätsunterlagen", "volksbankGroups.otherInformation": "Sonstige Auskünfte", "volksbankGroups.partitionDeclarations": "Teilungserklärungen", "volksbankGroups.personGeneralInformation": "Person Allgemein Auskünfte", "volksbankGroups.personLegalRelationships": "Person Rechtsverhält<PERSON>", "volksbankGroups.personLegalRelationshipsOther": "PRechtsSonstiges", "volksbankGroups.personLegitimation": "Person Legitimation", "volksbankGroups.personLegitimationOther": "Person Legitimation Sonstiges", "volksbankGroups.projectDocuments": "Projektunterlagen", "volksbankGroups.propertyDocuments": "Objektunterlagen", "volksbankGroups.propertyPictures": "Objektbilder", "volksbankGroups.purchaseContracts": "Kaufverträge", "volksbankGroups.realEstateManagementLoan": "Kredit Immobilienverwaltung", "volksbankGroups.realEstateManagementLoanOther": "Kredit Immoverwaltung Sonstiges", "volksbankGroups.rentalIncome": "Mieteinkünfte", "volksbankGroups.rentalLeaseAgreements": "Miet-/ Pachtverträge", "volksbankGroups.salaryStatement": "Gehaltsnachweis", "volksbankGroups.schufaInformation": "Schufa-Auskünfte", "volksbankGroups.selfInformation": "Selbstauskünfte", "volksbankGroups.singleValuationDeclarations": "Einmalvalutierungserklärungen", "volksbankGroups.statementOfAssets": "Vermögensübersicht", "volksbankGroups.statementOfAssetsSelfDeclaration": "Vermögensaufst. /Selbstausk.", "volksbankGroups.supplementaryValuationDocuments": "<PERSON><PERSON><PERSON>unterlagen", "volksbankGroups.taxAssessmentNotice": "Steuerbescheid", "volksbankGroups.taxConsultantPowerOfAttorney": "Steuerberater-Vollmacht", "volksbankGroups.taxReturn": "Steuererklärung", "volksbankGroups.valuationsAppraisals": "Wertermittlungen / Gutachten", "warning.field.totalEquity": "Die Summe der einzeln erfassten Eigenkapitalbausteine weicht von der Gesamtsumme ab.", "watchlisttabs.header": "Alle Ergebnisse", "write.email.label": "E-Mail schreiben"}}