<div class="numerical-stats">
  <app-statistics-item>
    <div label i18n="@@cases.statistics.totalParticipation">
      Gesamtbeteiligung
    </div>
    <div value>
      <ui-icon name="svgEuro"></ui-icon>
      <ui-monetary-expression
        [value]="stats?.totalParticipationAmount ?? 0"
        [showCurrencySign]="false"
      ></ui-monetary-expression>
    </div>
  </app-statistics-item>
  <app-statistics-item>
    <div label i18n="@@cases.statistics.ongoingCases">Laufende Fälle</div>
    <div value>
      @if (activeCasesIcons.length > 0) {
        <ui-stacked-icons
          [template]="activeCasesTooltip"
          [icons]="activeCasesIcons"
        ></ui-stacked-icons>
      }
      {{ stats?.totalActiveCases?.total ?? 0 | number | removeTrailingZeros }}
    </div>
  </app-statistics-item>
  <app-statistics-item>
    <div label i18n="@@cases.statistics.completedCases">
      Abgeschlossene Fälle
    </div>
    <div value>
      @if (closedCasesIcons.length > 0) {
        <ui-stacked-icons
          [template]="closedCasesTooltip"
          [icons]="closedCasesIcons"
        ></ui-stacked-icons>
      }
      {{ stats?.totalClosedCases?.total ?? 0 | number | removeTrailingZeros }}
    </div>
  </app-statistics-item>
</div>

<ng-template #activeCasesTooltip>
  @for (stat of stats?.totalActiveCases.distribution | keyvalue; track stat) {
    <p class="case-stat">
      {{ internationalizationTooltip(stat) }}
    </p>
  }
</ng-template>

<ng-template #closedCasesTooltip>
  @for (stat of stats?.totalClosedCases.distribution | keyvalue; track stat) {
    <p class="case-stat">
      {{ internationalizationTooltip(stat) }}
    </p>
  }
</ng-template>
