import { PercentPipe } from '@angular/common';
import {
  Component,
  DestroyRef,
  OnDestroy,
  OnInit,
  TemplateRef,
  ViewChild,
} from '@angular/core';

import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { ActivatedRoute, NavigationEnd, Router } from '@angular/router';
import { SearchFilterComponent } from '@fincloud/components/search-filter';
import { BusinessCaseModelService } from '@fincloud/core/business-case';
import { LayoutCommunicationService } from '@fincloud/core/layout';
import { selectAllowedPages } from '@fincloud/state/access';
import { selectCustomer, selectCustomerType } from '@fincloud/state/customer';
import { selectCanSeeCasesStatistics } from '@fincloud/state/dashboard';
import {
  selectIsPlatformManager,
  selectUserCustomerKey,
} from '@fincloud/state/user';
import { Customer } from '@fincloud/swagger-generator/authorization-server';
import {
  ExchangeService,
  SearchBusinessCaseResultDto,
} from '@fincloud/swagger-generator/exchange';
import {
  BusinessCaseType,
  CustomerType,
  Permission,
} from '@fincloud/types/enums';
import { AppState, InvitationBusinessCase } from '@fincloud/types/models';
import { NgbNavChangeEvent } from '@ng-bootstrap/ng-bootstrap';
import { Store } from '@ngrx/store';
import { every } from 'lodash-es';
import {
  BehaviorSubject,
  Observable,
  combineLatest,
  filter,
  map,
  of,
  shareReplay,
  switchMap,
  take,
  tap,
} from 'rxjs';
import {
  selectApplicationsData,
  selectCustomerKeyNames,
  selectInvitationsData,
} from '../../+state';
import { CasesPageActions } from '../../+state/actions';
import { ApplicationAndInvitationBusinessCase } from '../../models/application-and-invitation-business-case';
import { ApplicationBusinessCase } from '../../models/application-business-case';
import { InviationApplicationFilters } from '../../models/invitation-application-case-filters';
import { TabNames } from '../../models/tab-names';
import { DEFAULT_APPLICATION_AND_INVITATION_STATUSES } from '../../utils/default-application-and-invitation-statuses';
import { DEFAULT_APPLICATION_STATUSES } from '../../utils/default-application-statuses';
import { DEFAULT_INVITATION_STATUSES } from '../../utils/default-invitation-statuses';

@Component({
  selector: 'app-case-overview',
  templateUrl: './case-overview.component.html',
  styleUrls: ['./case-overview.component.scss'],
  providers: [BusinessCaseModelService, PercentPipe],
})
export class CaseOverviewComponent implements OnInit, OnDestroy {
  @ViewChild('searchFilterComponent', { static: false })
  searchFilter: SearchFilterComponent;

  @ViewChild('filtersTemplate')
  set filtersTemplate(template: TemplateRef<unknown>) {
    if (template) {
      this.layoutCommunicationService.setRightOverlayPanelTemplate(template);
    }
  }

  activeTab: TabNames;
  selectedTabId: string;
  customerKey$ = this.store.select(selectUserCustomerKey);

  searchFilterFocused$ = of(true);
  searchTerm = '';
  selectedCaseTypes: BusinessCaseType[] = [];
  filtersApplied = false;

  casesStatistics$ = this.customerKey$.pipe(
    filter(Boolean),
    take(1),
    switchMap((customerKey) => {
      return this.exchangeService.exchangeControllerGetCustomerBusinessCasesStatistics(
        {
          customerKey,
        },
      );
    }),
  );

  isPlatformManager$ = this.store.select(selectIsPlatformManager);
  canSeeStatistics$ = this.store.select(selectCanSeeCasesStatistics);

  // Applications
  applicationBusinessCases: ApplicationBusinessCase[];
  applicationBusinessCaseCache: ApplicationBusinessCase[];
  selectedApplicationStatuses = DEFAULT_APPLICATION_STATUSES;

  /* - - - - - - Invitations - - - - - - */

  // Invitations
  invitationBusinessCases: InvitationBusinessCase[];
  invitationBusinessCasesCache: InvitationBusinessCase[];
  selectedInvitationStatuses = DEFAULT_INVITATION_STATUSES;

  /* - - - - - - Applications and Invitations - - - - - - */

  applicationsAndInvitations: ApplicationAndInvitationBusinessCase[];
  selectedApplicationsAndInvitationsStatuses =
    DEFAULT_APPLICATION_AND_INVITATION_STATUSES;
  applicationsAndInvitationsCache: ApplicationAndInvitationBusinessCase[];

  /* - - - - - - Customer  - - - - - - */

  customer$ = this.store
    .select(selectCustomer)
    .pipe(filter(Boolean), shareReplay({ bufferSize: 1, refCount: true }));

  customerType$ = this.store.select(selectCustomerType);

  isRealEstateCorporate$ = this.customerType$.pipe(
    map(
      (customerType) =>
        customerType === CustomerType.REAL_ESTATE ||
        customerType === CustomerType.CORPORATE,
    ),
  );

  customerKeyNames$ = this.store.select(selectCustomerKeyNames);

  get permissionHtml(): typeof Permission {
    return Permission;
  }

  /* - - - - - - Empty state Applications/Invitations  - - - - - - */

  get isInvitationsApplicationsInitialized() {
    return !!this.applicationsAndInvitationsCache;
  }

  get isInvitationsInitialized() {
    return !!this.invitationBusinessCasesCache;
  }

  get isInvitationsApplicationsFullEmptyState() {
    return (
      !this.isInvitationsApplicationsInitialized ||
      this.applicationsAndInvitationsCache?.length === 0
    );
  }

  get isInvitationsFullEmptyState() {
    return (
      !this.invitationBusinessCasesCache ||
      this.invitationBusinessCasesCache?.length === 0
    );
  }

  invitationsApplicationsEmptyStateMessage$ = of(
    $localize`:@@cases.invitationsApplications.fullEmptyState:Sie haben noch keine Bewerbung verschickt oder Einladung erhalten.`,
  );

  /* - - - - - - Tabs  - - - - - - */

  invitationsTab$: Observable<boolean> = this.store
    .select(selectAllowedPages)
    .pipe(map((pageAccess) => pageAccess.Cases.sections.invitations.read));

  applicationsTab$: Observable<boolean> = this.store
    .select(selectAllowedPages)
    .pipe(map((pageAccess) => pageAccess.Cases.sections.applications.read));

  showApplicationsAndInvitationsTab$: Observable<boolean> = combineLatest([
    this.applicationsTab$,
    this.invitationsTab$,
  ]).pipe(
    map(([applicationsAccess, invitationsAccess]) => {
      return applicationsAccess && invitationsAccess;
    }),
  );
  showInvitationsTab$: Observable<boolean> = combineLatest([
    this.showApplicationsAndInvitationsTab$,
    this.invitationsTab$,
  ]).pipe(
    map(([applicationsTabAccess, invitationsAccess]) => {
      return invitationsAccess && !applicationsTabAccess;
    }),
  );

  /* - - - - - - Simple streams  - - - - - - */

  participatingTabText$: Observable<string> = this.customer$.pipe(
    map(this.getMyActiveTabText),
  );

  /* - - - - - - Filters panel  - - - - - - */

  invitationApplicationFilters$ =
    new BehaviorSubject<InviationApplicationFilters>(null);

  get hasFiltersApplied() {
    switch (this.activeTab) {
      case 'applications-invitations':
        return (
          !!this.selectedCaseTypes?.length ||
          !!this.selectedApplicationsAndInvitationsStatuses?.length
        );
      case 'invitations':
        return (
          !!this.selectedCaseTypes?.length ||
          !!this.selectedInvitationStatuses?.length
        );
    }

    return false;
  }

  constructor(
    private destroyRef: DestroyRef,
    private route: ActivatedRoute,
    private router: Router,
    private store: Store<AppState>,
    private exchangeService: ExchangeService,
    private layoutCommunicationService: LayoutCommunicationService,
  ) {}

  ngOnInit() {
    [
      CasesPageActions.loadInvitations,
      CasesPageActions.loadApplications,
      CasesPageActions.loadCustomerKeyNames,
    ].forEach((action) => this.store.dispatch(action()));

    combineLatest([
      this.store.select(selectInvitationsData),
      this.store.select(selectApplicationsData),
    ])
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(([invitations, applications]) => {
        this.invitationBusinessCases = invitations;

        this.applicationBusinessCaseCache = applications;
        this.invitationBusinessCasesCache = invitations;

        if (this.applicationBusinessCaseCache) {
          this.applicationsAndInvitationsCache = [
            ...(this.applicationsAndInvitationsCache || []),
            ...this.applicationBusinessCaseCache,
          ];
        }

        if (this.invitationBusinessCasesCache) {
          this.applicationsAndInvitationsCache = [
            ...(this.applicationsAndInvitationsCache || []),
            ...this.invitationBusinessCasesCache,
          ];
        }

        this.applicationBusinessCases =
          this.getFilteredApplicationsAndInvitationsBusinessCases<ApplicationBusinessCase>(
            this.applicationBusinessCaseCache,
            (a) => a.businessCase,
          );
        this.invitationBusinessCases =
          this.getFilteredApplicationsAndInvitationsBusinessCases<InvitationBusinessCase>(
            this.invitationBusinessCasesCache,
            (i) => i.businessCase,
          );
        this.applicationsAndInvitations = [
          ...this.applicationBusinessCases,
          ...this.invitationBusinessCases,
        ];
      });

    this.invitationApplicationFilters$
      .pipe(
        takeUntilDestroyed(this.destroyRef),
        tap((filters: InviationApplicationFilters) => {
          this.selectedCaseTypes = filters?.businessCaseTypes;
          this.selectedApplicationsAndInvitationsStatuses =
            filters?.applicationsAndInvitations;
          this.selectedInvitationStatuses = filters?.applicationsAndInvitations;
          this.filterApplicationsAndInvitations();
        }),
      )
      .subscribe();

    this.route.firstChild?.url
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((url) => {
        this.activeTab = url[0].path as TabNames;
      });

    this.router.events
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((event: any) => {
        const casesUrlPart = 'cases';
        const casesIndex = event.url?.indexOf(`/${casesUrlPart}`);
        let activeTab = '';

        if (casesIndex !== -1) {
          activeTab = event.url?.substring(
            casesIndex + `/${casesUrlPart}/`.length,
          );
        }

        if (
          event instanceof NavigationEnd &&
          event.url !== '/' &&
          this.activeTab !== activeTab &&
          activeTab !== ''
        ) {
          this.activeTab = activeTab as TabNames;
        }
      });
  }

  ngOnDestroy(): void {
    this.layoutCommunicationService.clearRightOverlayPanelTemplate();
  }

  /* - - - - - - Utility functions  - - - - - - */

  getMyActiveTabText(customer: Customer): string {
    switch (customer.customerType) {
      case CustomerType.BANK:
      case CustomerType.FSP:
        return $localize`:@@cases.sidebar.bank:Bank`;
      case CustomerType.CORPORATE:
      case CustomerType.REAL_ESTATE:
        return $localize`:@@cases.sidebar.firm:Firma`;
      default:
        return $localize`:@@cases.sidebar.bankDefault:Bank`;
    }
  }

  onSearchTermSelected(searchTerm: string) {
    this.searchTerm = searchTerm;
    this.filterApplicationsAndInvitations();
  }

  onToggleFilters() {
    this.layoutCommunicationService.toggleRightSideOverlayPanel();
  }

  filterApplicationsAndInvitations() {
    switch (this.activeTab) {
      case 'applications-invitations': {
        this.filtersApplied =
          !!this.selectedCaseTypes?.length ||
          !!this.selectedApplicationsAndInvitationsStatuses?.length;
        this.applicationsAndInvitations =
          this.getFilteredApplicationsAndInvitationsBusinessCases<ApplicationAndInvitationBusinessCase>(
            this.applicationsAndInvitationsCache,
            (i) => i.businessCase,
          );

        break;
      }
      case 'invitations': {
        this.invitationBusinessCases =
          this.getFilteredApplicationsAndInvitationsBusinessCases<InvitationBusinessCase>(
            this.invitationBusinessCasesCache,
            (i) => i.businessCase,
          );
        break;
      }
    }
  }

  changeOrOpenNewTab(shouldExecuteOnClick: boolean, customerKey: string) {
    if (shouldExecuteOnClick && this.selectedTabId !== undefined) {
      this.activeTab = this.selectedTabId as TabNames;
      void this.router.navigate([customerKey, 'cases', this.activeTab]);
    }
  }

  onTabChanged(event: NgbNavChangeEvent | { status: string }) {
    this.layoutCommunicationService.clearRightOverlayPanelTemplate();

    this.selectedTabId =
      'status' in event &&
      ['CANCELED', 'DECLINED', 'EXPIRED', 'REJECTED'].includes(
        (<{ status: string }>event).status,
      )
        ? 'my-cases'
        : ((<NgbNavChangeEvent>event).nextId as TabNames);

    this.searchTerm = '';
    this.searchFilter?.setSearchPhrase(this.searchTerm);

    if (this.selectedTabId === 'applications-invitations') {
      this.store.dispatch(
        CasesPageActions.fetchNewInvitationsAndApplications(),
      );
    }

    if ('preventDefault' in event) {
      event.preventDefault();
    }
  }

  private getFilteredApplicationsAndInvitationsBusinessCases<
    T extends
      | ApplicationBusinessCase
      | InvitationBusinessCase
      | ApplicationAndInvitationBusinessCase,
  >(items: T[], byKey: (i: T) => SearchBusinessCaseResultDto): T[] {
    const cases = items ?? [];

    return cases.filter((item) => {
      const businessCase = byKey(item);
      const shouldMatch: boolean[] = [];

      if (this.searchTerm) {
        shouldMatch.push(
          [
            businessCase?.company?.companyInfo?.legalName,
            businessCase?.customer?.name,
            businessCase?.information?.financingProduct?.value,
            businessCase?.autoGeneratedBusinessCaseName,
          ]
            .filter((s) => !!s)
            .map((s) => s?.toString().toLocaleLowerCase())
            .some((s) => s.includes(this.searchTerm?.toLocaleLowerCase())),
        );
      }
      if (this.selectedCaseTypes?.length > 0) {
        shouldMatch.push(
          this.selectedCaseTypes.includes(
            businessCase?.businessCaseType as BusinessCaseType,
          ),
        );
      }

      if (this.activeTab === 'applications-invitations') {
        shouldMatch.push(this.shouldMatchApplicationAndInvitationStatus(item));
      }

      if (this.activeTab === 'invitations') {
        shouldMatch.push(
          !this.selectedInvitationStatuses?.length ||
            this.selectedInvitationStatuses.some(
              (v) =>
                v ===
                (item as InvitationBusinessCase).invitation?.invitationStatus,
            ),
        );
      }

      return every(shouldMatch, (m) => m === true);
    });
  }

  private shouldMatchApplicationAndInvitationStatus(
    item: ApplicationAndInvitationBusinessCase,
  ) {
    if (!this.selectedApplicationsAndInvitationsStatuses?.length) {
      return true;
    }

    return this.selectedApplicationsAndInvitationsStatuses.some((status) => {
      const applicationStatusPrefix = 'APPLICATION_';
      if (item.application && status.startsWith(applicationStatusPrefix)) {
        return (
          status.slice(applicationStatusPrefix.length) ===
          item.application.state
        );
      }

      if (item.invitation && !status.startsWith(applicationStatusPrefix)) {
        return status === item.invitation.invitationStatus;
      }

      return false;
    });
  }
}
