import { Component, DestroyRef } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { ScrollCommunicationService } from '@fincloud/core/scroll';
import { CompanyAnalysisState } from '@fincloud/types/models';
import { Store } from '@ngrx/store';
import { selectSectionIntoView } from '../../+state';
import { Link } from '../../models/link';

@Component({
  selector: 'app-sidebar-nav',
  templateUrl: './sidebar-nav.component.html',
  styleUrls: ['./sidebar-nav.component.scss'],
})
export class SidebarNavComponent {
  links: Link[] = [
    {
      label: $localize`:@@dashboard.overviewTab.contactPerson:Ansprechpartner`,
      id: '#ansprechpartner',
    },
    {
      label: $localize`:@@companyAnalysis.companyInformationSections.informationSection.title:Anmeldedaten`,
      id: '#anmeldedaten',
    },
    {
      label: $localize`:@@companyAnalysis.companyInformationSections.informationSection.title.liquidation:Liquidation`,
      id: '#liquidation',
    },
    {
      label: $localize`:@@companyAnalysis.companyInformationSections.informationSection.title.informationSection:Statistische Daten`,
      id: '#daten',
    },
  ];

  currentActive = 0;

  constructor(
    private destroyRef: DestroyRef,
    private store: Store<CompanyAnalysisState>,
    private scrollCommunicationService: ScrollCommunicationService,
  ) {
    this.store
      .select(selectSectionIntoView)
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((sectionIndex) => this.selectSection(sectionIndex));
  }

  selectSection(sectionIndex: number, clicked = false) {
    this.currentActive = sectionIndex;
    if (clicked) {
      this.scrollCommunicationService.scrollToElementById(
        this.links[sectionIndex].id,
        -105,
      );
    }
  }
}
