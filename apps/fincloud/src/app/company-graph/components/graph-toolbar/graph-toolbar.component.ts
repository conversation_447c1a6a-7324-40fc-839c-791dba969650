import {
  ChangeDetectionStrategy,
  Component,
  EventEmitter,
  Input,
  Output,
} from '@angular/core';
import { IconName } from '@fincloud/types/models';
import { FinButtonAppearance, FinButtonShape } from '@fincloud/ui/button';
import { Store } from '@ngrx/store';
import { shareReplay } from 'rxjs';
import { CompanyGraphPageActions } from '../../+state/actions';
import { companyGraphFeature } from '../../+state/reducers/company-graph.reducer';
import { CollapseOrExpandNodeType } from '../../enums/collapse-expand-node';

@Component({
  selector: 'app-graph-toolbar',
  templateUrl: './graph-toolbar.component.html',
  styleUrl: './graph-toolbar.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class GraphToolbarComponent {
  @Input() isFullScreen = false;
  @Input() hasNoDataAvailable = false;
  @Output() fullScreenToggle = new EventEmitter();
  finButtonAppearance = FinButtonAppearance;
  finButtonShape = FinButtonShape;
  showExpandButton = true;
  expandOrCollapseNode$ = this.store.select(
    companyGraphFeature.selectExpandOrCollapseNode,
  );
  collapseOrExpandType = CollapseOrExpandNodeType;

  showFilter = false;
  showUBOList = false;

  hasFiltersApplied$ = this.store
    .select(companyGraphFeature.selectHasFiltersApplied)
    .pipe(shareReplay({ refCount: true, bufferSize: 1 }));

  constructor(private store: Store) {}

  get fullScreenIcon(): IconName {
    return this.isFullScreen ? 'close_fullscreen' : 'open_in_full';
  }

  toggleFilter() {
    this.showFilter = !this.showFilter;
    this.showUBOList = false;
  }

  toggleUBOList() {
    this.showUBOList = !this.showUBOList;
    this.showFilter = false;
  }

  expandNode() {
    this.store.dispatch(CompanyGraphPageActions.expandNode());
  }

  collapseNode() {
    this.store.dispatch(CompanyGraphPageActions.collapseNode());
  }
}
