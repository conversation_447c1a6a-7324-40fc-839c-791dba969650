import { formatDate } from '@angular/common';
import {
  Component,
  DestroyRef,
  EventEmitter,
  Inject,
  Input,
  LOCALE_ID,
  OnChanges,
  OnInit,
  Output,
  SimpleChanges,
  ViewChild,
} from '@angular/core';
import {
  Chat,
  ChatManagementControllerService,
  ChatMessage,
  ChatMessageManagementControllerService,
  ChatNotificationStatusControllerService,
} from '@fincloud/swagger-generator/communication';
import { ExchangeBusinessCase } from '@fincloud/swagger-generator/exchange';
import { Store } from '@ngrx/store';
import { groupBy } from 'lodash-es';
import { filter, map, merge, of, switchMap, withLatestFrom } from 'rxjs';

import {
  InfiniteScrollComponent,
  ScrollPosition,
} from '@fincloud/components/layout';
import { DateService } from '@fincloud/core/date';
import { ModalService } from '@fincloud/core/modal';
import { SocketService, SocketType } from '@fincloud/core/socket';
import { Toast } from '@fincloud/core/toast';
import { ParticipatingUsersRepresentationComponent } from '@fincloud/neoshare/business-case';
import { TabNavigationService } from '@fincloud/neoshare/services';
import { selectAccessRights } from '@fincloud/state/access';
import {
  selectBusinessCase,
  selectCustomerNamesByKey,
} from '@fincloud/state/business-case';
import {
  StateLibChatHistoryRangePageActions,
  StateLibChatPageActions,
  selectExistingChats,
} from '@fincloud/state/chat';
import {
  selectUserCustomerKey,
  selectUserId,
  selectUserToken,
} from '@fincloud/state/user';
import {
  Customer,
  User,
} from '@fincloud/swagger-generator/authorization-server';
import {
  DocumentControllerService,
  DocumentEntity,
  InboxDocumentControllerService,
} from '@fincloud/swagger-generator/document';
import {
  BusinessCasePermission,
  ChatTab,
  ChatType,
  Locale,
} from '@fincloud/types/enums';
import {
  AppState,
  AttachmentsInfo,
  ChatAttachment,
  Dictionary,
  UserToken,
} from '@fincloud/types/models';

import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { RegionalSettingsService } from '@fincloud/core/regional-settings';
import { setTimeoutUnpatched } from '@fincloud/core/utils';
import { StateLibNotificationsPageActions } from '@fincloud/state/notifications';
import { FinToastService } from '@fincloud/ui/toast';
import { CHAT_SOCKET_RECEIVE_MESSAGE_DESTINATION } from '@fincloud/utils';
import { selectChatHistoryRange } from '../../+state/selectors/chat.selectors';
import { MessageViewModel } from '../../models/message-view-model';
import { FIVE_MINUTES_TIMESTAMP } from '../../utils/five-minutes-timestamp';
import { DownloadChatModalComponent } from '../download-chat-modal/download-chat-modal.component';

@Component({
  selector: 'app-chat-window',
  templateUrl: './chat-window.component.html',
  styleUrls: ['./chat-window.component.scss'],
})
export class ChatWindowComponent implements OnInit, OnChanges {
  @ViewChild(InfiniteScrollComponent) infiniteScroll: InfiniteScrollComponent;
  @ViewChild(ParticipatingUsersRepresentationComponent)
  participantsRef: ParticipatingUsersRepresentationComponent;

  @Input() chatId: string;
  @Input() businessCaseId: string;
  @Input() disableSendMessages = false;
  @Input() chatType: ChatTab;
  @Input() chat: Chat;
  @Input() isChatMuted: boolean;

  @Input() usersById: Dictionary<User>;

  @Output() topicChatEdited = new EventEmitter();
  @Output() allMessagesRead = new EventEmitter();

  messages: MessageViewModel[];
  messagesGrouped: {
    groupName: string;
    messages: Record<string, MessageViewModel[]>;
  }[];
  _attachmentsInfo: AttachmentsInfo = {};

  sendingMessageInProgress = false;
  isSocketConnectionInitialized: boolean;

  userCustomerKey: string;
  customerNamesByKey: Dictionary<Customer>;
  businessCase: ExchangeBusinessCase;

  chatSuggestions: { id: string; name: string }[];

  userToken: UserToken;

  unreadMessagesCount: number = null;
  page = 0;
  readonly MESSAGES_TO_LOAD_LIMIT = 100;

  hasViewLoaded = false;
  canArchiveChat: boolean;

  canManageDocumentInbox$ = this.store.select(selectAccessRights).pipe(
    filter((a) => !!a),
    map((a) => a.dataRoom.write),
  );

  oldestAndNewestMessageOfChat$ = this.store.select(selectChatHistoryRange);
  constructor(
    private destroyRef: DestroyRef,
    @Inject(LOCALE_ID) private locale: Locale,
    private regionalSettings: RegionalSettingsService,
    private documentService: DocumentControllerService,
    private inboxDocumentService: InboxDocumentControllerService,
    private finToastService: FinToastService,
    private store: Store<AppState>,
    private chatMessageManagementController: ChatMessageManagementControllerService,
    private chatManagementController: ChatManagementControllerService,
    private socketService: SocketService,
    private tabNavigationService: TabNavigationService,
    private chatNotificationControllerService: ChatNotificationStatusControllerService,
    private dateService: DateService,
    private modalService: ModalService,
  ) {}

  get attachmentsInfo() {
    return this._attachmentsInfo;
  }

  set attachmentsInfo(value: AttachmentsInfo) {
    this._attachmentsInfo = {
      ...this._attachmentsInfo,
      ...value,
    };
  }

  get areThereParticipants(): boolean {
    return this.chat?.chatUsers?.length > 0;
  }

  get showChatHeader() {
    return this.chat?.chatType;
  }

  readonly businessCasePermission = BusinessCasePermission;

  ngOnInit() {
    this.store
      .select(selectUserToken)
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((token) => {
        this.userToken = token;
        this.handleChatParticipantsTagSuggestions();
      });
    if (this.businessCaseId) {
      this.store
        .select(selectExistingChats)
        .pipe(
          filter((chats) => chats.length > 0),
          switchMap((existingChats) => {
            const messageStreams = existingChats.map((chat: Chat) => {
              const destination = `${CHAT_SOCKET_RECEIVE_MESSAGE_DESTINATION}-${chat.id}`;
              return this.socketService.getMessagesByDestination$(
                destination,
                SocketType.CHAT,
              );
            });
            return merge(...messageStreams);
          }),
          takeUntilDestroyed(this.destroyRef),
        )
        .subscribe((message: ChatMessage) => {
          if (
            message.userId === this.userCustomerKey ||
            this.chatId !== message.chatId
          ) {
            return;
          }
          this.markMessageAsRead(message);
          const messageViewModel = this.mapToMessageViewModel(message);
          this.messages.push(messageViewModel);
          this.groupMessagesByDate();
          this.loadDocumentInfo([messageViewModel]);
          this.refreshChatIfNewUser(message.userId);
          this.scrollToBottom();

          setTimeoutUnpatched(() => {
            this.store.dispatch(
              StateLibNotificationsPageActions.markChatNotificationsAsRead({
                chatId: this.chatId,
              }),
            );
          }, 1000);
        });
    }
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.chat && this.chat) {
      this.getOldestMessage(this.businessCaseId, this.chatId);
      if (this.userToken?.sub) {
        this.handleChatParticipantsTagSuggestions();
      }
    }

    if (changes.chatId && this.chatId) {
      const timeout = !Object.keys(this.usersById)?.length ? 100 : 0;

      setTimeout(() => {
        this.page = 0;
        this.loadMessages();
      }, timeout);
    }
  }

  trackByMessageId(i: number, message: MessageViewModel) {
    return `${message.dto.id}${message.dto.readByUsers
      ?.map((u) => u.userId)
      .join('')}`;
  }

  markAllMessagesAsRead() {
    if (!this.hasViewLoaded || !this.messages?.length) {
      return;
    }

    this.chatNotificationControllerService
      .readAllForChat({
        userId: this.userToken?.sub,
        chatId: this.chatId,
        businessCaseId: this.businessCaseId,
      })
      .subscribe(() => {
        this.store.dispatch(
          StateLibNotificationsPageActions.markChatNotificationsAsRead({
            chatId: this.chatId,
          }),
        );
        setTimeout(() => {
          this.allMessagesRead.emit();
          this.unreadMessagesCount = 0;
          this.scrollToBottom();
        }, 2000);

        this.messages.forEach((m) => {
          const isReadByUser = this.isMessageReadByUser(m.dto);
          if (!isReadByUser) {
            this.markMessageAsRead(m.dto, false);
          }
        });
      });
  }

  private isMessageReadByUser(message: ChatMessage) {
    return message.readByUsers?.some((u) => u.userId === this.userToken?.sub);
  }

  private markMessageAsRead(message: ChatMessage, markAsReadInDB = true) {
    message.readByUsers?.push({
      id: this.userToken.sub,
      userId: this.userToken.sub,
    });

    if (markAsReadInDB) {
      this.chatNotificationControllerService
        .readByUser({
          userId: this.userToken.sub,
          messageId: message.id,
          businessCaseId: this.businessCaseId,
        })
        .subscribe();
    }
  }

  private loadMessages() {
    this.hasViewLoaded = false;

    this.store
      .select(selectUserId)
      .pipe(
        switchMap((userId: string) => {
          return this.chatNotificationControllerService.getUnreadMessagesCount({
            chatId: this.chatId,
            userId,
            businessCaseId: this.businessCaseId,
          });
        }),
        switchMap((unreadMessagesCount) => {
          if (!this.chatId) {
            return of();
          }
          this.unreadMessagesCount = unreadMessagesCount;
          const limit =
            unreadMessagesCount > 0 &&
            unreadMessagesCount <= this.MESSAGES_TO_LOAD_LIMIT
              ? this.MESSAGES_TO_LOAD_LIMIT
              : this.MESSAGES_TO_LOAD_LIMIT + unreadMessagesCount;
          return this.chatMessageManagementController.getAllChatMessages({
            chatId: this.chatId,
            offset: this.page,
            limit,
            businessCaseId: this.businessCaseId,
          });
        }),
        withLatestFrom(
          this.store.select(selectCustomerNamesByKey),
          this.store.select(selectBusinessCase),
          this.store.select(selectUserCustomerKey),
        ),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe(
        ([messages, customerNamesByKey, businessCase, customerKey]) => {
          this.userCustomerKey = customerKey;
          this.customerNamesByKey = customerNamesByKey;
          this.businessCase = businessCase;

          if (Object.keys(this.customerNamesByKey).length) {
            const unreadMarkerIndex =
              messages.numHits - this.unreadMessagesCount;
            this.messages =
              messages.hits
                .reverse()
                .map((message, index) =>
                  this.mapToMessageViewModel(
                    message,
                    unreadMarkerIndex === index,
                  ),
                ) || [];
          }

          this.canArchiveChat =
            this.chat?.customerKey === this.userCustomerKey &&
            this.chat.status !== 'ARCHIVED';

          this.groupMessagesByDate();
          this.loadDocumentInfo(this.messages);
          this.scrollToBottom();
          setTimeout(() => {
            // setTimeout because when we load messages it scrolls from top to bottom and triggers appInsideView directive
            this.hasViewLoaded = true;
            if (this.unreadMessagesCount > 0) {
              this.markAllMessagesAsRead();
            }
          }, 1000);
          this.page++;
        },
      );
  }

  loadOlderMessages() {
    this.chatMessageManagementController
      .getAllChatMessages({
        chatId: this.chatId,
        offset: this.page,
        limit: this.MESSAGES_TO_LOAD_LIMIT,
        businessCaseId: this.businessCaseId,
      })
      .subscribe((messages) => {
        if (!messages?.hits.length) {
          return;
        }

        const olderMessages =
          messages.hits
            .reverse()
            .map((message) => this.mapToMessageViewModel(message)) || [];
        this.loadDocumentInfo(olderMessages);
        this.messages.unshift(...olderMessages);
        this.groupMessagesByDate();

        this.page++;
      });
  }

  private groupMessagesByDate() {
    const groupedByDate = Object.entries(
      groupBy(this.messages, (m) => this.getGroupingDate(m.dto.timestamp)),
    ).map((entry) => {
      const [groupName, messages] = entry;
      return {
        groupName,
        messages,
      };
    });

    this.messagesGrouped = groupedByDate.map((g) => {
      let groupId: string;
      let lastMessage: ChatMessage;
      const userMessagesGrouped = g.messages.reduce(
        (acc, message, index) => {
          const isBySameUser = message.dto.userId === lastMessage?.userId;
          const isIntervalLessThanFiveMinutes =
            message.dto.timestamp - lastMessage?.timestamp <=
            FIVE_MINUTES_TIMESTAMP;
          if (isBySameUser && isIntervalLessThanFiveMinutes) {
            acc[groupId].push(message);
          } else {
            groupId = `${index}`;
            acc[groupId] = [message];
          }

          lastMessage = message.dto;

          return acc;
        },
        {} as Record<string, MessageViewModel[]>,
      );

      return {
        groupName: g.groupName,
        messages: userMessagesGrouped,
      };
    });
  }

  disableSort() {
    return 0;
  }

  private handleChatParticipantsTagSuggestions() {
    this.chatSuggestions =
      this.chat?.chatUsers
        ?.filter((u) => u.userId !== this.userToken?.sub)
        .map((u) => {
          return {
            id: u.userId,
            name: `${u.firstName} ${u.lastName}`,
          };
        }) || [];
  }

  private scrollToBottom() {
    setTimeout(() => {
      this.infiniteScroll?.scrollTo(ScrollPosition.BOTTOM);
      this.sendingMessageInProgress = false;
      this.getOldestMessage(this.businessCaseId, this.chatId);
    }, 0);
  }

  editTopicChat() {
    this.topicChatEdited.emit();
  }

  manageChatNotifications(shouldMute: boolean) {
    this.chatNotificationControllerService
      .changeUserNotificationStatusForGivenChat({
        userId: this.userToken.sub,
        isMuted: shouldMute,
        chatId: this.chatId,
        businessCaseId: this.businessCaseId,
      })
      .subscribe({
        next: () => {
          shouldMute
            ? this.store.dispatch(
                StateLibChatPageActions.addMutedChat({ payload: this.chatId }),
              )
            : this.store.dispatch(
                StateLibChatPageActions.removeMutedChat({
                  payload: this.chatId,
                }),
              );
          this.isChatMuted = shouldMute;
          this.finToastService.show(Toast.success());
        },
      });
  }

  archiveChat() {
    this.chatManagementController
      .archiveChat({
        chatId: this.chatId,
        businessCaseId: this.businessCaseId,
      })
      .subscribe({
        next: (chat) => {
          this.store.dispatch(
            StateLibChatPageActions.updateChat({ payload: chat }),
          );
          this.finToastService.show(Toast.success());
          this.tabNavigationService.addTabFragmentToURL(
            this.userCustomerKey,
            this.businessCaseId,
            'business-case',
          );
          this.canArchiveChat = false;
        },
      });
  }

  onMessageSent() {
    this.sendingMessageInProgress = true;
    this.refreshChatIfNewUser(this.userToken?.sub);
  }

  private refreshChatIfNewUser(userId: string) {
    if (!this.chat?.chatUsers?.map((c) => c.userId).includes(userId)) {
      this.store.dispatch(
        StateLibChatPageActions.refreshChat({ payload: this.chat?.id }),
      );
    }
  }

  private getTypeOfCustomerUser(
    customerKey: string,
    businessCase: ExchangeBusinessCase,
  ) {
    if (
      !businessCase.participants.map((p) => p.customerKey).includes(customerKey)
    ) {
      return '';
    }

    return customerKey === businessCase.leadCustomerKey
      ? $localize`:@@dashboard.businessCase.header.caseHolder:Fallinhaber`
      : $localize`:@@chat.window.customerKey.partner:Partner`;
  }

  private mapToMessageViewModel(message: ChatMessage, unreadMarker?: boolean) {
    return {
      dto: message,
      customerName: this.customerNamesByKey[message.customerKey]?.name,
      typeOfCustomerUser: this.getTypeOfCustomerUser(
        message.customerKey,
        this.businessCase,
      ),
      orientation: this.getChatOrientation(message),
      unreadMarker,
    } as MessageViewModel;
  }

  private getChatOrientation(message: ChatMessage) {
    if (this.chat?.chatType.toString().includes(ChatType.INTERNAL)) {
      return message.userId === this.userToken?.sub ? 'right' : 'left';
    }

    return message.customerKey === this.userCustomerKey ? 'right' : 'left';
  }

  handleFile(file: File) {
    this.store.dispatch(
      StateLibChatPageActions.initiateFileUpload({
        file: file,
      }),
    );
  }

  onSendToInbox(attachment: ChatAttachment) {
    this.inboxDocumentService
      .moveCommunicationDocumentToInboxBucket({
        communicationDocumentId: attachment.documentId,
        businessCaseId: this.businessCaseId,
      })
      .subscribe({
        next: () => {
          this.attachmentsInfo = {
            [attachment.documentId]: {
              documentType: 'INBOX',
              missing: false,
              sentToInbox: 'success',
            },
          };
        },
        error: () => {
          const attachementInfo = this.attachmentsInfo[attachment.documentId];
          this.attachmentsInfo = {
            [attachment.documentId]: {
              ...attachementInfo,
              sentToInbox: 'error',
            },
          };
        },
      });
  }

  private getGroupingDate(timestamp: number) {
    return `${this.dateService.getWeekday(timestamp)}, ${formatDate(
      this.dateService.timestampToDate(timestamp),
      this.regionalSettings.dateFormat,
      this.locale,
    )}`;
  }

  private loadDocumentInfo(messages: MessageViewModel[]) {
    if (!messages) {
      return;
    }
    /* TODO - download document chat */
    for (const message of messages) {
      for (const attachment of message.dto.attachments) {
        this.documentService
          .getDocument({
            documentId: attachment.documentId,
            includeContent: false,
          })
          .subscribe({
            next: (res: DocumentEntity) => {
              this.attachmentsInfo = {
                [attachment.documentId]: {
                  documentType: res.ownerReference.documentType,
                  missing: false,
                },
              };
            },
            error: () => {
              this.attachmentsInfo = {
                [attachment.documentId]: {
                  documentType: null,
                  missing: true,
                },
              };
            },
          });
      }
    }
  }

  openParticipantsModal(): void {
    this.participantsRef.openUsersModal();
  }

  exportChatAsPdf(data: {
    newestChatMessageDateTime: string;
    oldestChatMessageDateTime: string;
  }) {
    const newestChatMessageDateTime =
      this.dateService.getFormattedTimeNgbDateStruct(
        data.newestChatMessageDateTime,
      );
    const oldestChatMessageDateTime =
      this.dateService.getFormattedTimeNgbDateStruct(
        data.oldestChatMessageDateTime,
      );

    this.modalService.openComponent(DownloadChatModalComponent, {
      minAllowedDateInput: oldestChatMessageDateTime,
      maxAllowedDateInput: newestChatMessageDateTime,
      locale: this.locale,
    });
  }

  getOldestMessage(businessCaseId: string, chatId: string) {
    this.store.dispatch(
      StateLibChatHistoryRangePageActions.getOldestAndNewestMessageOfChat({
        payload: { businessCaseId, chatId },
      }),
    );
  }
}
