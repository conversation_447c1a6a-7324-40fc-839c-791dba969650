import { Component, DestroyRef, OnInit, ViewChild } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormControl } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import {
  DataRoomGroupsFieldsService,
  GroupTemplateFields,
} from '@fincloud/core/business-case';
import { HotkeyService } from '@fincloud/core/hotkeys';
import { ScrollCommunicationService } from '@fincloud/core/scroll';
import { setTimeoutUnpatched } from '@fincloud/core/utils';
import { BusinessCaseLayoutService } from '@fincloud/neoshare/business-case';
import { DataRoomBaseFunctionalityProvider } from '@fincloud/neoshare/data-room';
import { FOLDER_CONTENT_BASE_SERVICE_TOKEN } from '@fincloud/neoshare/folder-structure';
import {
  selectBusinessCase,
  selectBusinessCaseContentHeightCompanyAttachedDataRoom,
  selectBusinessCaseDataRoomTab,
  selectBusinessCaseSidePanelHeightDefault,
  selectBusinessCaseWrapperHeightCompanyAttachedDataRoom,
  selectBusinessCaseWrapperHeightDefault,
  selectHighlighted,
  selectIsLead,
} from '@fincloud/state/business-case';
import {
  StateLibSideNavigationsPageActions,
  sideNavigationsFeature,
} from '@fincloud/state/side-navigations';
import { FieldDto } from '@fincloud/swagger-generator/business-case-manager';
import { Information } from '@fincloud/swagger-generator/company';
import { CadrTemplate, Company } from '@fincloud/swagger-generator/exchange';
import { FolderStructureContext } from '@fincloud/types/enums';
import { AppState } from '@fincloud/types/models';
import { FinButtonAppearance, FinButtonShape } from '@fincloud/ui/button';
import { FinEmptyStateType } from '@fincloud/ui/empty-state';
import { FinScrollbarComponent } from '@fincloud/ui/scrollbar';
import { FinSize } from '@fincloud/ui/types';
import { FinWarningMessageAppearance } from '@fincloud/ui/warning-message';
import { SCROLL_TO_DELAY } from '@fincloud/utils';
import { Store } from '@ngrx/store';
import { isEqual, keyBy } from 'lodash-es';
import {
  asapScheduler,
  distinctUntilChanged,
  filter,
  shareReplay,
  subscribeOn,
  tap,
} from 'rxjs';
import { selectBusinessCaseCADRTemplateFieldData } from '../../+state/selectors/business-case-cadr.selectors';
import { BusinessCaseCADRFolderContentBaseService } from '../../services/business-case-cadr-folder-content-base.service';

@Component({
  selector: 'app-business-case-cadr',
  templateUrl: './business-case-cadr.component.html',
  styleUrls: ['./business-case-cadr.component.scss'],
  providers: [
    {
      provide: FOLDER_CONTENT_BASE_SERVICE_TOKEN,
      useClass: BusinessCaseCADRFolderContentBaseService,
    },
  ],
})
export class BusinessCaseCadrComponent
  extends DataRoomBaseFunctionalityProvider
  implements OnInit
{
  @ViewChild(FinScrollbarComponent) sidebarScroll?: FinScrollbarComponent;

  readonly finSize = FinSize;
  readonly finWarningMessageAppearance = FinWarningMessageAppearance;
  readonly finButtonAppearance = FinButtonAppearance;
  readonly folderStructureContext = FolderStructureContext;
  readonly finButtonShape = FinButtonShape;
  readonly finEmptyStateType = FinEmptyStateType;

  readonly emptyParticipantCadrMessage = $localize`:@@businessCaseCadr.text:Der unternehmensbezogene Data Room wurde zum Finanzierungsfall
        hinzugefügt, allerdings sind noch keine Felder im unternehmensbezogenen
        Data Room vorhanden. Sobald Felder zu diesem hinzugefügt werden, werden
        diese hier angezeigt.`;

  searchControl = new FormControl('');
  businessCaseDataRoomTab$ = this.store.select(selectBusinessCaseDataRoomTab);
  isCustomerLeadPartner$ = this.store
    .select(selectIsLead)
    .pipe(shareReplay({ refCount: true, bufferSize: 1 }));

  highlighted$ = this.store
    .select(selectHighlighted)
    .pipe(shareReplay({ bufferSize: 1, refCount: true }));

  templateFieldData$ = this.store
    .select(selectBusinessCaseCADRTemplateFieldData)
    .pipe(shareReplay({ bufferSize: 1, refCount: true }));

  navigationsAreOpen$ = this.store.select(
    sideNavigationsFeature.selectNavigationAndChatAreOpen,
  );

  company: Company;

  get hasResultAfterFiltering() {
    return (
      this.searchControl.value.length > 0 && !this.groupsTemplateFields.length
    );
  }

  readonly sidePanelHeight$ = this.store.select(
    selectBusinessCaseSidePanelHeightDefault,
  );

  readonly contentWrapperHeight$ = this.store.select(
    selectBusinessCaseWrapperHeightCompanyAttachedDataRoom,
  );

  readonly contentHeight$ = this.store.select(
    selectBusinessCaseContentHeightCompanyAttachedDataRoom,
  );

  readonly emptyContentHeight$ = this.store.select(
    selectBusinessCaseWrapperHeightDefault,
  );

  constructor(
    public destroyRef: DestroyRef,
    private groupsFieldsService: DataRoomGroupsFieldsService,
    store: Store<AppState>,
    scrollCommunicationService: ScrollCommunicationService,
    hotkeysService: HotkeyService,
    private router: Router,
    private route: ActivatedRoute,
    private businessCaseLayoutService: BusinessCaseLayoutService,
  ) {
    super(destroyRef, store, scrollCommunicationService, hotkeysService);
  }

  ngOnInit(): void {
    this.store
      .select(selectBusinessCase)
      .pipe(filter((c) => !!c))
      .pipe(
        tap((businessCase) => {
          if (businessCase?.isCADRLinked === false) {
            this.router.navigate(['data-room', 'case'], {
              relativeTo: this.route,
            });
          }
        }),
      )
      .pipe(
        distinctUntilChanged((prev, curr) =>
          isEqual(prev.company, curr.company),
        ),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe((businessCase) => {
        this.company = businessCase.company;

        this.reinitializeGroups(
          this.company?.companyTemplate?.template as CadrTemplate,
          this.company?.information as Information[],
        );
      });

    this.listenForSearchChange();

    this.highlighted$
      .pipe(
        filter(({ key }) => !!key),
        subscribeOn(asapScheduler, SCROLL_TO_DELAY),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe(({ key }) => {
        // Makes sure the DOM is updated so that the element could be found
        setTimeoutUnpatched(
          () => this.scrollCommunicationService.scrollToElementByClass(key),
          0,
        );
      });
  }

  private reinitializeGroups(
    template: CadrTemplate,
    informations: Information[],
  ) {
    const information = keyBy(informations || [], 'key');
    this.groupsTemplateFields =
      this.groupsFieldsService.getGroupsTemplateFields(
        (template?.fields || []) as FieldDto[],
        template?.groupsOrdered || [],
        information,
        null,
      );
    this.groupsTemplateFieldsInitial = structuredClone(
      this.groupsTemplateFields,
    );
  }

  internationalizationForDocument(length: number) {
    if (length > 1) {
      return $localize`:@@companyAnalysis.companyInformation.navLink.documents:Dokumente`;
    } else {
      return $localize`:@@dataRoom.draggableFields.document:Dokument`;
    }
  }

  // eslint-disable-next-line @typescript-eslint/no-empty-function
  openManageTableModal(_group: GroupTemplateFields): void {}
  // eslint-disable-next-line @typescript-eslint/no-empty-function
  openManageFieldModal(_field: FieldDto, _group: GroupTemplateFields): void {}
  // eslint-disable-next-line @typescript-eslint/no-empty-function
  openManageFolderModal(_group: GroupTemplateFields): void {}

  toggleNavigation(navigation: {
    isChatVisible: boolean;
    platformNavigationIsOpen: boolean;
  }) {
    if (navigation.isChatVisible && navigation.platformNavigationIsOpen) {
      this.store.dispatch(
        StateLibSideNavigationsPageActions.toggleOpenForPlatformNavigation(),
      );
    }
    this.store.dispatch(
      StateLibSideNavigationsPageActions.toggleOpenForPageNavigation(),
    );
  }

  redirectToCompanyInformationEditMode(
    customerKey: string,
    companyId: string,
  ): void {
    void this.router.navigate(
      [customerKey, 'company-analysis', companyId, 'data-room'],
      {
        queryParams: { editMode: true },
      },
    );
  }
}
