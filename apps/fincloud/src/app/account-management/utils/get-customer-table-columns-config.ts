import { FluidTableColumn, getComparator } from '@fincloud/components/lists';
import { CustomerTableRow } from '../models/customer-table-row';

export const getCustomerTableColumnsConfig: (
  isDemoEnvironment: boolean,
) => FluidTableColumn[] = (isDemoEnvironment: boolean) => {
  const columns = [
    {
      name: $localize`:@@customerTable.columns.customerKey:Kundenschlüssel`,
      prop: 'customerKey',
      id: 'customerKey',
      width: 150,
      flexGrow: 1,
      filterable: true,
      sortable: true,
      cellClass: 'gray',
      isCustomCellTemplate: true,
    },
    {
      name: $localize`:@@customerTable.columns.customerType:Kundentyp`,
      prop: 'customerType',
      id: 'customerType',
      width: 100,
      flexGrow: 0.8,
      filterable: true,
      sortable: true,
      isCustomCellTemplate: true,
    },
    {
      name: $localize`:@@customerTable.columns.name:Kundenname`,
      prop: 'name',
      id: 'name',
      width: 120,
      flexGrow: 1.2,
      filterable: true,
      sortable: true,
      isCustomCellTemplate: true,
    },
    {
      name: 'BIC',
      prop: 'bic',
      id: 'bic',
      width: 100,
      flexGrow: 0.7,
      filterable: true,
      sortable: true,
      isCustomCellTemplate: true,
    },
    {
      name: $localize`:@@contract.table.list.header.createdBy:Erstellt von`,
      prop: 'createdByName',
      id: 'createdByName',
      width: 100,
      flexGrow: 1,
      filterable: true,
      isCustomCellTemplate: true,
    },
    {
      name: $localize`:@@customerTable.columns.createdOn:Erstellt am`,
      prop: 'creationDate',
      id: 'creationDate',
      width: 120,
      flexGrow: 0.6,
      filterable: true,
      sortable: true,
      isCustomCellTemplate: true,
      comparator: getComparator<CustomerTableRow>((r) => r.createdOn),
    },
    {
      isCustomCellTemplate: true,
      name: $localize`:@@dashboard.businessCase.actions:Aktionen`,
      prop: 'actions',
      id: 'actions',
      width: 95,
      flexGrow: 0.45,
      filterable: false,
      sortable: false,
      headerClass: 'align-right-header',
      cellClass: 'align-right',
    },
    // ALPHA-5337 - hide
    // {
    //   name: 'Status',
    //   isCustomCellTemplate: true,
    //   id: 'status',
    //   width: 250,
    //   flexGrow: 0.6,
    //   filterable: true,
    //   sortable: true,
    //   comparator: getComparator<CustomerTableRow>((r) => r.status),
    // },
  ];

  if (isDemoEnvironment) {
    columns.splice(
      columns.length - 1,
      0,
      {
        isCustomCellTemplate: false,
        name: $localize`:@@customerTable.columns.source:Quelle`,
        prop: 'source',
        id: 'source',
        width: 100,
        flexGrow: 0.7,
        filterable: true,
        sortable: false,
      },
      {
        isCustomCellTemplate: true,
        name: $localize`:@@customerTable.columns.sourceType:Quellentyp`,
        prop: 'sourceType',
        id: 'sourceType',
        width: 150,
        flexGrow: 1,
        filterable: true,
        sortable: false,
      },
    );
  }

  return columns;
};
