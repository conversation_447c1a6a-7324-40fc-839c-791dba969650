import { FluidTableColumn, getComparator } from '@fincloud/components/lists';
import { UserTableRow } from '../models/user-table-row';

export const getUserTableColumnsConfig: (
  isGuestCustomer: boolean,
) => FluidTableColumn[] = (isGuestCustomer) => {
  const actionsColumnConfig = {
    isCustomCellTemplate: true,
    name: $localize`:@@dashboard.businessCase.actions:Aktionen`,
    prop: 'Actions',
    id: 'actions',
    width: 95,
    flexGrow: 0.5,
    filterable: false,
    sortable: false,
    headerClass: 'align-right-header',
    cellClass: 'align-right',
  };

  const columns = [
    {
      name: $localize`:@@userDataForm.email:E-Mail-Adresse`,
      prop: 'username',
      id: 'username',
      width: 200,
      flexGrow: 1.5,
      filterable: true,
      sortable: true,
      isCustomCellTemplate: true,
    },
    {
      name: $localize`:@@userTable.columns.name:Name`,
      prop: 'firstName',
      id: 'firstName',
      width: 200,
      flexGrow: 1.5,
      filterable: true,
      sortable: true,
      isCustomCellTemplate: true,
    },
    {
      name: $localize`:@@firmenkunden.companyEdit.partner.info5.dropdown:Rolle`,
      isCustomCellTemplate: true,
      prop: 'userRoles',
      id: 'userRoles',
      width: 200,
      flexGrow: 1.5,
      filterable: true,
    },
    {
      name: $localize`:@@contract.table.list.header.createdBy:Erstellt von`,
      prop: 'createdByName',
      id: 'createdByName',
      width: 200,
      flexGrow: 1,
      filterable: true,
      isCustomCellTemplate: true,
    },
    {
      name: $localize`:@@userTable.columns.createdOn:Erstellt am`,
      prop: 'creationDate',
      id: 'creationDate',
      width: 150,
      flexGrow: 1,
      filterable: true,
      sortable: true,
      isCustomCellTemplate: true,
      comparator: getComparator<UserTableRow>((r) => r.timestamp),
    },
  ];

  if (!isGuestCustomer) {
    columns.push(actionsColumnConfig);
  }

  return columns;
};
