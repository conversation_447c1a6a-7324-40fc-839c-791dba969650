import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';

import { NgxSliderModule } from '@angular-slider/ngx-slider';
import { DragDropModule } from '@angular/cdk/drag-drop';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { AvatarComponent } from '@fincloud/components/avatar';
import { NsUiBooleansModule } from '@fincloud/components/booleans';
import { NsUiButtonsModule } from '@fincloud/components/buttons';
import { NsUiGroupVisibilityStatusInfoModule } from '@fincloud/components/group-visibility-status-info';
import { NsUiHintModule } from '@fincloud/components/hint';
import { NsUiIconsModule } from '@fincloud/components/icons';
import { NsUiOrganizationLogoRendererModule } from '@fincloud/components/organization-logo-renderer';
import { NsUiNgxModule } from '@fincloud/components/third-party-modules';
import { NsCoreDateModule } from '@fincloud/core/date';
import { InitialsPipe, NsCorePipesModule } from '@fincloud/core/pipes';
import { NsBusinessCaseRefactoringModule } from '@fincloud/neoshare/business-case';
import { NsDataRoomModule } from '@fincloud/neoshare/data-room';
import { NsDocumentModule } from '@fincloud/neoshare/document';
import { NsNeogptChatModule } from '@fincloud/neoshare/neogpt-chat';
import { NsTemplateFieldModule } from '@fincloud/neoshare/template-field';
import { FinButtonModule } from '@fincloud/ui/button';
import { FinCheckboxModule } from '@fincloud/ui/checkbox';
import { FinExpansionPanelModule } from '@fincloud/ui/expansion-panel';
import { FinIconModule } from '@fincloud/ui/icon';
import { FinModalModule } from '@fincloud/ui/modal';
import { FinScrollbarModule } from '@fincloud/ui/scrollbar';
import { FinSlideToggleModule } from '@fincloud/ui/slide-toggle';
import { FinTruncateTextModule } from '@fincloud/ui/truncate-text';
import { FinWarningMessageModule } from '@fincloud/ui/warning-message';
import { NgxPermissionsModule } from 'ngx-permissions';
import { NgScrollbarModule } from 'ngx-scrollbar';
import { BusinessCaseCadrModule } from '../business-case-cadr/business-case-cadr.module';
import { BusinessCaseTeaserRoutingModule } from './business-case-teaser-routing.module';
import { TeaserButtonComponent } from './components/teaser-button/teaser-button.component';
import { TeaserConfigurationModalComponent } from './components/teaser-configuration-modal/teaser-configuration-modal.component';
import { TeaserDownloadComponent } from './components/teaser-download/teaser-download.component';

@NgModule({
  declarations: [
    TeaserDownloadComponent,
    TeaserConfigurationModalComponent,
    TeaserButtonComponent,
  ],
  imports: [
    CommonModule,
    FormsModule,
    NsUiHintModule,
    NsUiOrganizationLogoRendererModule,
    DragDropModule,
    NsUiIconsModule,
    NgScrollbarModule,
    ReactiveFormsModule,
    NsBusinessCaseRefactoringModule,
    NgxSliderModule,
    AvatarComponent,
    NsDataRoomModule,
    NsDocumentModule,
    NsTemplateFieldModule,
    BusinessCaseCadrModule,
    NgxPermissionsModule.forChild(),
    NsNeogptChatModule,
    BusinessCaseTeaserRoutingModule,
    NsCoreDateModule,
    NsCorePipesModule,
    FinIconModule,
    NsUiIconsModule,
    NsUiNgxModule,
    NsUiBooleansModule,
    NsUiGroupVisibilityStatusInfoModule,
    NsUiButtonsModule,
    FinModalModule,
    FinButtonModule,
    FinWarningMessageModule,
    FinSlideToggleModule,
    FinExpansionPanelModule,
    FinCheckboxModule,
    FinScrollbarModule,
    FinTruncateTextModule,
  ],
  providers: [InitialsPipe],
  exports: [TeaserButtonComponent, TeaserDownloadComponent],
})
export class BusinessCaseTeaserModule {}
