import { AbstractControl, ValidationErrors, ValidatorFn } from '@angular/forms';
import { BusinessCaseInboxDocument } from '@fincloud/types/models';

export function duplicateValidator(
  document: BusinessCaseInboxDocument,
): ValidatorFn {
  return (control: AbstractControl): ValidationErrors | null => {
    if (document.hasError && !document.isWarningIgnored) {
      return { duplicate: true };
    }
    return null;
  };
}
