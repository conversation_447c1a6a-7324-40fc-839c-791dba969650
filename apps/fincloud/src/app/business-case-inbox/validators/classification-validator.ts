import { AbstractControl, ValidationErrors, ValidatorFn } from '@angular/forms';
import { BusinessCaseInboxDocument } from '@fincloud/types/models';

export function classificationValidator(
  document: BusinessCaseInboxDocument,
): ValidatorFn {
  return (control: AbstractControl): ValidationErrors | null => {
    if (document.hasClassificationError) {
      return { classification: true };
    }
    return null;
  };
}
