import { inject } from '@angular/core';
import { CanDeactivateFn } from '@angular/router';
import { Store } from '@ngrx/store';
import { BusinessCaseInboxPageActions } from '../+state';

export const closeDocumentClassificationSocketConnectionGuard: CanDeactivateFn<
  unknown
> = () => {
  const store = inject(Store);

  store.dispatch(
    BusinessCaseInboxPageActions.closeDocumentClassificationSocketConnection(),
  );

  return true;
};
