import { Overlay, OverlayRef } from '@angular/cdk/overlay';
import { ComponentPortal, ComponentType } from '@angular/cdk/portal';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class FullScreenOverlayService {
  private overlayRef: OverlayRef | null = null;

  constructor(private overlay: Overlay) {}

  openFullscreen<T>(component: ComponentType<T>): OverlayRef {
    this.closeOverlay();

    this.overlayRef = this.overlay.create({
      hasBackdrop: false,
      positionStrategy: this.overlay
        .position()
        .global()
        .top('0')
        .left('0')
        .width('100vw')
        .height('100vh'),
      scrollStrategy: this.overlay.scrollStrategies.block(),
      panelClass: 'fullscreen-overlay',
    });

    const portal = new ComponentPortal(component, null);
    this.overlayRef.attach(portal);
    return this.overlayRef;
  }

  closeOverlay(): void {
    if (this.overlayRef) {
      this.overlayRef.dispose();
      this.overlayRef = null;
    }
  }
}
