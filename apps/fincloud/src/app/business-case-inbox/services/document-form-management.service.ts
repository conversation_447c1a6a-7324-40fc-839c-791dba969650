import { Injectable } from '@angular/core';
import {
  Form<PERSON>uilder,
  FormControl,
  FormGroup,
  Validators,
} from '@angular/forms';
import {
  FORBIDDEN_CHARACTERS_REGEX,
  doesNotEndWith,
} from '@fincloud/core/form';
import { BusinessCaseInboxDocument } from '@fincloud/types/models';
import { FinCheckboxComponent } from '@fincloud/ui/checkbox';
import { FORM_CONTROL_SUFFIX } from '../utils/form-control-suffix';
import { classificationValidator } from '../validators/classification-validator';
import { duplicateValidator } from '../validators/duplicate-validator';

@Injectable()
export class DocumentFormManagementService {
  readonly formControlSuffix = FORM_CONTROL_SUFFIX;
  readonly documentDoesNotEndWith = '.';

  constructor(private fb: FormBuilder) {}

  removeObsoleteControls(
    form: FormGroup,
    documents: BusinessCaseInboxDocument[],
  ): boolean {
    const documentIds = documents.map((doc) => doc.id || doc.uploadId);
    const existingControlNames = Object.keys(form.controls).filter(
      (controlName) => !controlName.includes(this.formControlSuffix),
    );

    let removedAnyControls = false;

    existingControlNames.forEach((controlName) => {
      if (!documentIds.includes(controlName)) {
        form.removeControl(controlName, { emitEvent: false });
        form.removeControl(`${controlName}${this.formControlSuffix}`, {
          emitEvent: false,
        });
        removedAnyControls = true;
      }
    });

    return removedAnyControls;
  }

  addNewControl(
    form: FormGroup,
    document: BusinessCaseInboxDocument,
  ): FormControl | null {
    const documentId = document.id || document.uploadId;

    if (form.get(documentId)) {
      return null;
    }

    const selectionControl = this.fb.control(false);
    const classificationControl = this.fb.control(
      document.contentReference?.fileName || document.fileName,
    );

    form.addControl(documentId, selectionControl, { emitEvent: false });
    form.addControl(
      `${documentId}${this.formControlSuffix}`,
      classificationControl,
      { emitEvent: false },
    );

    return selectionControl;
  }

  checkValidationControl(
    form: FormGroup,
    document: BusinessCaseInboxDocument,
  ): void {
    if (!document.id) {
      return;
    }

    const selectionControl = form.get(document.id);
    const classificationControl = form.get(
      `${document.id}${this.formControlSuffix}`,
    );

    if (document.hasClassificationError) {
      selectionControl?.disable({ emitEvent: false });
    } else {
      selectionControl?.enable({ emitEvent: false });
    }

    if (classificationControl) {
      classificationControl.setErrors(null);

      classificationControl.setValidators([
        Validators.required,
        duplicateValidator(document),
        classificationValidator(document),
        Validators.pattern(FORBIDDEN_CHARACTERS_REGEX),
        doesNotEndWith(this.documentDoesNotEndWith),
      ]);

      classificationControl.updateValueAndValidity();
    }

    if (
      classificationControl?.hasError('duplicate') ||
      classificationControl?.hasError('classification')
    ) {
      classificationControl.markAsTouched();
    }
  }

  resetSelectionControlAfterManualMove(
    form: FormGroup,
    document: BusinessCaseInboxDocument,
  ): void {
    if (!document.manuallySetFolderId) {
      return;
    }

    const selectionControl = form.get(document.id);
    if (selectionControl && selectionControl.value) {
      selectionControl.setValue(false, { emitEvent: false });
    }
  }

  resetSelectionControlAfterRegenerate(
    form: FormGroup,
    document: BusinessCaseInboxDocument,
  ): boolean {
    const selectionControl = form.get(document.id);
    if (
      !selectionControl ||
      (!document.hasClassificationError && selectionControl.enabled)
    ) {
      return false;
    }

    selectionControl.setValue(false, { emitEvent: false });
    return true;
  }

  updateControlValue(
    form: FormGroup,
    document: BusinessCaseInboxDocument,
  ): void {
    if (!document.id) return;

    const classificationControl = form.get(
      `${document.id}${this.formControlSuffix}`,
    );
    const activeDocumentPrediction =
      document.documentClassification?.predictions?.find(
        (prediction) => prediction.isActive,
      );

    if (
      classificationControl instanceof FormControl &&
      (document.manuallySetFileName || activeDocumentPrediction)
    ) {
      classificationControl.setValue(
        document.manuallySetFileName ||
          activeDocumentPrediction.placeholderName,
        { emitEvent: false },
      );
    }
  }

  getSelectedDocumentIds(form: FormGroup): string[] {
    return Object.entries(form.value)
      .filter(
        ([controlName, isSelected]) =>
          isSelected && !controlName.includes(this.formControlSuffix),
      )
      .map(([docId]) => docId);
  }

  getEnabledSelectionControlNames(form: FormGroup): string[] {
    return Object.keys(form.controls).filter(
      (controlName) =>
        !controlName.includes(this.formControlSuffix) &&
        form.get(controlName)?.enabled,
    );
  }

  updateSelectAllCheckboxState(
    form: FormGroup,
    selectAllControl: FormControl,
    selectAllCheckbox: FinCheckboxComponent,
    enabledControlNames: string[],
  ): void {
    const formValues = enabledControlNames.map(
      (controlName) => form.get(controlName)?.value,
    );

    if (!formValues.length) {
      selectAllControl.setValue(false, { emitEvent: false });
      selectAllCheckbox?.setIndeterminateState(false);
      return;
    }

    const allSelected = formValues.every((value) => value);
    const anySelected = formValues.some((value) => value);

    selectAllControl.setValue(allSelected, { emitEvent: false });
    selectAllCheckbox?.setIndeterminateState(!allSelected && anySelected);
  }
}
