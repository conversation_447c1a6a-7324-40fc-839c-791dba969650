import {
  ChangeDetectionStrategy,
  Component,
  HostBinding,
  Inject,
  Input,
  LOCALE_ID,
  OnInit,
} from '@angular/core';
import { FormBuilder } from '@angular/forms';
import { DocumentPreviewContentType, Locale } from '@fincloud/types/enums';
import { FinButtonAppearance } from '@fincloud/ui/button';
import { FinEmptyStateType } from '@fincloud/ui/empty-state';
import { FinModalService } from '@fincloud/ui/modal';
import { FinSize } from '@fincloud/ui/types';
import { Store } from '@ngrx/store';
import { pdfDefaultOptions } from 'ngx-extended-pdf-viewer';
import { shareReplay } from 'rxjs';
import {
  BusinessCaseInboxPdfViewerPageActions,
  inboxFeature,
} from '../../+state';

@Component({
  selector: 'app-inbox-document-preview',
  templateUrl: './inbox-document-preview.component.html',
  styleUrl: './inbox-document-preview.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class InboxDocumentPreviewComponent implements OnInit {
  @HostBinding('class.is-expanded') @Input() isExpanded = false;
  @Input() initialZoom = 200;

  readonly finButtonAppearance = FinButtonAppearance;
  readonly finSize = FinSize;
  readonly minZoom = 50;
  readonly maxZoom = 250;
  readonly zoomStep = 10;
  readonly alwaysVisible = 'always-visible';
  readonly zoomControl = this.fb.nonNullable.control(this.minZoom);
  readonly localeId = this.locale;
  readonly documentPreviewContentType = DocumentPreviewContentType;
  readonly finEmptyStateType = FinEmptyStateType;
  readonly emptyStateMessage = $localize`:@@documentPreview.noPreview:Keine Vorschau für diesen Dateityp verfügbar.`;

  documentForPreview$ = this.store
    .select(inboxFeature.selectSelectedHighlightDocument)
    .pipe(shareReplay({ bufferSize: 1, refCount: true }));
  documentPreviewData$ = this.store.select(
    inboxFeature.selectDocumentPreviewData,
  );
  activeState$ = this.store.select(inboxFeature.selectIsPdfExpanded);
  page$ = this.store.select(inboxFeature.selectOpenDocumentOnPage);

  constructor(
    @Inject(LOCALE_ID) private locale: Locale,
    private store: Store,
    private fb: FormBuilder,
    private finModalService: FinModalService,
  ) {
    pdfDefaultOptions.doubleTapZoomsInHandMode = false;
    pdfDefaultOptions.doubleTapZoomsInTextSelectionMode = false;
    pdfDefaultOptions.doubleTapResetsZoomOnSecondDoubleTap = false;
  }

  ngOnInit() {
    this.zoomControl.setValue(this.initialZoom);
  }

  closePdfPreview() {
    this.store.dispatch(
      BusinessCaseInboxPdfViewerPageActions.closeInboxPdfViewer(),
    );
    this.closeModal();
  }

  zoomChange(zoomLevel: string | number) {
    this.zoomControl.setValue(parseInt(zoomLevel.toString()));
  }

  toggleViewer() {
    this.store.dispatch(
      BusinessCaseInboxPdfViewerPageActions.toggleInboxPdfViewer(),
    );
    this.closeModal();
  }

  private closeModal() {
    if (this.isExpanded) {
      this.finModalService.closeAll();
    }
  }
}
