import {
  BusinessCaseInformation,
  Folder,
  InformationRecord,
} from '@fincloud/swagger-generator/exchange';
import { DataRoomGroupTemplateName, FieldType } from '@fincloud/types/enums';
import {
  BusinessCaseInboxDocument,
  InboxDataRoomEntity,
} from '@fincloud/types/models';
import { CustomInformationRecord } from '../models/custum-information-record';
import { INBOX_DOCUMENT_PLACEHOLDERS } from './inbox-document-placeholders';

export function createInboxDataRoomStructure(
  layer: Folder,
  caseInformation: InformationRecord,
  documentsInUploadSection: BusinessCaseInboxDocument[],
): InboxDataRoomEntity[] {
  // get all the direct document fields for this folder level in the data room

  const documentFields =
    layer?.fields?.reduce((accumulatedFieldsArray, currentValue) => {
      const fieldInformation: BusinessCaseInformation = (
        caseInformation as CustomInformationRecord
      )[currentValue];

      if (fieldInformation?.field?.fieldType === FieldType.DOCUMENT) {
        const documentField = {
          ...fieldInformation.field,
          templateName: DataRoomGroupTemplateName.DOCUMENT,
          value: fieldInformation.value
            ? INBOX_DOCUMENT_PLACEHOLDERS.FILE_PRESENT
            : INBOX_DOCUMENT_PLACEHOLDERS.NO_FILE,
        } as InboxDataRoomEntity;

        // if there is not file, uploaded in the document field
        if (!fieldInformation.value) {
          const documentToEnterCurrentFieldPlaceholder =
            documentsInUploadSection.find((doc) => {
              const activePrediction =
                doc.documentClassification?.predictions?.find(
                  (prediction) => prediction.isActive,
                );

              // if the folder has been manually moved don't display under folder, that prediction expects
              return (
                !doc.manuallySetFolderId &&
                activePrediction?.placeholderKey === documentField.key &&
                !doc.hasClassificationError
              );
            });

          // if there is a document, targeting this placeholder empty document field
          if (documentToEnterCurrentFieldPlaceholder) {
            documentField.id = documentToEnterCurrentFieldPlaceholder.id;
            documentField.isRepresentation = true;
            documentField.value =
              documentToEnterCurrentFieldPlaceholder.contentReference?.fileName;
          }
        }

        accumulatedFieldsArray.push(documentField);
      }

      return accumulatedFieldsArray;
    }, []) || [];

  // classifications only for NEW files (not set in empty "placeholder" document fields)
  const documentsToAddToThisLayer = documentsInUploadSection.filter((doc) => {
    const activePrediction = doc.documentClassification?.predictions?.find(
      (prediction) => prediction.isActive,
    );
    return (
      // if you have moved it manually, it take that with priority
      !doc.hasClassificationError &&
      ((doc.manuallySetFolderId && doc.manuallySetFolderId === layer.id) ||
        // if you haven't moved it and it's prediction isn't a file target, but a folder
        (!doc.manuallySetFolderId &&
          !activePrediction?.placeholderKey &&
          activePrediction?.rootFolderId &&
          activePrediction?.rootFolderId === layer.id))
    );
  });
  if (documentsToAddToThisLayer?.length) {
    documentFields.unshift(
      ...documentsToAddToThisLayer.map((docFile) => {
        const activePrediction =
          docFile.documentClassification.predictions.find(
            (pred) => pred.isActive,
          );
        docFile.manuallySetFileName ?? docFile.contentReference?.fileName;
        // a document field template, extended to an InboxDataRoomEntity
        return {
          key: docFile.contentReference?.documentKey,
          // this will be altered a bit, with the renaming (one of the next steps)
          value: docFile.contentReference?.fileName,
          templateName: DataRoomGroupTemplateName.DOCUMENT,
          label:
            docFile.manuallySetFileName ||
            activePrediction?.placeholderName ||
            docFile.contentReference.fileName,
          id: docFile.id,
          isRepresentation: true,
        } as InboxDataRoomEntity;
      }),
    );
  }

  // create a folder on this folder level and fill it with sub level data, via recursion
  const folders =
    layer?.children?.reduce((accumulatedFoldersArray, currentFolder) => {
      accumulatedFoldersArray.push({
        ...currentFolder,
        templateName: DataRoomGroupTemplateName.FOLDER,
        children: createInboxDataRoomStructure(
          { ...currentFolder },
          caseInformation,
          documentsInUploadSection,
        ),
      });

      return accumulatedFoldersArray;
    }, []) || [];

  return [...documentFields, ...folders];
}
