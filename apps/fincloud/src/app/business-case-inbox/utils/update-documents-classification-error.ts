import {
  BusinessCaseGroup,
  InformationRecord,
} from '@fincloud/swagger-generator/exchange';
import { BusinessCaseInboxDocument } from '@fincloud/types/models';
import { CustomInformationRecord } from '../models/custum-information-record';
import { extractFileIdsFromDataRoomFolder } from './extract-file-names-from-folder';
import { exportInformationFromTargetDocument } from './extract-information-from-target-document';

export function updateDocumentsWithClassificationErrors(
  documents: BusinessCaseInboxDocument[],
  orderedGroupsSnapshot: BusinessCaseGroup[],
  caseInformation: InformationRecord,
): BusinessCaseInboxDocument[] {
  const arrayForLooping = structuredClone(documents);

  const output = documents.map((currentDocument) => {
    let hasClassificationError = false;

    const currentFileData =
      exportInformationFromTargetDocument(currentDocument);

    if (!currentFileData.docFolderId) {
      return { ...currentDocument, hasClassificationError };
    }

    arrayForLooping.forEach((otherDocument) => {
      const otherFileData = exportInformationFromTargetDocument(otherDocument);

      if (
        currentDocument.id !== otherDocument.id &&
        currentFileData.docFolderId === otherFileData.docFolderId &&
        (((!!currentFileData.docPlaceholderName?.length ||
          !!otherFileData.docPlaceholderName?.length) &&
          currentFileData.docPlaceholderName ===
            otherFileData.docPlaceholderName) ||
          ((!!currentFileData.targetPlaceholderKey?.length ||
            !!otherFileData.targetPlaceholderKey?.length) &&
            currentFileData.targetPlaceholderKey ===
              otherFileData.targetPlaceholderKey))
      ) {
        hasClassificationError = true;
      }
    });

    if (!hasClassificationError) {
      const gatheredDocumentIds: string[] = [];

      orderedGroupsSnapshot.forEach((mainGroup) => {
        if (currentFileData.docFolderId === mainGroup.rootFolder.id) {
          gatheredDocumentIds.push(...(mainGroup.rootFolder?.fields || []));
        } else {
          gatheredDocumentIds.push(
            ...extractFileIdsFromDataRoomFolder(currentFileData.docFolderId, {
              ...mainGroup.rootFolder,
              id: mainGroup.rootFolder.id,
            }),
          );
        }
      });

      if (gatheredDocumentIds.length) {
        const businessCasePresentFields = gatheredDocumentIds.map((docId) => {
          const foundField = (caseInformation as CustomInformationRecord)[docId]
            .field;
          return {
            name: foundField.label,
            id: foundField.key,
          };
        });

        const isFilePresent = businessCasePresentFields.some(
          (doc) =>
            doc.name === currentFileData.docPlaceholderName &&
            doc.id !== currentFileData.targetPlaceholderKey,
        );

        if (isFilePresent) {
          hasClassificationError = true;
        }
      }
    }

    return { ...currentDocument, hasClassificationError };
  });

  return output;
}
