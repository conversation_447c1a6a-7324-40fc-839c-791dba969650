import { SelectedInboxFileSource } from '@fincloud/types/enums';
import { BusinessCaseInboxState } from '@fincloud/types/models';

export const BUSINESS_CASE_INBOX_INITIAL_STATE: BusinessCaseInboxState = {
  forwardingEmail: '',
  documents: [],
  temporaryFiles: [],
  isForwardingEmailCopied: false,
  selectedDocumentIds: [],
  searchTerm: '',
  isLoading: false,
  showDocumentPreview: false,
  openDocumentOnPage: 1,
  sortDirection: null,
  selectedHighlightDocument: null,
  selectedInboxFileSource: SelectedInboxFileSource.FILE_UPLOAD,
  isEditingDocument: false,
  dataRoomHash: '',
  isSingleMove: false,
  businessCaseGroupsOrderedSnapShot: [],
  updateSnapShotsAllowed: false,
  isPdfExpanded: false,
  businessCaseInformationSnapShot: null,
  showApplyButtonLoader: false,
  documentPreviewData: { blob: null, documentType: null },
};
