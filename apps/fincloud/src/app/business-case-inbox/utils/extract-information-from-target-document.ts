import { BusinessCaseInboxDocument } from '@fincloud/types/models';

export function exportInformationFromTargetDocument(
  doc: BusinessCaseInboxDocument,
) {
  const activePrediction = doc.documentClassification?.predictions?.find(
    (prediction) => prediction.isActive,
  );

  const docFolderId = doc.manuallySetFolderId || activePrediction?.rootFolderId;

  const docPlaceholderName = doc.manuallySetFolderId
    ? doc.manuallySetFileName || doc.contentReference.fileName
    : doc.manuallySetFileName ||
      activePrediction?.placeholderName ||
      doc.contentReference?.fileName;

  const targetPlaceholderKey = doc.manuallySetFolderId
    ? null
    : activePrediction?.placeholderKey;

  return {
    docFolderId,
    docPlaceholderName,
    targetPlaceholderKey,
  };
}
