import { INgxbStepOption } from 'ngx-ui-tour-ngx-bootstrap/lib/step-option.interface';
import { InboxTourStepsNamesEnum } from '../enums/inbox-tour-steps-names-enum';

export const INBOX_TOUR_CONFIG: Record<
  InboxTourStepsNamesEnum,
  INgxbStepOption[]
> = {
  [InboxTourStepsNamesEnum.INBOX_FEATURE_INTRODUCTION_STEP]: [
    {
      anchorId: InboxTourStepsNamesEnum.INBOX_FEATURE_INTRODUCTION_STEP,
      // TODO the modal z-index is 1095, so we had to go higher
      popoverClass:
        '!tw-ml-[calc(50%-41rem)] !tw-mt-[calc(50vh-23rem)] !tw-transform-none !tw-z-[1097] !tw-max-w-none',
      backdropConfig: {
        zIndex: '1096',
      },
    },
  ],
  [InboxTourStepsNamesEnum.INBOX_FILES_SECTION_STEP]: [
    {
      anchorId: InboxTourStepsNamesEnum.INBOX_FILES_SECTION_STEP,
      popoverClass: '!tw-ml-[1.1rem] !tw-z-[1097] !tw-max-w-none',
      backdropConfig: {
        zIndex: '1096',
      },
    },
  ],
  [InboxTourStepsNamesEnum.INBOX_DATA_ROOM_TREE_SECTION_STEP]: [
    {
      anchorId: InboxTourStepsNamesEnum.INBOX_DATA_ROOM_TREE_SECTION_STEP,
      popoverClass: '!tw-mr-[1.1rem] !tw-z-[1097] !tw-max-w-none',
      backdropConfig: {
        zIndex: '1096',
      },
    },
  ],
};
