import { Folder } from '@fincloud/swagger-generator/exchange';

export function extractFileIdsFromDataRoomFolder(
  folderId: string,
  layer: Folder,
): string[] {
  if (!layer.fields?.length && !layer.children?.length) {
    return [];
  }

  if (layer.id === folderId) {
    return layer.fields;
  }

  let outputIds: string[] = [];

  layer.children?.forEach((innerLayer) => {
    outputIds = [
      ...outputIds,
      ...extractFileIdsFromDataRoomFolder(folderId, innerLayer),
    ];
  });

  return outputIds;
}
