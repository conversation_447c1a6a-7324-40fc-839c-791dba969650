import {
  BusinessCaseGroup,
  InformationRecord,
} from '@fincloud/swagger-generator/exchange';
import {
  BusinessCaseInboxDocument,
  DocumentClassification,
} from '@fincloud/types/models';
import { ClassificationLoadingState } from '../enums/classification-loading-state';
import { updateDocumentsWithClassificationErrors } from './update-documents-classification-error';

export const updateDocumentsWithClassification = (
  documents: BusinessCaseInboxDocument[],
  classifications: DocumentClassification[],
  businessCaseGroupsOrderedSnapShot: BusinessCaseGroup[],
  caseInformation: InformationRecord,
): BusinessCaseInboxDocument[] => {
  const classificationDocumentIds = new Set(
    classifications.map((classification) => classification.documentId),
  );

  // on arrival of predictions, always set the first as active, so it can be correctly applied into the structure
  const classificationsWithActivePredictions = classifications.map(
    (classification) => ({
      ...classification,
      predictions:
        classification.predictions?.map((prediction, index) => ({
          ...prediction,
          isActive: !index,
        })) || [],
    }),
  );

  const documentsWithClassification = documents.map((documentFromStore) => {
    if (!classificationDocumentIds.has(documentFromStore.id)) {
      return documentFromStore;
    }

    const classificationForCurrentDocument =
      classificationsWithActivePredictions.find(
        (classification) => classification.documentId === documentFromStore.id,
      );

    const activePrediction =
      classificationForCurrentDocument?.predictions?.find(
        (prediction) => prediction.isActive,
      );

    return {
      ...documentFromStore,
      isClassificationLoading:
        classificationForCurrentDocument?.documentProcessingStatus ===
        ClassificationLoadingState.PENDING,
      documentClassification: {
        ...classificationForCurrentDocument,
        ...documentFromStore.documentClassification,
      },
      aiPrediction: !!activePrediction,
      rootFolderId: activePrediction?.rootFolderId,
      isLoading: false,
    };
  });

  return updateDocumentsWithClassificationErrors(
    documentsWithClassification,
    businessCaseGroupsOrderedSnapShot,
    caseInformation,
  );
};
