import { InboxAIFilePrediction } from '@fincloud/types/models';

export function hasDocumentClassificationError(
  activePrediction: InboxAIFilePrediction,
  allPredictions?: InboxAIFilePrediction[],
): boolean {
  if (!activePrediction || !allPredictions || allPredictions.length < 2) {
    return false;
  }

  const matchingPredictions = allPredictions.filter((prediction) => {
    // check for placeholder_key
    if (
      activePrediction.placeholder_key &&
      prediction.placeholder_key === activePrediction.placeholder_key
    ) {
      return true;
    }

    // check for placeholder_name and group_key
    if (
      activePrediction.placeholder_name &&
      activePrediction.root_folder_id &&
      prediction.placeholder_name === activePrediction.placeholder_name &&
      prediction.root_folder_id === activePrediction.root_folder_id
    ) {
      return true;
    }

    return false;
  });

  return matchingPredictions.length > 1;
}
