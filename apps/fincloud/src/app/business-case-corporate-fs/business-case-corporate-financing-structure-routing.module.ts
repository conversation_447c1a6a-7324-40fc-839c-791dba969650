import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import {
  SectionApplicationComponent,
  SectionInvitationComponent,
} from '@fincloud/neoshare/business-case';
import {
  FinancingDetailsPath,
  FinancingDetailsSubPage,
} from '@fincloud/types/enums';
import { FinancingDetailsCorporateComponent } from './components/financing-details-corporate/financing-details-corporate.component';
import { FinancingStructureComponent } from './components/financing-structure/financing-structure.component';
import { ParticipationCorporateComponent } from './components/participation-corporate/participation-corporate.component';
import { corporateFinancingStructureResolver } from './guards/corporate-financing-sructure-resolver';
import { financingDetailsCorporateActivateGuard } from './guards/financing-details-corporate-activate.guard';
import { financingStructureCorporateActivateGuard } from './guards/financing-structure-corporate-activate.guard';
import { participationCorporateMatchGuard } from './guards/participation-corporate-match.guard';
import { sectionApplicationCorporateMatchGuard } from './guards/section-application-corporate-match.guard';
import { sectionInvitationCorporateMatchGuard } from './guards/section-invitation-corporate-match.guard';

const routes: Routes = [
  {
    path: '',
    component: FinancingDetailsCorporateComponent,
    canActivate: [financingDetailsCorporateActivateGuard],
    children: [
      {
        path: FinancingDetailsPath.FINANCING_STRUCTURE,
        component: FinancingStructureComponent,
        canActivate: [financingStructureCorporateActivateGuard],
        data: {
          activePath: FinancingDetailsSubPage.FINANCING_STRUCTURE,
        },
        resolve: [corporateFinancingStructureResolver],
      },
      {
        path: FinancingDetailsPath.MY_PARTICIPATION,
        component: ParticipationCorporateComponent,
        canMatch: [participationCorporateMatchGuard],
        data: { activePath: FinancingDetailsSubPage.MY_PARTICIPATION },
      },
      {
        path: FinancingDetailsPath.MY_PARTICIPATION,
        component: SectionApplicationComponent,
        canMatch: [sectionApplicationCorporateMatchGuard],
        data: { activePath: FinancingDetailsSubPage.MY_PARTICIPATION },
      },
      {
        path: FinancingDetailsPath.MY_PARTICIPATION,
        component: SectionInvitationComponent,
        canMatch: [sectionInvitationCorporateMatchGuard],
        data: { activePath: FinancingDetailsSubPage.MY_PARTICIPATION },
      },
    ],
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class BusinessCaseCorporateFinancingStructureRoutingModule {}
