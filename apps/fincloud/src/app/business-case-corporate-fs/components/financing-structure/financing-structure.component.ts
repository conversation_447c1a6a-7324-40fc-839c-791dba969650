import { ChangeDetectionStrategy, Component } from '@angular/core';
import { FormControl, FormGroup } from '@angular/forms';
import { BusinessCaseLayoutService } from '@fincloud/neoshare/business-case';
import {
  selectBusinessCaseContentHeightDefault,
  selectBusinessCaseSidePanelHeightDefault,
  selectBusinessCaseWrapperHeightDefault,
  selectFacilityAmountFields,
  selectFacilityViewData,
  selectFetchingNorthDataCompanies,
  selectMultiCompanyCompositeFieldNorthDataCompaniesOptions,
  selectMultiNorthDataCompanies,
  selectSingleCompanyCompositeFieldNorthDataCompaniesOptions,
} from '@fincloud/state/business-case';
import { StateLibFacilitiesPageActions } from '@fincloud/state/facilities';
import { Facility } from '@fincloud/swagger-generator/business-case-manager';
import { NorthDataCompany } from '@fincloud/swagger-generator/company';
import { ExchangeBusinessCase } from '@fincloud/swagger-generator/exchange';
import { FacilityFieldKeys, FieldType } from '@fincloud/types/enums';
import { FacilityFieldViewModel } from '@fincloud/types/models';
import { FinButtonActionType } from '@fincloud/ui/button';
import { FinDropdownOption } from '@fincloud/ui/dropdown';
import { FinInputType } from '@fincloud/ui/input';
import { FinSeparator } from '@fincloud/ui/separators';
import { FinSize } from '@fincloud/ui/types';
import { Store } from '@ngrx/store';
import { cloneDeep } from 'lodash-es';
import {
  BehaviorSubject,
  Observable,
  debounceTime,
  filter,
  shareReplay,
  tap,
} from 'rxjs';
import { FacilityFieldService } from '../../services/facility-field.service';
import { FacilityModalService } from '../../services/facility-modal.service';

@Component({
  selector: 'app-financing-structure',
  templateUrl: './financing-structure.component.html',
  styleUrls: ['./financing-structure.component.scss'],
  providers: [FacilityFieldService],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FinancingStructureComponent {
  readonly faciliiFieldKeys = FacilityFieldKeys;

  readonly finSize = FinSize;
  readonly finSeparator = FinSeparator;
  readonly finInputType = FinInputType;
  readonly fieldType = FieldType;
  readonly finButtonActionType = FinButtonActionType;

  readonly financingVolumeLabel = $localize`:@@dashboard.businessCase.card.financingVolume:Finanzierungsvolumen`;
  readonly loanPurposeLabel = $localize`:@@facilityField.basicInfo.loanPurpose:Verwendungszweck`;
  readonly guaranteeCompanySearchLabel = $localize`:@@facilityField.basicInfo.guaranteeCompanySearch:Garantiegeber`;
  readonly targetCompanyLabel = $localize`:@@facilityField.basicInfo.targetCompany:Zielgesellschaft `;

  readonly formGroup = new FormGroup({
    financingVolume: new FormControl(''),
    companyName: new FormControl(''),
    purpose: new FormControl(''),
    singleCompositeSelection: new FormControl(''),
    multiCompositeSelection: new FormControl([]),
  });
  private readonly compositeMultiSelectionFieldControlValue$$ =
    new BehaviorSubject<string>('');

  readonly getShowFacilitiesGraph$ = this.store
    .select(selectFacilityAmountFields)
    .pipe(filter(Boolean));

  readonly getSingleCompanyCompositeFieldNorthDataCompaniesOptions$: Observable<
    FinDropdownOption[]
  > = this.store.select(
    selectSingleCompanyCompositeFieldNorthDataCompaniesOptions,
  );

  readonly getMultiCompanyCompositeFieldNorthDataCompaniesOptions$: Observable<
    FinDropdownOption[]
  > = this.store.select(
    selectMultiCompanyCompositeFieldNorthDataCompaniesOptions,
  );

  readonly getFacilityViewData$ = this.store
    .select(selectFacilityViewData)
    .pipe(
      tap((data) => {
        data.editableFacilities.forEach(({ facilityField }) =>
          this.facilityFieldService.setFacilityFieldToForm(
            this.formGroup,
            facilityField as FacilityFieldViewModel,
          ),
        );
        this.formGroup.patchValue({ companyName: data.companyName });

        this.formGroup.patchValue({
          financingVolume:
            data.financingVolumeField?.facilityField?.value?.toString(),
        });
      }),
    );

  readonly fetchingCompanies$: Observable<boolean> = this.store
    .select(selectFetchingNorthDataCompanies)
    .pipe(shareReplay({ bufferSize: 1, refCount: true }));

  readonly sidePanelHeight$ = this.store.select(
    selectBusinessCaseSidePanelHeightDefault,
  );

  readonly contentWrapperHeight$ = this.store.select(
    selectBusinessCaseWrapperHeightDefault,
  );

  readonly contentHeight$ = this.store.select(
    selectBusinessCaseContentHeightDefault,
  );

  readonly compositeMultiSelectionFieldControlValue$: Observable<string> =
    this.compositeMultiSelectionFieldControlValue$$.pipe(debounceTime(100));

  readonly multiNorthDataCompanies$: Observable<NorthDataCompany[]> = this.store
    .select(selectMultiNorthDataCompanies)
    .pipe(debounceTime(100));

  get singleCompositeSelectionControl() {
    return this.formGroup.controls.singleCompositeSelection;
  }

  get multiCompositeSelectionControl() {
    return this.formGroup.controls.multiCompositeSelection;
  }

  constructor(
    private store: Store,
    private facilityFieldService: FacilityFieldService,
    private facilityModalService: FacilityModalService,
    private businessCaseLayoutService: BusinessCaseLayoutService,
  ) {}

  compositeSingleSelectionChanged(
    companyName: string,
    field: FacilityFieldViewModel,
  ): void {
    this.facilityFieldService.compositeSingleSelectionChanged(
      companyName,
      field,
    );
  }

  compositeMultiSelectionChanged(
    selectedOption: FinDropdownOption[],
    field: FacilityFieldViewModel,
  ): void {
    this.facilityFieldService.compositeMultiSelectionChanged(
      selectedOption,
      field,
    );
    this.compositeMultiSelectionFieldControlValue$$.next('');
  }

  compositeMultiSelectionRemoved(
    removed: FinDropdownOption,
    field: FacilityFieldViewModel,
  ): void {
    this.facilityFieldService.compositeMultiSelectionRemoved(removed, field);
  }

  autoCompleteInputChange(query: string, fieldKey: FacilityFieldKeys): void {
    this.facilityFieldService.autoCompleteInputChange(query, fieldKey);
    if (
      fieldKey === FacilityFieldKeys.COMPANY_MULTISELECT_COMPOSITE_FIELD_KEYS
    ) {
      this.compositeMultiSelectionFieldControlValue$$.next(query);
    }
  }

  changeFieldValue(value: string, facility: FacilityFieldViewModel): void {
    if (value === facility.value) {
      return;
    }

    this.facilityFieldService.addConfigurationFacilityFieldValue(
      value,
      facility,
    );
  }

  onAmountFieldValueChanged(
    facilityField: FacilityFieldViewModel,
    value: string,
  ) {
    this.facilityFieldService.amountFieldValueChanged(facilityField, value);
  }

  handleMenuAction(
    tab: 'edit' | 'revisions',
    field: FacilityFieldViewModel,
    facility: Facility,
    businessCase: ExchangeBusinessCase,
  ) {
    this.openModal(tab, field, facility, businessCase);
  }

  openModal(
    tab: 'edit' | 'revisions',
    field: FacilityFieldViewModel,
    facility: Facility,
    businessCase: ExchangeBusinessCase,
  ) {
    const faField = { ...field, value: field.rawValue };
    this.facilityModalService.openManageFacilityFieldModal(
      tab,
      {
        isEdit: true,
        field: faField,
        facility,
      },
      cloneDeep(businessCase),
    );
  }

  clearNorthDataCompanies(): void {
    this.store.dispatch(
      StateLibFacilitiesPageActions.clearNorthDataCompanies(),
    );
    this.compositeMultiSelectionFieldControlValue$$.next('');
  }

  addManualCompany(field: FacilityFieldViewModel): void {
    if (
      field.key === FacilityFieldKeys.COMPANY_SINGLE_SELECT_COMPOSITE_FIELD_KEYS
    ) {
      return this.facilityFieldService.compositeSingleSelectionChanged(
        this.formGroup.controls.singleCompositeSelection.value,
        field,
        true,
      );
    }
    const value = this.formGroup.controls.multiCompositeSelection.getRawValue();
    this.facilityFieldService.compositeMultiSelectionChanged(
      [{ value, label: value }],
      field,
      true,
    );
  }

  onBlurSingleSelectionComposite(field: FacilityFieldViewModel): void {
    if (!this.singleCompositeSelectionControl.value) {
      return this.compositeSingleSelectionChanged(null, field);
    }

    this.clearNorthDataCompanies();
  }
}
