import { ChangeDetectionStrategy, Component } from '@angular/core';
import {
  StateLibBusinessCasePageActions,
  selectActiveIndexFinanceRouterPath,
  selectEditTemplateMode,
  selectHasBusinessCasePermission,
  selectStatisticsCorporateRealEstateVisibility,
} from '@fincloud/state/business-case';
import {
  BusinessCasePermission,
  FinancingDetailsPath,
} from '@fincloud/types/enums';
import { FinTabType } from '@fincloud/ui/tabs';
import { Store } from '@ngrx/store';
import { Observable } from 'rxjs';

@Component({
  selector: 'app-financing-details-corporate',
  templateUrl: './financing-details-corporate.component.html',
  styleUrl: './financing-details-corporate.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FinancingDetailsCorporateComponent {
  tabType: FinTabType = FinTabType.SECONDARY;
  financingDetailsPath = FinancingDetailsPath;
  editModeLabel = $localize`:@@dashboard.overview.finiancingDetails.header.editMode:Bearbeitungsmodus`;
  ownFinancingStructureTabIndex = 0;

  editMode$: Observable<boolean> = this.store.select(selectEditTemplateMode);
  getActivePathIndex$: Observable<number> = this.store.select(
    selectActiveIndexFinanceRouterPath,
  );
  statisticsCorporateRealEstateVisibility$ = this.store.select(
    selectStatisticsCorporateRealEstateVisibility,
  );
  hasEditFinancingStructurePermission$ = this.store.select(
    selectHasBusinessCasePermission(BusinessCasePermission.BCP_00065),
  );

  constructor(private store: Store) {}

  editToggleChange(editMode: boolean): void {
    this.store.dispatch(
      StateLibBusinessCasePageActions.updateEditTemplateMode({
        payload: editMode,
      }),
    );
  }
}
