<div
  class="tw-flex tw-items-center tw-bg-color-surface-primary tw-sticky tw-top-0 tw-z-[2]"
>
  <fin-toolbar>
    <fin-tabs [type]="tabType" (selectedTabChange)="editToggleChange(false)">
      <fin-tab>
        <ng-template finTabLabel>
          <a [routerLink]="[financingDetailsPath.FINANCING_STRUCTURE]">
            <span i18n="@@participation-page.labels.my-financing-structure">
              Eigene Finanzierungsstruktur
            </span>
          </a>
        </ng-template>
      </fin-tab>
      @if (statisticsCorporateRealEstateVisibility$ | async) {
        <fin-tab>
          <ng-template finTabLabel>
            <a [routerLink]="financingDetailsPath.MY_PARTICIPATION">
              <span i18n="@@participation-page.labels.own-investment-amount">
                Eigene Beteiligung
              </span>
            </a>
          </ng-template>
        </fin-tab>
      }
    </fin-tabs>
    <ng-container finToolbarSuffix>
      @if (
        (getActivePathIndex$ | async) === ownFinancingStructureTabIndex &&
        (hasEditFinancingStructurePermission$ | async)
      ) {
        <fin-slide-toggle
          [checked]="editMode$ | async"
          [label]="editModeLabel"
          class="tw-ml-auto"
          (slideChange)="editToggleChange($event)"
        ></fin-slide-toggle>
      }
    </ng-container>
  </fin-toolbar>
</div>

<router-outlet></router-outlet>
