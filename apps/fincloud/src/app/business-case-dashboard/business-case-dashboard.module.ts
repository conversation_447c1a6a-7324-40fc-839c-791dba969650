import { NgxSliderModule } from '@angular-slider/ngx-slider';
import { DragDropModule } from '@angular/cdk/drag-drop';
import {
  CommonModule,
  CurrencyPipe,
  DatePipe,
  DecimalPipe,
} from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { AvatarComponent } from '@fincloud/components/avatar';
import { NsUiBooleansModule } from '@fincloud/components/booleans';
import { NsUiButtonsModule } from '@fincloud/components/buttons';
import { NsUiChartsModule } from '@fincloud/components/charts';
import { NsUiCompositeModule } from '@fincloud/components/composite';
import { NsUiDataRoomModule } from '@fincloud/components/data-room';
import { NsUiFormsModule } from '@fincloud/components/forms';
import { NsUiHintModule } from '@fincloud/components/hint';
import { NsUiIconsModule } from '@fincloud/components/icons';
import { NsUiLayoutModule } from '@fincloud/components/layout';
import { NsUiNavigationModule } from '@fincloud/components/navigation';
import { NsUiNumberModule } from '@fincloud/components/number';
import { NsUiOrganizationLogoRendererModule } from '@fincloud/components/organization-logo-renderer';
import { NsUiSelectsModule } from '@fincloud/components/selects';
import { NsUiNgxModule } from '@fincloud/components/third-party-modules';
import { NsUiTooltipModule } from '@fincloud/components/tooltip';
import { NsUiTruncatedTextModule } from '@fincloud/components/truncated-text';
import { NsCoreAspectRatioModule } from '@fincloud/core/aspect-ratio';
import { NsCoreDateModule } from '@fincloud/core/date';
import { NsCoreDirectivesModule } from '@fincloud/core/directives';
import { NsCoreLayoutModule } from '@fincloud/core/layout';
import { NsCoreOverlaysModule } from '@fincloud/core/overlays';
import {
  NsCorePipesModule,
  RemoveTrailingZerosPipe,
} from '@fincloud/core/pipes';
import { NsCoreScrollModule } from '@fincloud/core/scroll';
import { NsBusinessCaseRefactoringModule } from '@fincloud/neoshare/business-case';
import { NsDataRoomModule } from '@fincloud/neoshare/data-room';
import { NsDocumentModule } from '@fincloud/neoshare/document';
import { NsNeogptChatModule } from '@fincloud/neoshare/neogpt-chat';
import { NsTemplateFieldModule } from '@fincloud/neoshare/template-field';
import {
  StateLibFinancingStructureEffects,
  businessCaseRealEstateFeature,
} from '@fincloud/state/business-case-real-estate';
import { StateLibCustomerEffects } from '@fincloud/state/customer';
import { StateLibDocumentEffects } from '@fincloud/state/document';
import { faqFeature } from '@fincloud/state/faq';
import { BUSINESS_CASE_DASHBOARD_FEATURE_KEY } from '@fincloud/state/utils';
import { FinActionsMenuModule } from '@fincloud/ui/actions-menu';
import { FinAvatarModule } from '@fincloud/ui/avatar-default';
import { FinAvatarParticipantsModule } from '@fincloud/ui/avatar-participants';
import { FinBadgesModule } from '@fincloud/ui/badges';
import { FinButtonModule } from '@fincloud/ui/button';
import { FinContainerModule } from '@fincloud/ui/container';
import { FinDropdownModule } from '@fincloud/ui/dropdown';
import { FinExpansionPanelModule } from '@fincloud/ui/expansion-panel';
import { FinFieldMessageModule } from '@fincloud/ui/field-message';
import { FinIconModule } from '@fincloud/ui/icon';
import { FinMenuItemModule } from '@fincloud/ui/menu-item';
import { FinModalModule } from '@fincloud/ui/modal';
import { FinProgressBarModule } from '@fincloud/ui/progress-bar';
import { FinScrollbarModule } from '@fincloud/ui/scrollbar';
import {
  FinHorizontalSeparatorDirective,
  FinSeparatorsModule,
} from '@fincloud/ui/separators';
import { FinSlideToggleModule } from '@fincloud/ui/slide-toggle';
import {
  FinRowDetailsTemplateDirective,
  FinRowTemplateDirective,
  FinTableModule,
} from '@fincloud/ui/table';
import { FinTabsModule } from '@fincloud/ui/tabs';
import { FinToolbarModule } from '@fincloud/ui/toolbar';
import { FinTooltipModule } from '@fincloud/ui/tooltip';
import { FinTruncateTextModule } from '@fincloud/ui/truncate-text';
import { FinWarningMessageModule } from '@fincloud/ui/warning-message';
import { provideEffects } from '@ngrx/effects';
import { provideState } from '@ngrx/store';
import { NgxPermissionsModule } from 'ngx-permissions';
import { NgScrollbarModule } from 'ngx-scrollbar';
import { BusinessCaseCadrModule } from '../business-case-cadr/business-case-cadr.module';
import { BusinessCaseTeaserModule } from '../business-case-teaser/business-case-teaser.module';
import {
  ActivityLogsEffects,
  AdministrationEffects,
  ApplicationEffects,
  BusinessCaseDataRoomEffects,
  BusinessCaseEffects,
  ChatEffects,
  ChatExportEffects,
  CompanyEffects,
  CompanyPortalEffects,
  CriteriaEffects,
  CustomerEffects,
  FacilityEffects,
  InvitationsEffects,
  PermissionsEffects,
  TeaserExportEffects,
  UserEffects,
} from './+state';
import { businessCaseReducers } from './+state/reducers';
import { BusinessCaseDashboardRoutingModule } from './business-case-dashboard-routing.module';
import { AllGroupsPortalActionsComponent } from './components/all-groups-portal-actions/all-groups-portal-actions.component';
import { BusinessCaseApplicationCriteriaComponent } from './components/business-case-application-criteria/business-case-application-criteria.component';
import { BusinessCaseDashboardHeaderComponent } from './components/business-case-dashboard-header/business-case-dashboard-header.component';
import { BusinessCaseDashboardComponent } from './components/business-case-dashboard/business-case-dashboard.component';
import { BusinessCaseFaqsCardComponent } from './components/business-case-faqs-card/business-case-faqs-card.component';
import { BusinessCaseLeaderContactInfoComponent } from './components/business-case-leader-contact-info/business-case-leader-contact-info.component';
import { BusinessCaseOverviewBreakdownCardComponent } from './components/business-case-overview-breakdown-card/business-case-overview-breakdown-card.component';
import { BusinessCaseOverviewGeneralComponent } from './components/business-case-overview-general/business-case-overview-general.component';
import { BusinessCaseOverviewComponent } from './components/business-case-overview/business-case-overview.component';
import { BusinessCaseRevisionsCardComponent } from './components/business-case-revisions-card/business-case-revisions-card.component';
import { ChangeBusinessCaseStateModalComponent } from './components/change-business-case-state-modal/change-business-case-state-modal.component';
import { FacilitySelectorComponent } from './components/facility-selector/facility-selector.component';
import { FinancingAmountsBreakdownComponent } from './components/financing-amounts-breakdown/financing-amounts-breakdown.component';
import { ParticipantIconComponent } from './components/participant-icon/participant-icon.component';

@NgModule({
  declarations: [
    BusinessCaseDashboardComponent,
    BusinessCaseApplicationCriteriaComponent,
    FacilitySelectorComponent,
    AllGroupsPortalActionsComponent,
    BusinessCaseOverviewBreakdownCardComponent,
    ParticipantIconComponent,
    BusinessCaseFaqsCardComponent,
    BusinessCaseRevisionsCardComponent,
    BusinessCaseDashboardHeaderComponent,
    FinancingAmountsBreakdownComponent,
    ChangeBusinessCaseStateModalComponent,
    BusinessCaseOverviewGeneralComponent,
    BusinessCaseOverviewComponent,
    BusinessCaseLeaderContactInfoComponent,
  ],
  imports: [
    CommonModule,
    FormsModule,
    FinWarningMessageModule,
    NsUiHintModule,
    NsUiOrganizationLogoRendererModule,
    DragDropModule,
    NsUiIconsModule,
    FinTabsModule,
    FinContainerModule,
    NgScrollbarModule,
    ReactiveFormsModule,
    FinBadgesModule,
    NsBusinessCaseRefactoringModule,
    NgxSliderModule,
    AvatarComponent,
    FinActionsMenuModule,
    NsUiButtonsModule,
    NsDataRoomModule,
    FinButtonModule,
    FinIconModule,
    NsDocumentModule,
    NsTemplateFieldModule,
    BusinessCaseCadrModule,
    NgxPermissionsModule.forChild(),
    NsNeogptChatModule,
    BusinessCaseDashboardRoutingModule,
    NsBusinessCaseRefactoringModule,
    FinSeparatorsModule,
    NsCoreDateModule,
    // APP Modules
    BusinessCaseTeaserModule,
    FinTabsModule,
    FinSlideToggleModule,
    FinHorizontalSeparatorDirective,
    FinToolbarModule,
    FinExpansionPanelModule,
    FinTableModule,
    FinRowTemplateDirective,
    FinRowDetailsTemplateDirective,
    FinScrollbarModule,
    FinAvatarModule,
    FinTruncateTextModule,
    FinProgressBarModule,
    FinTableModule,
    FinRowTemplateDirective,
    FinRowDetailsTemplateDirective,
    FinAvatarParticipantsModule,
    NsCoreDirectivesModule,
    NsCoreScrollModule,
    NsCorePipesModule,
    NsCoreLayoutModule,
    NsCoreOverlaysModule,
    NsUiNgxModule,
    NsUiDataRoomModule,
    NsUiTruncatedTextModule,
    NsUiButtonsModule,
    NsUiSelectsModule,
    NsUiIconsModule,
    NsUiCompositeModule,
    NsUiTooltipModule,
    NsUiNavigationModule,
    NsUiBooleansModule,
    NsUiNumberModule,
    NsUiChartsModule,
    NsUiFormsModule,
    NsUiLayoutModule,
    FinModalModule,
    FinDropdownModule,
    FinFieldMessageModule,
    FinTooltipModule,
    FinMenuItemModule,
    NsCoreAspectRatioModule,
  ],
  providers: [
    provideState(BUSINESS_CASE_DASHBOARD_FEATURE_KEY, businessCaseReducers),
    provideState(faqFeature),
    //TODO: to be moved within business-case-dashboard state,once composeReducers (local impl)
    //is replaced via combineReducers (ngrx impl)
    provideState(businessCaseRealEstateFeature),
    provideEffects(
      BusinessCaseEffects,
      CriteriaEffects,
      ApplicationEffects,
      InvitationsEffects,
      CustomerEffects,
      UserEffects,
      ActivityLogsEffects,
      ChatEffects,
      ChatExportEffects,
      CompanyPortalEffects,
      FacilityEffects,
      CompanyEffects,
      PermissionsEffects,
      StateLibDocumentEffects,
      TeaserExportEffects,
      StateLibCustomerEffects,
      BusinessCaseDataRoomEffects,
      AdministrationEffects,
      StateLibFinancingStructureEffects,
    ),
    RemoveTrailingZerosPipe,
    DatePipe,
    CurrencyPipe,
    DecimalPipe,
  ],
})
export class BusinessCaseDashboardModule {}
