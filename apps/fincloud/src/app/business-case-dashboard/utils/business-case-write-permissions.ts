import { BusinessCasePermission } from '@fincloud/types/enums';

export const BUSINESS_CASE_WRITE_PERMISSIONS: BusinessCasePermission[] = [
  BusinessCasePermission.BCP_00016,
  BusinessCasePermission.BCP_00017,
  BusinessCasePermission.BCP_00018,
  BusinessCasePermission.BCP_00019,
  BusinessCasePermission.BCP_00020,
  BusinessCasePermission.BCP_00027,
  BusinessCasePermission.BCP_00028,
  BusinessCasePermission.BCP_00029,
  BusinessCasePermission.BCP_00030,
  // BusinessCasePermission.BCP_00031,
  BusinessCasePermission.BCP_00033,
  BusinessCasePermission.BCP_00034,
  BusinessCasePermission.BCP_00035,
  BusinessCasePermission.BCP_00036,
  BusinessCasePermission.BCP_00037,
  BusinessCasePermission.BCP_00040,
  BusinessCasePermission.BCP_00041,
  BusinessCasePermission.BCP_00042,
  BusinessCasePermission.BCP_00046,
  BusinessCasePermission.BCP_00047,
  BusinessCasePermission.BCP_00048,
  BusinessCasePermission.BCP_00050,
  BusinessCasePermission.BCP_00052,
  BusinessCasePermission.BCP_00054,
  BusinessCasePermission.BCP_00057,
  BusinessCasePermission.BCP_00053,
  BusinessCasePermission.BCP_00055,
  BusinessCasePermission.BCP_00056,
  BusinessCasePermission.BCP_00059,
  BusinessCasePermission.BCP_00063,
  BusinessCasePermission.BCP_00065,
  BusinessCasePermission.BCP_00066,
  BusinessCasePermission.BCP_00067,
  BusinessCasePermission.BCP_00068,
  BusinessCasePermission.BCP_00069,
  BusinessCasePermission.BCP_00070,
  BusinessCasePermission.BCP_00123,
  BusinessCasePermission.BCP_00125,
  BusinessCasePermission.BCP_00130,
  BusinessCasePermission.BCP_00132,
  BusinessCasePermission.BCP_00133,
  BusinessCasePermission.BCP_00134,
  BusinessCasePermission.BCP_00038,
  BusinessCasePermission.BCP_00062,
  BusinessCasePermission.BCP_00139,
  BusinessCasePermission.BCP_00140,
  BusinessCasePermission.BCP_00141,
  BusinessCasePermission.BCP_00142,
  BusinessCasePermission.BCP_00143,
  BusinessCasePermission.BCP_00149,
];
