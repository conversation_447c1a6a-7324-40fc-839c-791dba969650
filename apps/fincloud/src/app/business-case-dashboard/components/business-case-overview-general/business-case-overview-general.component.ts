import { Component, DestroyRef, OnInit } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { BusinessCaseModelService } from '@fincloud/core/business-case';
import { neitherNullNorUndefined } from '@fincloud/core/utils';
import {
  ActivityLogService,
  BusinessCaseLayoutService,
} from '@fincloud/neoshare/business-case';
import { FaqService } from '@fincloud/neoshare/services';
import { selectAccessRights } from '@fincloud/state/access';
import {
  selectActivityLogs,
  selectBusinessCase,
  selectBusinessCaseBreakdown,
  selectBusinessCaseContentHeightDefault,
  selectBusinessCaseContentHeightNoSecondaryTabs,
  selectBusinessCaseParticipantsPermissions,
  selectBusinessCaseVisibilityForCustomer,
  selectBusinessCaseWrapperHeightDefault,
  selectBusinessCaseWrapperHeightNoSecondaryTabs,
  selectCriteria,
  selectHasAnyBusinessCasePermission,
  selectHasBusinessCasePermission,
  selectIsBusinessCaseRealEstate,
  selectUsersById,
} from '@fincloud/state/business-case';
import { selectCustomerType } from '@fincloud/state/customer';
import { faqFeature } from '@fincloud/state/faq';
import { selectIsPlatformManager, selectUserId } from '@fincloud/state/user';
import { Criteria } from '@fincloud/swagger-generator/application';
import { User } from '@fincloud/swagger-generator/authorization-server';
import {
  Faq,
  ParticipantCasePermissionSetEntity,
} from '@fincloud/swagger-generator/business-case-manager';
import {
  ActivityLogDto,
  ExchangeBusinessCase,
} from '@fincloud/swagger-generator/exchange';
import { BusinessCasePermission, CustomerType } from '@fincloud/types/enums';
import {
  AccessRights,
  AppState,
  BusinessCaseBreakdown,
  Dictionary,
  PartnerInfo,
  StackedBarChartData,
} from '@fincloud/types/models';
import { FinTabType } from '@fincloud/ui/tabs';
import { FinSize } from '@fincloud/ui/types';
import { Store } from '@ngrx/store';
import { isEmpty, max, orderBy } from 'lodash-es';
import {
  Observable,
  auditTime,
  combineLatest,
  filter,
  map,
  shareReplay,
  switchMap,
  tap,
} from 'rxjs';
import { ActivityLogsPageActions } from '../../+state/actions';

@Component({
  selector: 'app-business-case-overview-general',
  templateUrl: './business-case-overview-general.component.html',
  styleUrls: ['./business-case-overview-general.component.scss'],
})
export class BusinessCaseOverviewGeneralComponent implements OnInit {
  businessCase: ExchangeBusinessCase;
  activityLogs$: Observable<ActivityLogDto[]>;
  usersById: Dictionary<User> = {};
  faqs: Faq[];
  criteria: Criteria[];
  access: AccessRights;

  readonly finTabType = FinTabType;
  readonly finTypeSize = FinSize;

  readonly businessCasePermission = BusinessCasePermission;

  faqs$ = this.store.select(faqFeature.selectFaq);

  getBusinessCase$ = this.store.select(selectBusinessCase).pipe(
    filter(Boolean),
    tap((businessCase) => (this.businessCase = businessCase)),
  );

  getUsersById$ = this.store.select(selectUsersById).pipe(
    filter(Boolean),
    tap((usersById) => (this.usersById = usersById)),
  );

  getAccessRights$ = this.store.select(selectAccessRights).pipe(
    filter(Boolean),
    tap((access) => (this.access = access)),
  );

  getUserId$ = this.store.select(selectUserId).pipe(filter(Boolean));

  canSeeAmountCollected$: Observable<boolean> = this.store.select(
    selectHasBusinessCasePermission(BusinessCasePermission.BCP_00010),
  );

  canSeeParticipationDistribution$: Observable<boolean> = this.store.select(
    selectHasBusinessCasePermission(BusinessCasePermission.BCP_00011),
  );

  getBusinessCaseVisibilityForCustomer$ = this.store.select(
    selectBusinessCaseVisibilityForCustomer,
  );

  businessCaseBreakdown$: Observable<BusinessCaseBreakdown> = this.store
    .select(selectBusinessCaseBreakdown)
    .pipe(
      filter(({ businessCase }) => neitherNullNorUndefined(businessCase)),
      map(
        ({
          businessCase,
          customerKeysNames,
          participantsPermissions,
          userToken,
        }) =>
          this.businessCaseModelService.toBusinessCaseBreakdownViewModel(
            businessCase,
            customerKeysNames,
            participantsPermissions,
            userToken,
          ),
      ),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );

  stackedBarChartData$: Observable<StackedBarChartData> =
    this.businessCaseBreakdown$.pipe(
      map((breakdown) => ({
        series: [
          {
            name: $localize`:@@dashboard.businessCase.collected:Gesammelt`,
            value: breakdown.investedAmount,
          },
          {
            name: $localize`:@@dashboard.businessCase.necessary:Erforderlich`,
            value: breakdown.financingVolume,
          },
        ],
      })),
    );

  breakdownParticipants$ = combineLatest([
    this.businessCaseBreakdown$.pipe(
      map((breakdown) => {
        return orderBy(
          [breakdown.leadPartner, ...breakdown.partners],
          (v) => v.totalParticipationAmount,
          'desc',
        );
      }),
    ),
    this.store.select(selectBusinessCaseParticipantsPermissions),
  ]).pipe(
    filter(([_, participantsPermissions]) => !isEmpty(participantsPermissions)),
    map(([participants, participantsPermissions]) =>
      this.mapBreakdownParticipantsToData(
        this.filterBreakdownParticipantsByPermission(
          participants,
          participantsPermissions,
        ),
      ).sort((a, b) => b.totalParticipationAmount - a.totalParticipationAmount),
    ),
    auditTime(500),
  );

  private readonly isPlatformManager$ = this.store.select(
    selectIsPlatformManager,
  );

  private readonly hasKpiPermissions$: Observable<boolean> = this.store.select(
    selectHasAnyBusinessCasePermission([BusinessCasePermission.BCP_00157]),
  );

  private readonly canSeeKpi$: Observable<boolean> = combineLatest([
    this.isPlatformManager$,
    this.hasKpiPermissions$,
  ]).pipe(
    map(
      ([isPlatformManager, hasKpiPermissions]) =>
        isPlatformManager || hasKpiPermissions,
    ),
  );

  private readonly canSeeGeneral$: Observable<boolean> = this.store.select(
    selectHasAnyBusinessCasePermission([BusinessCasePermission.BCP_00163]),
  );

  private readonly isCorporateFSP$ = this.store
    .select(selectCustomerType)
    .pipe(
      map(
        (customerType) =>
          customerType === CustomerType.CORPORATE ||
          customerType === CustomerType.FSP,
      ),
    );

  private readonly hideTabs$ = combineLatest([
    this.store.select(selectIsBusinessCaseRealEstate),
    this.isCorporateFSP$,
    this.canSeeKpi$,
    this.canSeeGeneral$,
  ]).pipe(
    map(
      ([
        isBusinessCaseRealEstate,
        isCorporateFSP,
        canSeeKpi,
        canSeeGeneral,
      ]) => {
        return (
          isCorporateFSP ||
          !isBusinessCaseRealEstate ||
          !canSeeKpi ||
          !canSeeGeneral
        );
      },
    ),
    shareReplay({
      refCount: true,
      bufferSize: 1,
    }),
  );

  readonly contentWrapperHeight$ = this.hideTabs$.pipe(
    switchMap((hideTabs) =>
      this.store.select(
        hideTabs
          ? selectBusinessCaseWrapperHeightNoSecondaryTabs
          : selectBusinessCaseWrapperHeightDefault,
      ),
    ),
  );

  readonly contentHeight$ = this.hideTabs$.pipe(
    switchMap((hideTabs) =>
      this.store.select(
        hideTabs
          ? selectBusinessCaseContentHeightNoSecondaryTabs
          : selectBusinessCaseContentHeightDefault,
      ),
    ),
  );

  constructor(
    private destroyRef: DestroyRef,
    private store: Store<AppState>,
    private faqService: FaqService,
    private businessCaseModelService: BusinessCaseModelService,
    private businessCaseLayoutService: BusinessCaseLayoutService,
    private activityLogRefreshService: ActivityLogService,
  ) {}

  ngOnInit(): void {
    this.hookIntoBusinessCaseUpdates();
    this.hookIntoActivityLogsUpdates();
    this.hookIntoFaqsUpdates();
    this.hookIntoCriteriaUpdates();
  }

  onLoadMoreRevisions() {
    this.store.dispatch(ActivityLogsPageActions.loadMoreActivityLogs());
  }

  private hookIntoBusinessCaseUpdates() {
    combineLatest([this.getBusinessCase$, this.getUsersById$])
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe();
  }

  private hookIntoActivityLogsUpdates() {
    this.activityLogs$ = this.store.select(selectActivityLogs).pipe(
      map((logs) =>
        logs.filter((log) =>
          this.activityLogRefreshService.displayActivityLogsFilter(log),
        ),
      ),
      takeUntilDestroyed(this.destroyRef),
    );
  }

  private hookIntoFaqsUpdates() {
    this.faqService
      .getFaqs(this.businessCase.id)
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((faqs: Faq[]) => (this.faqs = faqs));

    this.faqService
      .asFaqObservable()
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((faq: Faq | string) => {
        // TODO: Move into store please
        // On any action update/create/delete make new request to BE and update the FAQ in the store (effect)
        if (typeof faq === 'string') {
          // delete
          this.faqs = this.faqs.filter((f) => f.id !== faq);
        } else {
          const index = this.faqs.findIndex((f) => f.id === faq.id);
          if (index === -1) {
            // create
            this.faqs = [...this.faqs, faq];
          } else {
            // update
            this.faqs[index] = faq;
          }
        }
      });
  }

  private hookIntoCriteriaUpdates() {
    this.store
      .select(selectCriteria)
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((criteria) => (this.criteria = criteria));
  }

  private mapBreakdownParticipantsToData(participants: PartnerInfo[]) {
    return participants.map((participant) => {
      const series = [
        {
          name: $localize`:@@businessCase.overviewBreakDownCard.series:Gesamtbetrag`,
          value: participant.totalParticipationAmount,
        },
      ];

      return {
        partnerInfo: participant,
        name: participant.name,
        totalParticipationAmount: participant.totalParticipationAmount,
        participation: {
          scale: participant.totalParticipationAmount
            ? parseFloat(
                (
                  participant.totalParticipationAmount /
                  max([...participants].map((p) => p.totalParticipationAmount))
                ).toFixed(2),
              )
            : 0,
          series: series,
        },
      };
    });
  }

  private filterBreakdownParticipantsByPermission(
    participants: PartnerInfo[],
    permissions: Dictionary<ParticipantCasePermissionSetEntity>,
  ): PartnerInfo[] {
    return participants.filter(
      (participant) =>
        permissions[participant.customerKey]?.permissions?.includes(
          BusinessCasePermission.BCP_00063,
        ) ||
        permissions[participant.customerKey]?.permissions?.includes(
          BusinessCasePermission.BCP_00140,
        ),
    );
  }
}
