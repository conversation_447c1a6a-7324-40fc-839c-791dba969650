import {
  Component,
  DestroyRef,
  OnDestroy,
  OnInit,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { ActivatedRoute, NavigationEnd, Router } from '@angular/router';
import { ConfirmationDialogComponent } from '@fincloud/components/modals';
import { BusinessCaseModelService } from '@fincloud/core/business-case';
import { ModalService } from '@fincloud/core/modal';
import {
  PortalSocketMessage,
  PortalSocketMessageType,
  SocketService,
  SocketType,
} from '@fincloud/core/socket';
import { selectAccessRights } from '@fincloud/state/access';
import {
  selectActiveNavigationItem,
  selectApplicationsLoaded,
  selectBusinessCase,
  selectBusinessCaseDashboardCardData,
  selectBusinessCaseHeaderHeight,
  selectBusinessCaseParticipantsPermissions,
  selectBusinessCaseType,
  selectBusinessCaseVisibilityForCustomer,
  selectCaseFieldsAccess,
  selectCustomerNamesByKey,
  selectEditTemplateMode,
  selectFinancindDetailsMiscCaseWithParticipantRealEstateVisibility,
  selectHasAnyBusinessCasePermission,
  selectHasBusinessCasePermission,
  selectHasReceivedInvitation,
  selectInvitations,
  selectInvitationsLoaded,
  selectIsBusinessCaseActive,
  selectIsCompanyDataRoomTab,
  selectIsLead,
  selectIsRealEstateTabOpened,
  selectPendingInvitation,
  selectSelectedCollaborationInvitationsApplications,
  selectSelectedDataRoomCase,
  selectSelectedDataRoomCompany,
  selectUsersById,
} from '@fincloud/state/business-case';
import {
  selectCustomer,
  selectIsCustomerGuest,
} from '@fincloud/state/customer';
import { StateLibInvitationPageActions } from '@fincloud/state/invitation';
import { selectIsNeoGptVisible } from '@fincloud/state/neogpt-chat';
import {
  selectUserCustomerKey,
  selectUserId,
  selectUserToken,
} from '@fincloud/state/user';
import { Invitation } from '@fincloud/swagger-generator/application';
import { User } from '@fincloud/swagger-generator/authorization-server';
import { ParticipantControllerService } from '@fincloud/swagger-generator/business-case-manager';
import {
  ActivityLogDto,
  ExchangeBusinessCase,
} from '@fincloud/swagger-generator/exchange';
import {
  CaseFieldAccess,
  CaseFieldInputRequest,
} from '@fincloud/swagger-generator/portal';
import {
  BusinessCaseDashboardPath,
  BusinessCaseDataRoomTab,
  BusinessCasePermission,
  BusinessCaseType,
  CustomerStatus,
  CustomerType,
  FinancingDetailsPath,
  FinancingStructureType,
  InvitationFlowStrategy,
  InvitationStatus,
} from '@fincloud/types/enums';
import {
  AccessRights,
  AppState,
  BusinessCase,
  CollaborationTab,
  Dictionary,
} from '@fincloud/types/models';
import { NgbNavChangeEvent } from '@ng-bootstrap/ng-bootstrap';
import { Store } from '@ngrx/store';
import { isEmpty } from 'lodash-es';
import { Observable, asapScheduler, combineLatest, concat, of } from 'rxjs';
import {
  catchError,
  debounceTime,
  distinctUntilChanged,
  distinctUntilKeyChanged,
  filter,
  map,
  observeOn,
  shareReplay,
  startWith,
  switchMap,
  take,
  tap,
  withLatestFrom,
} from 'rxjs/operators';
import { BusinessCaseDashboardTabService } from '../../services/business-case-dashboard-tab.service';

import { LayoutCommunicationService } from '@fincloud/core/layout';
import {
  ActivityLogService,
  BusinessCaseLayoutService,
} from '@fincloud/neoshare/business-case';
import { StateLibBusinessCasePageActions } from '@fincloud/state/business-case';
import { StateLibBusinessCaseRealEstatePageActions } from '@fincloud/state/business-case-real-estate';
import { StateLibCompanyPortalPageActions } from '@fincloud/state/company-portal';
import {
  StateLibSideNavigationsPageActions,
  sideNavigationsFeature,
} from '@fincloud/state/side-navigations';
import { StateLibUserPageActions } from '@fincloud/state/user';
import { PORTAL_SUBSCRIPTION_DESTINATION } from '@fincloud/utils';
import { concatLatestFrom } from '@ngrx/operators';
import { TraceClass, TraceMethod } from '@sentry/angular';
import {
  ActivityLogsPageActions,
  BusinessCasePageActions,
  CompanyPortalPageActions,
} from '../../+state/actions';

@Component({
  selector: 'app-business-case-dashboard',
  templateUrl: './business-case-dashboard.component.html',
  styleUrls: ['./business-case-dashboard.component.scss'],
})
@TraceClass({ name: 'BusinessCaseDashboardComponent' })
export class BusinessCaseDashboardComponent implements OnInit, OnDestroy {
  private readonly dataRoomPath = 'data-room';
  businessCaseInfo: BusinessCase;
  businessCase: ExchangeBusinessCase;
  customerUser: User;
  activityLogs$: Observable<ActivityLogDto[]>;
  distance: number;
  editMode = false;
  access: AccessRights;
  usersById: Dictionary<User> = {};
  callerCustomerName: string;
  shouldShowCallToActions: boolean;
  isBankType: boolean;
  businessCaseInvitation: Invitation;
  userId: string;
  hasCompanyVisibleFields: boolean;
  isCompanyDataRoomTab: boolean;
  invitations: Invitation[];
  hasCheckForApplicationsStarted: boolean;
  selectedTabId: string;
  latestSelectedTabId: string;
  isRealEstateCase: boolean;
  receivedInvitation$: Observable<boolean> = this.store.select(
    selectHasReceivedInvitation,
  );
  caseCloseReason: string;
  selectedIndex = 0;

  private readonly tabNames = [
    'overview',
    'financing-details',
    'data-room',
    'collaboration',
    'management',
  ];

  get isInviteToJoinStrategy() {
    return (
      this.businessCaseInvitation?.invitationType ===
      InvitationFlowStrategy.INVITE_TO_JOIN_STRATEGY
    );
  }

  @ViewChild('businessCaseReasonsForClose')
  businessCaseReasonsForCloseTemplateRef: TemplateRef<unknown>;

  getBusinessCasePermissions$ = this.store
    .select(selectBusinessCaseParticipantsPermissions)
    .pipe(filter((x) => !!Object.keys(x)?.length));

  getUsersById$ = this.store.select(selectUsersById).pipe(
    filter(Boolean),
    tap((usersById) => (this.usersById = usersById)),
  );

  getUserId$ = this.store.select(selectUserId).pipe(filter(Boolean));

  getAccessRights$ = this.store.select(selectAccessRights).pipe(
    filter(Boolean),
    tap((access) => (this.access = access)),
  );

  getBusinessCase$ = this.store.select(selectBusinessCase).pipe(
    filter(Boolean),
    tap((businessCase) => (this.businessCase = businessCase)),
  );

  getUserToken$ = this.store.select(selectUserToken).pipe(filter(Boolean));

  getCustomerNameByKey$ = this.store
    .select(selectCustomerNamesByKey)
    .pipe(filter(Boolean));

  canSeeTeaserExport$ = this.store.select(
    selectHasAnyBusinessCasePermission([BusinessCasePermission.BCP_00015]),
  );

  hasPermissionForGroupAccess$ = this.store.select(
    selectHasAnyBusinessCasePermission([BusinessCasePermission.BCP_00017]),
  );

  isNotEmployeeOfParticipatingCustomer$ = this.getAccessRights$.pipe(
    distinctUntilKeyChanged('granular'),
    map(
      (accessRights) =>
        !accessRights.granular.isEmployeeOfParticipatingCustomer,
    ),
  );

  getBusinessCaseType$ = this.store
    .select(selectBusinessCaseType)
    .pipe(filter(Boolean));

  showParticipationRole$ = this.getBusinessCaseType$.pipe(
    map((c) => c === BusinessCaseType.FINANCING_CASE),
  );

  isCompanyDataRoomTab$ = this.store.select(selectIsCompanyDataRoomTab);

  canSeeOverviewTab$ = this.store.select(
    selectHasAnyBusinessCasePermission([BusinessCasePermission.BCP_00001]),
  );

  canSeeDataRoomTab$ = this.store.select(
    selectHasAnyBusinessCasePermission([BusinessCasePermission.BCP_00002]),
  );

  canSeeCollaborationsTab$ = this.store.select(
    selectHasAnyBusinessCasePermission([BusinessCasePermission.BCP_00003]),
  );

  canSeeManagementTab$ = this.store.select(
    selectHasAnyBusinessCasePermission([BusinessCasePermission.BCP_00004]),
  );

  userCustomerKey$ = this.store
    .select(selectUserCustomerKey)
    .pipe(distinctUntilChanged());

  isCustomerGuest$ = this.store
    .select(selectIsCustomerGuest)
    .pipe(distinctUntilChanged());

  isCustomerLead$ = this.getAccessRights$.pipe(
    distinctUntilKeyChanged('granular'),
    map((accessRights) => accessRights?.granular?.isEmployeeOfLeadCustomer),
  );

  customer$ = this.store.select(selectCustomer).pipe(filter(Boolean));

  getBusinessCaseVisibilityForCustomer$ = this.store.select(
    selectBusinessCaseVisibilityForCustomer,
  );

  isCaseActive$ = this.store.select(selectIsBusinessCaseActive);

  isRealEstateTabOpened$ = this.store
    .select(selectIsRealEstateTabOpened)
    .pipe(
      observeOn(asapScheduler),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );

  showCadrDocumentsDownload$ = this.getBusinessCase$.pipe(
    map((businessCase) => {
      return (
        businessCase?.company &&
        Object.values(businessCase?.company?.information || {})?.some(
          (info) => info.field.fieldType === 'DOCUMENT' && info.value,
        )
      );
    }),
    distinctUntilChanged(),
  );

  caseParticipants$ = combineLatest([
    this.getBusinessCase$.pipe(distinctUntilKeyChanged('participants')),
    this.userCustomerKey$,
    this.getCustomerNameByKey$,
    this.getBusinessCasePermissions$,
  ]).pipe(
    takeUntilDestroyed(this.destroyRef),
    map(
      ([
        businessCase,
        userCustomerKey,
        customerNamesByKey,
        participantsPermissions,
      ]) => {
        return businessCase?.participants
          .filter((p) => p.customerKey !== userCustomerKey)
          .map((p) => {
            return {
              customerKey: p.customerKey,
              customerName: customerNamesByKey[p.customerKey]?.name,
              canEdit: participantsPermissions[
                p.customerKey
              ]?.permissions.includes(BusinessCasePermission.BCP_00016),
            };
          });
      },
    ),
  );
  getNeoGptVisibility$ = this.store.select(selectIsNeoGptVisible);

  // collaboration tab
  selectedCollaborationTab: CollaborationTab | null = null;
  selectedDataRoomTab: BusinessCaseDataRoomTab | null = null;
  timeout: NodeJS.Timeout;

  readonly businessCasePermission = BusinessCasePermission;

  private readonly tabs: Record<string, string> = {
    overview: 'overview',
    financingDetails: 'financing-details',
    dataRoom: 'data-room',
    collaboration: 'collaboration',
    participantStatus: 'participant-status',
    participants: 'participants',
    management: 'management',
    apply: 'apply',
    invitation: 'invitation',
  };

  businessCaseDashboardCardData$ = this.store.select(
    selectBusinessCaseDashboardCardData,
  );

  canChangeBusinessCaseState$ = this.store.select(
    selectHasAnyBusinessCasePermission([BusinessCasePermission.BCP_00034]),
  );

  canChangeBusinessCaseStatuses$ = this.store.select(
    selectHasAnyBusinessCasePermission([BusinessCasePermission.BCP_00149]),
  );

  canSeeAmountCollected$: Observable<boolean> = this.store.select(
    selectHasBusinessCasePermission(BusinessCasePermission.BCP_00010),
  );

  canSeeParticipationDistribution$: Observable<boolean> = this.store.select(
    selectHasBusinessCasePermission(BusinessCasePermission.BCP_00011),
  );

  isSharedOrOwnFinancingStructureTab$ = this.route.url;

  isOwnOrSharedFinancingStructureVisible$: Observable<boolean> = concat(
    of(this.router.url),
    this.router.events.pipe(
      filter((event) => event instanceof NavigationEnd),
      map((event) => {
        if (event instanceof NavigationEnd) {
          return event.url;
        }
        return '';
      }),
    ),
  ).pipe(
    map(
      (url) =>
        url.includes(FinancingDetailsPath.SHARED_FINANCING_STRUCTURE) ||
        url.includes(FinancingDetailsPath.FINANCING_STRUCTURE),
    ),
  );

  readonly headerHeight$ = this.store.select(selectBusinessCaseHeaderHeight);

  readonly getFinancindDetailsMiscCaseWithParticipantRealEstateVisibility$: Observable<boolean> =
    this.store.select(
      selectFinancindDetailsMiscCaseWithParticipantRealEstateVisibility,
    );

  constructor(
    private destroyRef: DestroyRef,
    public router: Router,
    private route: ActivatedRoute,
    private businessCaseModelService: BusinessCaseModelService,
    private store: Store<AppState>,
    // TODO: Revise and delete the whole service probably
    private tabNavigationService: BusinessCaseDashboardTabService,
    private modalService: ModalService,
    private socketService: SocketService,
    private participantControllerService: ParticipantControllerService,
    private activityLogRefreshService: ActivityLogService,
    private layoutCommunicationService: LayoutCommunicationService,
    private businessCaseLayoutService: BusinessCaseLayoutService,
  ) {}

  @TraceMethod({ name: 'businessCaseDashboardOnInit' })
  ngOnInit(): void {
    this.setSelectedTabIndex();
    this.hookIntoBusinessCaseUpdates();
    this.hookIntoNavigationEvents();
    this.hookIntoAccessRightsUpdates();
    this.hookIntoLoadedInvitationsApplicationsUpdates();
    this.hookIntoActiveNavigationItemsUpdates();
    this.hookIntoSelectedCollaborationUpdates();
    this.hookIntoSelectedDataRoomUpdates();
    this.getCallToActionsStatus();
    this.getCallerCustomerName();
    this.initializePortalSocket();
    this.hookIntoCaseAccessUpdates();
    this.getEditModeToggleState();
    this.hookIntoInvitations();
    this.addInvitedGuestToBusinessCase();
    this.getReasonsForClosingBusinessCase();
    this.hookIntoPollingUpdates();
  }

  ngOnDestroy() {
    clearTimeout(this.timeout);
    this.store.dispatch(
      StateLibBusinessCaseRealEstatePageActions.clearFinancialStructureCase(),
    );
    this.activityLogRefreshService.clearActivityLogInterval();
  }

  get isDataRoomTab() {
    return this.tabNavigationService.isTab(this.dataRoomPath);
  }

  onTabChanged(event: NgbNavChangeEvent) {
    this.latestSelectedTabId = null;
    this.selectedTabId = this.tabs[event.nextId];
    if (this.editMode) {
      this.store.dispatch(
        StateLibBusinessCasePageActions.updateEditTemplateMode({
          payload: false,
        }),
      );
    }
    event.preventDefault();
  }

  confirmAction(actionType: 'accept' | 'reject') {
    const confirmAcceptInvitationMessage = $localize`:@@confirm.accept.invitation.message:Sind Sie sicher, dass Sie diese Einladung annehmen wollen?`;
    const confirmRejectInvitationMessage = $localize`:@@confirm.reject.invitation.message:Sind Sie sicher, dass Sie diese Einladung ablehnen wollen?`;
    const confirmMessage =
      actionType === 'accept'
        ? confirmAcceptInvitationMessage
        : confirmRejectInvitationMessage;

    this.modalService.openComponent<ConfirmationDialogComponent>(
      ConfirmationDialogComponent,
      {
        message: confirmMessage,
        confirmButtonColor: 'primary',
        confirmLabel: $localize`:@@button.label.confirm:Ja, Ich bin sicher`,
        cancelLabel: $localize`:@@detachMasterCase.dialog.label.no:Nein`,
        svgIcon:
          actionType === 'accept'
            ? 'svgCorporateAcceptGroup'
            : 'svgCorporateCancelGroup',
      },
      {
        windowClass: 'confirmation-dialog',
      },
      (res) => {
        if (!res.success) {
          return;
        }
        if (actionType === 'accept') {
          this.store.dispatch(StateLibInvitationPageActions.acceptInvitation());
        } else {
          this.store.dispatch(
            StateLibInvitationPageActions.declineInvitation(),
          );
        }
      },
    );
  }

  redirectToCollaborationTab(customerKey: string) {
    void this.router.navigate(
      [
        customerKey,
        'business-case',
        this.businessCaseInfo?.id,
        'financing-details',
        'my-participation',
      ],
      {
        skipLocationChange: false,
      },
    );
  }

  openChat(chatMode: 'invitation' | 'apply') {
    this.tabNavigationService.openChat(chatMode);
  }

  onLoadMoreRevisions() {
    this.store.dispatch(ActivityLogsPageActions.loadMoreActivityLogs());
  }

  setSelectedTabInDataRoom(isCompanyDataRoomTab: boolean): void {
    if (isCompanyDataRoomTab) {
      this.store.dispatch(
        StateLibBusinessCasePageActions.updateEditTemplateMode({
          payload: false,
        }),
      );
    }
    this.isCompanyDataRoomTab = isCompanyDataRoomTab;
  }

  private hookIntoInvitations() {
    this.store
      .select(selectInvitations)
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((invitations) => (this.invitations = invitations));
  }

  private hookIntoActiveNavigationItemsUpdates() {
    this.store
      .select(selectActiveNavigationItem)
      .pipe(
        withLatestFrom(
          this.store.select(
            sideNavigationsFeature.selectNavigationAndChatAreOpen,
          ),
        ),
        tap(([navigationItem, navigationState]) => {
          if (navigationItem !== 'collaboration') {
            this.tabNavigationService.clearSelectedCollaborationTabs();
          }
          if (this.selectedTabId === undefined) {
            this.selectedTabId = this.tabs[navigationItem];
            this.latestSelectedTabId = this.selectedTabId;
          }
          if (
            navigationItem &&
            navigationItem !== BusinessCaseDashboardPath.OVERVIEW &&
            !navigationState.pageNavigationIsOpen
          ) {
            this.toggleSideNavigation(navigationState);
          }
          this.setSelectedTabIndex();
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }

  private hookIntoLoadedInvitationsApplicationsUpdates() {
    combineLatest([
      this.store
        .select(selectInvitationsLoaded)
        .pipe(takeUntilDestroyed(this.destroyRef), filter(Boolean)),
      this.store
        .select(selectApplicationsLoaded)
        .pipe(filter(Boolean), takeUntilDestroyed(this.destroyRef)),
    ])
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(() => {
        const pathToBusinessCaseDashboardTab =
          this.tabNavigationService.parsePathToBusinessCaseDashboardTab(
            this.router.url,
          );
        this.tabNavigationService.selectBusinessCaseDashboardTab(
          pathToBusinessCaseDashboardTab,
        );
      });
  }

  private hookIntoNavigationEvents() {
    this.router.events
      .pipe(
        filter((event) => event instanceof NavigationEnd),
        takeUntilDestroyed(this.destroyRef),
        map((e) => {
          return e as NavigationEnd;
        }),
      )
      .subscribe((event: NavigationEnd) => {
        if (this.isRealEstateCase && !event.url.includes('financing-details')) {
          this.store.dispatch(
            StateLibBusinessCaseRealEstatePageActions.activateIndividualCase(),
          );
        }

        const pathToBusinessCaseDashboardTab =
          this.tabNavigationService.parsePathToBusinessCaseDashboardTab(
            event.url,
          );
        this.tabNavigationService.selectBusinessCaseDashboardTab(
          pathToBusinessCaseDashboardTab,
        );

        this.store.dispatch(
          StateLibBusinessCasePageActions.setIsBusinessCaseHeaderInView({
            payload: true,
          }),
        );
      });
  }

  private hookIntoPollingUpdates() {
    combineLatest([this.getAccessRights$, this.getUserId$.pipe(take(1))])
      .pipe(debounceTime(2000), takeUntilDestroyed(this.destroyRef))
      .subscribe(([_, userId]) => {
        this.startDataPollingIfAllowed(userId);
      });
  }

  private startDataPollingIfAllowed(userId: string) {
    this.activityLogRefreshService.startStopRefreshInterval(
      this.access.activityLog.read,
      this.businessCase.id,
      userId,
    );
  }

  private hookIntoSelectedCollaborationUpdates() {
    combineLatest([
      this.store.select(selectSelectedCollaborationInvitationsApplications),
      this.store.select(selectBusinessCaseType),
    ])
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(([invitationsApplications, businessCaseType]) => {
        if (businessCaseType === BusinessCaseType.FINANCING_CASE) {
          this.selectedCollaborationTab = invitationsApplications
            ? 'INVITATIONS_APPLICATIONS'
            : 'MY_PARTNERS';
        } else {
          this.selectedCollaborationTab = 'INVITATIONS_APPLICATIONS';
        }
        this.store.dispatch(
          StateLibBusinessCasePageActions.setSelectedCollaborationTap({
            selectedCollaborationTab: this.selectedCollaborationTab,
          }),
        );
      });
  }

  private hookIntoSelectedDataRoomUpdates() {
    combineLatest([
      this.store
        .select(selectSelectedDataRoomCompany)
        .pipe(takeUntilDestroyed(this.destroyRef)),
      this.store
        .select(selectSelectedDataRoomCase)
        .pipe(takeUntilDestroyed(this.destroyRef)),
    ])
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(
        ([isCompanyDataRoomSelected, isBusinessCaseDataRoomSelected]) => {
          if (!isCompanyDataRoomSelected && !isBusinessCaseDataRoomSelected) {
            return;
          }
          if (isCompanyDataRoomSelected) {
            this.store.dispatch(
              StateLibCompanyPortalPageActions.setCompanySelectedTab({
                selectedTab: BusinessCaseDataRoomTab.COMPANY_DATA_ROOM,
              }),
            );
          } else {
            this.store.dispatch(
              StateLibCompanyPortalPageActions.setCompanySelectedTab({
                selectedTab: BusinessCaseDataRoomTab.CASE_DATA_ROOM,
              }),
            );
          }
        },
      );
  }

  private hookIntoAccessRightsUpdates() {
    combineLatest([this.getAccessRights$])
      .pipe(debounceTime(2000), takeUntilDestroyed(this.destroyRef))
      .subscribe(([_]) => {
        if (!this.access.dataRoom.write && this.isDataRoomTab) {
          this.editMode = false;
        }
      });
  }

  private hookIntoBusinessCaseUpdates() {
    combineLatest([
      this.getBusinessCase$,
      this.getUserToken$,
      this.getUsersById$,
      this.getBusinessCasePermissions$.pipe(startWith(null)),
    ])
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(
        ([businessCase, userToken, usersById, participantsPermissions]) => {
          if (participantsPermissions) {
            this.businessCaseInfo = {
              ...this.businessCaseModelService.toBusinessCaseViewModel(
                businessCase,
                participantsPermissions,
                userToken.customer_key,
              ),
            };
          }

          this.isRealEstateCase =
            businessCase.structuredFinancingConfiguration
              ?.financingStructureType === FinancingStructureType.REAL_ESTATE;

          this.customerUser = businessCase.participants?.length
            ? usersById[businessCase.participants[0].users[0].userId]
            : undefined;
        },
      );
  }

  private initializePortalSocket() {
    this.getBusinessCase$
      .pipe(
        distinctUntilKeyChanged('id'),
        tap(({ id }) => {
          this.socketService.initializeSocket(SocketType.PORTAL, {
            caseId: id,
          });

          this.socketService.joinRoomAndReceiveMessagesByDestination(
            id,
            PORTAL_SUBSCRIPTION_DESTINATION,
            SocketType.PORTAL,
          );
          this.subscribeToPortalMessages();
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }

  private hookIntoCaseAccessUpdates() {
    this.store
      .select(selectCaseFieldsAccess)
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(
        (caseFieldsAccess) =>
          (this.hasCompanyVisibleFields = !!caseFieldsAccess?.filter(
            (cfa) => cfa.active,
          )?.length),
      );
  }

  // TODO: move to somewhere else
  private subscribeToPortalMessages() {
    this.socketService
      .getMessagesByDestination$(
        PORTAL_SUBSCRIPTION_DESTINATION,
        SocketType.PORTAL,
      )
      .pipe(
        concatLatestFrom(() => [
          this.store.select(selectUserId),
          this.store.select(selectIsLead),
        ]),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe(([subscriptionMessage, userId, isLead]) => {
        const message = subscriptionMessage as PortalSocketMessage;

        if (message.type === PortalSocketMessageType.FIELD_ACCESS) {
          const fieldAccess = message.payload as CaseFieldAccess;
          this.store.dispatch(
            CompanyPortalPageActions.manageCaseFieldAccess({
              payload: fieldAccess,
            }),
          );
          return;
        }
        if (message.type === PortalSocketMessageType.REQUEST_INFORMATION) {
          const inputRequest = message.payload as CaseFieldInputRequest;
          if (
            inputRequest.participantUsersToBeNotified?.includes(userId) ||
            isLead
          ) {
            this.store.dispatch(
              CompanyPortalPageActions.manageCaseFieldInputRequest({
                payload: inputRequest,
              }),
            );
          }
          return;
        }
      });
  }

  private getEditModeToggleState() {
    this.store
      .select(selectEditTemplateMode)
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((mode: boolean) => (this.editMode = mode));
  }

  private getReasonsForClosingBusinessCase() {
    this.store.dispatch(BusinessCasePageActions.loadReasonsForClosingCase());
  }

  private getCallToActionsStatus() {
    combineLatest([
      this.customer$,
      this.store.select(selectPendingInvitation),
      this.isCustomerGuest$,
    ])
      .pipe(
        takeUntilDestroyed(this.destroyRef),
        filter(([customer]) => !!customer),
      )
      .subscribe(([{ customerType }, invitation, isCustomerGuest]) => {
        this.shouldShowCallToActions = isCustomerGuest && !isEmpty(invitation);
        this.isBankType = !!(customerType === CustomerType.BANK);
      });
  }

  private getCallerCustomerName() {
    combineLatest([
      this.store.select(selectPendingInvitation),
      this.getCustomerNameByKey$,
    ])
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(([invitation, customersByKey]) => {
        const callerCustomerKey = invitation?.callerCustomerKey;
        this.callerCustomerName = customersByKey[callerCustomerKey]?.name;
        this.businessCaseInvitation = invitation;
      });
  }

  private addInvitedGuestToBusinessCase() {
    this.customer$
      .pipe(
        filter(
          (customer) =>
            !!customer && customer?.customerStatus === CustomerStatus.GUEST,
        ),
        withLatestFrom(this.getUserId$),
        switchMap(([customer, userId]) => {
          const invitation = this.invitations.find(
            (i) =>
              i.invitationStatus === InvitationStatus.ACCEPTED &&
              i.invitedUsers?.find((u) => u.userId === userId) &&
              !this.businessCase.participants
                .find((c) => c.customerKey === customer.key)
                .users.find((u) => u.userId === userId),
          );

          if (invitation) {
            return this.participantControllerService
              .addParticipantUserToBusinessCase({
                businessCaseId: this.businessCase.id,
                userId,
              })
              .pipe(
                catchError((err) => {
                  console.error(err?.message);
                  return of({ users: [] });
                }),
              );
          }
          return of({ users: [] });
        }),
        withLatestFrom(this.getBusinessCase$.pipe(take(1))),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe(([participants, businessCase]) => {
        this.store.dispatch(
          StateLibUserPageActions.loadUsers({
            payload: participants.users.map((u) => u.userId),
          }),
        );
        this.store.dispatch(
          StateLibBusinessCasePageActions.loadBusinessCase({
            payload: businessCase.id,
          }),
        );
      });
  }

  public onStatusChanged(value: string) {
    this.store.dispatch(
      BusinessCasePageActions.changeBusinessCaseStatus({
        payload: {
          status: value,
          businessCaseId: this.route.snapshot.params.id,
        },
      }),
    );
  }

  public onCloseCase() {
    this.store.dispatch(
      BusinessCasePageActions.showCompleteBusinessCaseModal({
        payload: this.route.snapshot.params.id,
      }),
    );
  }

  public onReactivateCase() {
    this.store.dispatch(
      BusinessCasePageActions.showReactivateBusinessCaseModal({
        payload: this.route.snapshot.params.id,
      }),
    );
  }

  private setSelectedTabIndex() {
    // TODO - try using app-menu-items-list , like in collaboration -management
    const currentRoute = this.router.url;

    this.tabNames.forEach((tabName) => {
      if (currentRoute.toLocaleLowerCase().includes(tabName)) {
        this.selectedIndex = this.tabNames.indexOf(tabName);
      }
    });
  }

  private toggleSideNavigation({
    isChatVisible,
    platformNavigationIsOpen,
  }: Dictionary<boolean>): void {
    if (isChatVisible && platformNavigationIsOpen) {
      this.store.dispatch(
        StateLibSideNavigationsPageActions.toggleOpenForPlatformNavigation(),
      );
    }
    this.store.dispatch(
      StateLibSideNavigationsPageActions.toggleOpenForPageNavigation(),
    );
  }
}
