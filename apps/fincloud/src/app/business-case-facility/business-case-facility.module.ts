import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';

import { NgxSliderModule } from '@angular-slider/ngx-slider';
import { DragDropModule } from '@angular/cdk/drag-drop';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { AvatarComponent } from '@fincloud/components/avatar';
import { NsUiHintModule } from '@fincloud/components/hint';
import { NsUiIconsModule } from '@fincloud/components/icons';
import { NsUiOrganizationLogoRendererModule } from '@fincloud/components/organization-logo-renderer';
import { NsBusinessCaseRefactoringModule } from '@fincloud/neoshare/business-case';
import { NsDataRoomModule } from '@fincloud/neoshare/data-room';
import { NsDocumentModule } from '@fincloud/neoshare/document';
import { NsNeogptChatModule } from '@fincloud/neoshare/neogpt-chat';
import { NsTemplateFieldModule } from '@fincloud/neoshare/template-field';
import { NgxPermissionsModule } from 'ngx-permissions';
import { NgScrollbarModule } from 'ngx-scrollbar';
import { BusinessCaseCadrModule } from '../business-case-cadr/business-case-cadr.module';
import { BusinessCaseFacilityRoutingModule } from './business-case-facility-routing.module';

@NgModule({
  declarations: [],
  imports: [
    CommonModule,
    FormsModule,
    NsUiHintModule,
    NsUiOrganizationLogoRendererModule,
    DragDropModule,
    NsUiIconsModule,
    NgScrollbarModule,
    ReactiveFormsModule,
    NsBusinessCaseRefactoringModule,
    NgxSliderModule,
    AvatarComponent,
    NsDataRoomModule,
    NsDocumentModule,
    NsTemplateFieldModule,
    BusinessCaseCadrModule,
    NgxPermissionsModule.forChild(),
    NsNeogptChatModule,
    BusinessCaseFacilityRoutingModule,
    NsBusinessCaseRefactoringModule,
  ],
})
export class BusinessCaseFacilityModule {}
