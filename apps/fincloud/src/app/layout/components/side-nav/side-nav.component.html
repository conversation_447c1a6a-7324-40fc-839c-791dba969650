@if (navigationsAreOpen$ | async; as open) {
  <ul class="nav flex-column nav-pills">
    <li>
      <ui-menu-item
        routerLinkText="company-portal"
        icon="home"
        navTitle="Dashboard"
        [isCollapsed]="!open.platformNavigationIsOpen"
      ></ui-menu-item>
    </li>

    @if (showCasesBeforeDashboard$ | async) {
      <ng-template [ngTemplateOutlet]="casesMenuItem"> </ng-template>
    }

    <li class="nav-item">
      <ui-menu-item
        routerLinkText="dashboard"
        icon="dashboard"
        navTitle="Dashboard"
        i18n-navTitle="@@sidenav.items.dashboard"
        [isCollapsed]="!open.platformNavigationIsOpen"
      ></ui-menu-item>
    </li>

    @if (showCasesBeforeDashboard$ | async | isFalsy) {
      <ng-template [ngTemplateOutlet]="casesMenuItem"> </ng-template>
    }

    <li class="nav-item">
      <ui-menu-item
        routerLinkText="company-management"
        icon="business"
        [navTitle]="companiesPageNavLinkLabel$ | async"
        [isCollapsed]="!open.platformNavigationIsOpen"
      ></ui-menu-item>
    </li>

    <li class="nav-item">
      <ui-menu-item
        icon="document"
        navTitle="Verträge"
        i18n-navTitle="@@sidenav.items.documents"
        routerLinkText="contract-management"
        [isCollapsed]="!open.platformNavigationIsOpen"
      ></ui-menu-item>
    </li>

    <li class="nav-item">
      <ui-menu-item
        icon="user-management"
        navTitle="Nutzerverwaltung"
        i18n-navTitle="@@sidenav.items.userManagement"
        routerLinkText="user-management"
        [isCollapsed]="!open.platformNavigationIsOpen"
      ></ui-menu-item>
    </li>

    <li class="nav-item">
      <ui-menu-item
        icon="svgCustomerMasterData"
        navTitle="Organisationsdaten"
        i18n-navTitle="@@sidenav.items.customerOrganisationData"
        routerLinkText="customer-master-data"
        [isCollapsed]="!open.platformNavigationIsOpen"
      ></ui-menu-item>
    </li>

    <li class="nav-item">
      <ui-menu-item
        icon="template"
        navTitle="Vorlagen"
        i18n-navTitle="@@sidenav.items.businessCaseTemplateManagement"
        routerLinkText="template-management"
        [isCollapsed]="!open.platformNavigationIsOpen"
      ></ui-menu-item>
    </li>

    @if (isUserAdmin$ | async) {
      <li class="nav-item">
        <ui-menu-item
          icon="integration-instructions"
          navTitle="Apps"
          i18n-navTitle="@@sidenav.items.apps"
          routerLinkText="apps-integration"
          [isCollapsed]="!open.platformNavigationIsOpen"
        ></ui-menu-item>
      </li>
    }

    <li class="nav-item">
      <ui-menu-item
        icon="balance"
        navTitle="Kunden"
        i18n-navTitle="@@sidenav.items.accountManagement.customers"
        routerLinkText="account-management/customers"
        [isCollapsed]="!open.platformNavigationIsOpen"
      ></ui-menu-item>
    </li>

    <!-- <li class="nav-item">
  <ui-menu-item
    icon="person"
    navTitle="Nutzer"
    i18n-navTitle="@@sidenav.items.accountManagement.users"
    routerLinkText="account-management/users"
  ></ui-menu-item>
  </li> -->

    <li class="nav-item">
      <ui-menu-item
        icon="task"
        navTitle="Nutzungsverträge"
        i18n-navTitle="@@sidenav.items.accountManagement.usageContracts"
        routerLinkText="account-management/usage-contracts"
        [isCollapsed]="!open.platformNavigationIsOpen"
      ></ui-menu-item>
    </li>

    <li class="nav-item">
      <ui-menu-item
        icon="svgEditDocument"
        navTitle="Signaturübersicht"
        i18n-navTitle="@@sidenav.items.signature"
        routerLinkText="digital-signature"
        [isCollapsed]="!open.platformNavigationIsOpen"
      ></ui-menu-item>
    </li>

    <li class="nav-item">
      <ui-menu-item
        icon="receipt"
        navTitle="Abrechnung"
        i18n-navTitle="@@sidenav.items.receipt"
        routerLinkText="billing-management"
        [isCollapsed]="!open.platformNavigationIsOpen"
      ></ui-menu-item>
    </li>

    <li class="nav-item">
      <ui-menu-item
        icon="svgClusterDemo"
        navTitle="Snapshots"
        i18n-navTitle="@@sidenav.items.demos"
        routerLinkText="demo-snapshot"
        [isCollapsed]="!open.platformNavigationIsOpen"
      ></ui-menu-item>
    </li>
  </ul>

  <ng-template #casesMenuItem>
    <li class="nav-item">
      <ui-menu-item
        routerLinkText="cases"
        icon="list_alt"
        navTitle="Fälle"
        i18n-navTitle="@@sidenav.items.cases"
        [isCollapsed]="!open.platformNavigationIsOpen"
      ></ui-menu-item>
    </li>
  </ng-template>
}
