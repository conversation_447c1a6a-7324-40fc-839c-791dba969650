<ul class="nav flex-column nav-pills tw-pt-[2.4rem] tw-px-[1.2rem]">
  <li>
    <app-side-nav-menu-item
      routerLinkText="company-portal"
      iconName="home"
      navTitle="Dashboard"
      [collapsed]="collapsed"
    >
    </app-side-nav-menu-item>
  </li>

  @if (showCasesBeforeDashboard$ | async) {
    <ng-template [ngTemplateOutlet]="casesMenuItem"> </ng-template>
  }

  <li class="nav-item">
    <app-side-nav-menu-item
      routerLinkText="dashboard"
      iconName="dashboard"
      navTitle="Dashboard"
      i18n-navTitle="@@sidenav.items.dashboard"
      [collapsed]="collapsed"
    ></app-side-nav-menu-item>
  </li>

  @if (showCasesBeforeDashboard$ | async | isFalsy) {
    <ng-template [ngTemplateOutlet]="casesMenuItem"> </ng-template>
  }

  <li class="nav-item">
    <app-side-nav-menu-item
      routerLinkText="company-management"
      iconName="business"
      [navTitle]="companiesPageNavLinkLabel$ | async"
      [collapsed]="collapsed"
    ></app-side-nav-menu-item>
  </li>

  <li class="nav-item">
    <app-side-nav-menu-item
      iconName="description"
      navTitle="Verträge"
      i18n-navTitle="@@sidenav.items.documents"
      routerLinkText="contract-management"
      [collapsed]="collapsed"
    ></app-side-nav-menu-item>
  </li>

  <li class="nav-item">
    <app-side-nav-menu-item
      iconName="manage_accounts"
      navTitle="Nutzerverwaltung"
      i18n-navTitle="@@sidenav.items.userManagement"
      routerLinkText="user-management"
      [collapsed]="collapsed"
    ></app-side-nav-menu-item>
  </li>

  <li class="nav-item">
    <app-side-nav-menu-item
      iconSrc="assets/svg/svgCustomerMasterData.svg"
      navTitle="Organisationsdaten"
      i18n-navTitle="@@sidenav.items.customerOrganisationData"
      routerLinkText="customer-master-data"
      [collapsed]="collapsed"
    ></app-side-nav-menu-item>
  </li>

  <li class="nav-item">
    <app-side-nav-menu-item
      iconName="copy_all"
      navTitle="Vorlagen"
      i18n-navTitle="@@sidenav.items.businessCaseTemplateManagement"
      routerLinkText="template-management"
      [collapsed]="collapsed"
    ></app-side-nav-menu-item>
  </li>

  @if (isUserAdmin$ | async) {
    <li class="nav-item">
      <app-side-nav-menu-item
        iconName="integration_instructions"
        navTitle="Apps"
        i18n-navTitle="@@sidenav.items.apps"
        routerLinkText="apps-integration"
        [collapsed]="collapsed"
      ></app-side-nav-menu-item>
    </li>
  }

  <li class="nav-item">
    <app-side-nav-menu-item
      iconName="account_balance"
      navTitle="Kunden"
      i18n-navTitle="@@sidenav.items.accountManagement.customers"
      routerLinkText="account-management/customers"
      [collapsed]="collapsed"
    ></app-side-nav-menu-item>
  </li>

  <!-- <li class="nav-item">
    <app-side-nav-menu-item
      icon="person"
      navTitle="Nutzer"
      i18n-navTitle="@@sidenav.items.accountManagement.users"
      routerLinkText="account-management/users"
      [collapsed]="collapsed"
    ></app-side-nav-menu-item>
  </li> -->

  <li class="nav-item">
    <app-side-nav-menu-item
      iconName="task"
      navTitle="Nutzungsverträge"
      i18n-navTitle="@@sidenav.items.accountManagement.usageContracts"
      routerLinkText="account-management/usage-contracts"
      [collapsed]="collapsed"
    ></app-side-nav-menu-item>
  </li>

  <li class="nav-item">
    <app-side-nav-menu-item
      iconName="edit_document"
      navTitle="Signaturübersicht"
      i18n-navTitle="@@sidenav.items.signature"
      routerLinkText="digital-signature"
      [collapsed]="collapsed"
    ></app-side-nav-menu-item>
  </li>

  <li class="nav-item">
    <app-side-nav-menu-item
      iconName="receipt"
      navTitle="Abrechnung"
      i18n-navTitle="@@sidenav.items.receipt"
      routerLinkText="billing-management"
      [collapsed]="collapsed"
    ></app-side-nav-menu-item>
  </li>

  <li class="nav-item">
    <app-side-nav-menu-item
      iconSrc="assets/svg/svgClusterDemo.svg"
      navTitle="Snapshots"
      i18n-navTitle="@@sidenav.items.demos"
      routerLinkText="demo-snapshot"
      [collapsed]="collapsed"
    ></app-side-nav-menu-item>
  </li>
</ul>

<ng-template #casesMenuItem>
  <li class="nav-item">
    <app-side-nav-menu-item
      routerLinkText="cases"
      iconName="list_alt"
      navTitle="Fälle"
      i18n-navTitle="@@sidenav.items.cases"
      [collapsed]="collapsed"
    ></app-side-nav-menu-item>
  </li>
</ng-template>
