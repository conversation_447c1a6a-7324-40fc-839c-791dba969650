import { Component } from '@angular/core';
import { DeleteTemplateFieldModalComponent } from '@fincloud/components/modals';
import {
  BusinessCaseLayoutService,
  FaqComponent,
} from '@fincloud/neoshare/business-case';
import {
  selectBusinessCaseContentHeightAdministration,
  selectBusinessCaseId,
  selectBusinessCaseWrapperHeightAdministration,
  selectIsBusinessCaseActive,
} from '@fincloud/state/business-case';
import { StateLibFaqPageActions, faqFeature } from '@fincloud/state/faq';
import { Faq } from '@fincloud/swagger-generator/business-case-manager';
import { FinButtonAppearance } from '@fincloud/ui/button';
import { FinAccordionTogglePosition } from '@fincloud/ui/expansion-panel';
import { FinModalService } from '@fincloud/ui/modal';
import { FinSize } from '@fincloud/ui/types';
import { Store } from '@ngrx/store';
import { filter, shareReplay, tap } from 'rxjs/operators';

@Component({
  selector: 'app-administration-faq-page',
  templateUrl: './administration-faq.component.html',
  styleUrl: './administration-faq.component.scss',
})
export class AdministrationFaqComponent {
  isBusinessCaseActive$ = this.store
    .select(selectIsBusinessCaseActive)
    .pipe(shareReplay({ refCount: true, bufferSize: 1 }));

  businessCaseId$ = this.store.select(selectBusinessCaseId);
  faqs$ = this.store
    .select(faqFeature.selectFaq)
    .pipe(shareReplay({ refCount: true, bufferSize: 1 }));

  readonly finSize = FinSize;
  readonly finAccordionTogglePosition = FinAccordionTogglePosition;
  readonly finButtonAppearance = FinButtonAppearance;

  readonly actionsButtonLabel = $localize`:@@dashboard.businessCase.administration.faq.label.addQuestion:Frage hinzufügen`;
  readonly sectionTitle = $localize`:@@dashboard.businessCase.administration.sectionTitle.FAQ:FAQ`;

  readonly contentWrapperHeight$ = this.store.select(
    selectBusinessCaseWrapperHeightAdministration,
  );

  readonly contentHeight$ = this.store.select(
    selectBusinessCaseContentHeightAdministration,
  );

  constructor(
    private store: Store,
    private finModalService: FinModalService,
    private businessCaseLayoutService: BusinessCaseLayoutService,
  ) {}

  manageQuestion(faq?: Faq) {
    this.finModalService.open(FaqComponent, {
      data: faq,
      size: this.finSize.S,
      hasBackdrop: true,
      disableClose: true,
    });
  }

  deleteFaq(faq: Faq) {
    this.finModalService
      .open(DeleteTemplateFieldModalComponent, {
        data: {
          deleteMessage: $localize`:@@dashboard.businessCase.administration.deleteFaq:Möchten Sie diese Frage löschen?`,
          deleteSubMessage: '',
        },
        size: this.finSize.S,
        hasBackdrop: true,
        disableClose: true,
      })
      .afterClosed()
      .pipe(
        filter(Boolean),
        tap(() => {
          this.store.dispatch(
            StateLibFaqPageActions.deleteFaq({
              faqId: faq.id,
            }),
          );
        }),
      )
      .subscribe();
  }
}
