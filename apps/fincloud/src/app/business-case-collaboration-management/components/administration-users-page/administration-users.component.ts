import { Clipboard } from '@angular/cdk/clipboard';
import { Component, DestroyRef, OnInit } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { BusinessCaseAdministrationUser } from '@fincloud/core/business-case';
import { Toast } from '@fincloud/core/toast';
import {
  BusinessCaseLayoutService,
  LOCALIZED_SALUTATION,
} from '@fincloud/neoshare/business-case';
import {
  StateLibBusinessCasePageActions,
  selectAdministrationTabUsers,
  selectBusinessCaseContentHeightAdministration,
  selectBusinessCaseId,
  selectBusinessCaseWrapperHeightAdministration,
  selectCanPlatformManagerAddHimself,
  selectIsBusinessCaseActive,
} from '@fincloud/state/business-case';
import { selectCustomerKey } from '@fincloud/state/customer';
import {
  ContactPersonControllerService,
  ParticipantControllerService,
} from '@fincloud/swagger-generator/business-case-manager';
import { FinButtonActionType, FinButtonShape } from '@fincloud/ui/button';
import { FinModalService } from '@fincloud/ui/modal';
import { FinTableColumn } from '@fincloud/ui/table';
import { FinToastService } from '@fincloud/ui/toast';
import { FinSize } from '@fincloud/ui/types';
import { COPY_MAIL_MESSAGE } from '@fincloud/utils';
import { Store } from '@ngrx/store';
import { Observable, iif, of, takeLast, tap } from 'rxjs';
import {
  distinctUntilChanged,
  filter,
  shareReplay,
  switchMap,
} from 'rxjs/operators';
import { UserContactStatus } from '../../models/user-contact-status';
import { ADMINISTRATION_USER_COLUMNS } from '../../utils/administration-user-columns';
import { TRANSLATED_ACADEMIC_TITLE } from '../../utils/translate-academic-title';
import { AddUserParticipantComponent } from '../add-user-participant/add-user-participant.component';

@Component({
  selector: 'app-administration-users',
  templateUrl: './administration-users.component.html',
  styleUrl: './administration-users.component.scss',
})
export class AdministrationUsersComponent implements OnInit {
  readonly finButtonShape = FinButtonShape;
  readonly finButtonActionType = FinButtonActionType;
  readonly finSize = FinSize;
  readonly actionsButtonLabel = $localize`:@@dashboard.businessCase.administration.companyContactUsers.addUserLabel:Nutzer hinzufügen`;
  readonly tableTitle = $localize`:@@accountManagement.users:Nutzer`;

  businessCaseId: string;
  customerKey: string;
  columns: FinTableColumn[] = ADMINISTRATION_USER_COLUMNS;

  tableUsers$ = this.store.select(selectAdministrationTabUsers);
  isPlatformManageStillNotPartOfCase$ = this.store.select(
    selectCanPlatformManagerAddHimself,
  );

  isBusinessCaseActive$: Observable<boolean> = this.store
    .select(selectIsBusinessCaseActive)
    .pipe(shareReplay({ bufferSize: 1, refCount: true }));

  readonly contentWrapperHeight$ = this.store.select(
    selectBusinessCaseWrapperHeightAdministration,
  );

  readonly contentHeight$ = this.store.select(
    selectBusinessCaseContentHeightAdministration,
  );

  constructor(
    private destroyRef: DestroyRef,
    private participantControllerService: ParticipantControllerService,
    private finToastService: FinToastService,
    private contactPersonControllerService: ContactPersonControllerService,
    private finModalService: FinModalService,
    private store: Store,
    private clipboard: Clipboard,
    private businessCaseLayoutService: BusinessCaseLayoutService,
  ) {}

  addUserAsParticipantOrContactPerson() {
    this.finModalService.open(AddUserParticipantComponent, {
      size: FinSize.S,
      disableClose: true,
    });
  }

  translateSalutation(user: BusinessCaseAdministrationUser) {
    return user.attributes.salutation
      ? LOCALIZED_SALUTATION[user.attributes.salutation]
      : '';
  }

  translateAcademicTitle(user: BusinessCaseAdministrationUser) {
    return user.attributes.academicTitle
      ? TRANSLATED_ACADEMIC_TITLE[user.attributes.academicTitle]
      : '';
  }

  ngOnInit(): void {
    this.store
      .select(selectCustomerKey)
      .pipe(
        distinctUntilChanged(),
        takeUntilDestroyed(this.destroyRef),
        filter(Boolean),
        tap((customerKey) => (this.customerKey = customerKey)),
      )
      .subscribe();

    this.store
      .select(selectBusinessCaseId)
      .pipe(
        distinctUntilChanged(),
        takeUntilDestroyed(this.destroyRef),
        filter(Boolean),
        tap((businessCaseId) => (this.businessCaseId = businessCaseId)),
      )
      .subscribe();
  }

  userStatusChanged({ isContactPerson, userId }: UserContactStatus) {
    of(isContactPerson)
      .pipe(
        switchMap((isContactPerson) =>
          iif(
            () => isContactPerson,
            this.contactPersonControllerService.deleteSingleContactPerson({
              businessCaseId: this.businessCaseId,
              userId,
            }),
            this.contactPersonControllerService.addContactPersons({
              businessCaseId: this.businessCaseId,
              body: [userId],
            }),
          ),
        ),
        takeLast(1),
      )
      .subscribe({
        next: () => {
          return this.store.dispatch(
            StateLibBusinessCasePageActions.loadBusinessCase({
              payload: this.businessCaseId,
            }),
          );
        },
        error: () => this.finToastService.show(Toast.error()),
      });
  }

  userRemoved(contactPerson: UserContactStatus) {
    of(contactPerson.isContactPerson)
      .pipe(
        switchMap((isContactPerson) =>
          iif(
            () => isContactPerson,
            this.contactPersonControllerService
              .deleteSingleContactPerson({
                businessCaseId: this.businessCaseId,
                userId: contactPerson.userId,
              })
              .pipe(
                switchMap(() =>
                  this.participantControllerService.removeUserFromBusinessCase({
                    businessCaseId: this.businessCaseId,
                    userId: contactPerson.userId,
                  }),
                ),
              ),
            this.participantControllerService.removeUserFromBusinessCase({
              businessCaseId: this.businessCaseId,
              userId: contactPerson.userId,
            }),
          ),
        ),
      )
      .subscribe({
        next: () => {
          return this.store.dispatch(
            StateLibBusinessCasePageActions.loadBusinessCase({
              payload: this.businessCaseId,
            }),
          );
        },
        error: () => this.finToastService.show(Toast.error()),
      });
  }

  copyEmail(email: string): void {
    this.clipboard.copy(email);
    this.finToastService.show(Toast.success(COPY_MAIL_MESSAGE));
  }
}
