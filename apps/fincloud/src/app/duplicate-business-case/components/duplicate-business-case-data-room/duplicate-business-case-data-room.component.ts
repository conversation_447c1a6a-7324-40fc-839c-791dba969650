import {
  Component,
  DestroyRef,
  EventEmitter,
  Input,
  OnInit,
  Output,
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormControl, FormGroup } from '@angular/forms';
import {
  DataRoomGroupsFieldsService,
  GroupTemplateFields,
  InformationUtils,
  TemplateFieldViewModel,
} from '@fincloud/core/business-case';
import {
  StateLibInboxDocumentsPageActions,
  selectInboxDocuments,
} from '@fincloud/state/inbox-documents';
import {
  User,
  UserManagementControllerService,
} from '@fincloud/swagger-generator/authorization-server';
import {
  FieldDto,
  Group,
} from '@fincloud/swagger-generator/business-case-manager';
import { ExchangeBusinessCase } from '@fincloud/swagger-generator/exchange';
import { MultiselectOption } from '@fincloud/types/models';
import { Store } from '@ngrx/store';
import { cloneDeep, keyBy } from 'lodash-es';
import { catchError, map, merge, of, toArray } from 'rxjs';
import { InboxDocument } from '../../models/inbox-document';

@Component({
  selector: 'app-duplicate-business-case-data-room',
  templateUrl: './duplicate-business-case-data-room.component.html',
  styleUrls: ['./duplicate-business-case-data-room.component.scss'],
})
export class DuplicateBusinessCaseDataRoomComponent implements OnInit {
  @Input()
  businessCase: ExchangeBusinessCase;

  @Output()
  sendSelectedInboxDocuments = new EventEmitter<string[]>();

  @Output()
  sendSelectedGroupFieldsValues = new EventEmitter<string[]>();

  @Output()
  // TODO: Rename - only controls groupVisibility
  sendGroups = new EventEmitter<Group[]>();

  inboxDocuments: MultiselectOption<InboxDocument>[];
  groupsTemplateFields: GroupTemplateFields[];

  dataRoomForm = new FormGroup({
    groupVisibility: new FormControl(true),
    inboxDocumentsSelected: new FormControl(),
    copyInformationForFieldKeys: new FormControl(),
  });

  templateFields: TemplateFieldViewModel;
  copyGroupsTemplateFields: GroupTemplateFields[];
  copyInboxDocuments: MultiselectOption<InboxDocument>[];

  constructor(
    private destroyRef: DestroyRef,
    private userManagementControllerService: UserManagementControllerService,
    private store: Store,
    private businessCaseGroupsFieldsService: DataRoomGroupsFieldsService,
  ) {}

  ngOnInit(): void {
    this.store.dispatch(
      StateLibInboxDocumentsPageActions.loadInboxDocuments({
        payload: { businessCaseId: this.businessCase.id },
      }),
    );
    this.store
      .select(selectInboxDocuments)
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((documents) => {
        this.inboxDocuments = documents.map((document) => {
          return {
            label: document.id,
            value: {
              value: document.id,
              documentName: document.contentReference.fileName,
              userId: document.userId,
              createdOn: document.createdOn,
            },
          };
        });
        this.dataRoomForm.patchValue({
          inboxDocumentsSelected: this.inboxDocuments?.map(
            (option) => option?.value?.value,
          ),
        });
        const userInfo$ = this.inboxDocuments.map((document) => {
          return this.getUserInfo(document.value.userId).pipe(
            map((user) => {
              document.value.fullName = user.username;
              return user;
            }),
          );
        });

        this.sendSelectedInboxDocuments.emit(
          this.dataRoomForm.get('inboxDocumentsSelected')?.value,
        );

        merge(...userInfo$)
          .pipe(toArray())
          .subscribe(() => {
            this.copyInboxDocuments = cloneDeep(this.inboxDocuments);
          });
      });

    this.reinitializeGroups();

    this.sendGroups.emit(
      this.businessCase.businessCaseTemplate.template.groupsOrdered,
    );
  }

  private reinitializeGroups() {
    const information = keyBy(
      Object.values(this.businessCase.information),
      'key',
    );
    this.groupsTemplateFields =
      this.businessCaseGroupsFieldsService.getGroupsTemplateFields(
        this.businessCase.businessCaseTemplate.template?.fields,
        this.businessCase.businessCaseTemplate.template.groupsOrdered,
        information,
        null,
      );
    this.copyGroupsTemplateFields = cloneDeep(this.groupsTemplateFields);

    this.dataRoomForm.patchValue({
      copyInformationForFieldKeys:
        this.businessCase.businessCaseTemplate.template?.fields?.map(
          (field) => field.key,
        ),
    });
    this.sendSelectedGroupFieldsValues.emit(
      this.dataRoomForm.get('copyInformationForFieldKeys')?.value,
    );
  }

  getUserInfo(userId: string) {
    return this.userManagementControllerService
      .getUserById({
        userId: userId,
      })
      .pipe(
        map((user) => {
          const username = user.firstName + ' ' + user.lastName;
          return { ...user, username };
        }),
      )
      .pipe(catchError(() => of({ firstName: '', lastName: '' } as User)));
  }

  isExpressionField(field: FieldDto) {
    return field?.expression?.length;
  }

  hasValue(templateField: TemplateFieldViewModel) {
    return (
      !!templateField.information?.value?.toString().length &&
      !InformationUtils.isEmptyValue(templateField.information?.value)
    );
  }

  searchGroupOrField(searchExpression: string) {
    const searchTerm = searchExpression;

    if (searchTerm) {
      const matchedGroups: GroupTemplateFields[] = [];
      const matchedFields: TemplateFieldViewModel[] = [];
      for (const group of this.groupsTemplateFields) {
        const matchedGroup = cloneDeep(group);
        matchedGroup.fields = [];
        matchedGroup.documents = [];
        matchedGroup.templateFields = [];

        for (const field of group.templateFields) {
          if (
            field.field.label
              .toLocaleLowerCase()
              .includes(searchTerm.toLocaleLowerCase())
          ) {
            matchedGroup.templateFields.push(field);
            matchedFields.push(field);
          }
        }
        for (const field of group.documents) {
          if (
            field.field.label
              .toLocaleLowerCase()
              .includes(searchTerm.toLocaleLowerCase())
          ) {
            matchedGroup.documents.push(field);
            matchedFields.push(field);
          }
        }

        if (
          matchedGroup.templateFields?.length ||
          matchedGroup.documents?.length
        ) {
          matchedGroups.push(matchedGroup);
        }
      }

      const filteredOptions = matchedGroups;
      if (filteredOptions.length === 0) {
        this.copyGroupsTemplateFields = [];
      } else {
        this.copyGroupsTemplateFields = filteredOptions;
      }
    } else {
      this.copyGroupsTemplateFields = cloneDeep(this.groupsTemplateFields);
    }
  }

  filterDocuments(searchExpression: Event | string) {
    const searchTerm = searchExpression.toString();
    if (searchTerm) {
      this.copyInboxDocuments = this.inboxDocuments.filter((item) => {
        const { createdOn, documentName, fullName } = item.value;
        return (
          createdOn.includes(searchTerm) ||
          documentName.includes(searchTerm) ||
          fullName.includes(searchTerm)
        );
      });
    } else {
      this.copyInboxDocuments = cloneDeep(this.inboxDocuments);
    }
  }

  setSelectedInboxDocuments(inboxDocuments: { [x: string]: boolean }) {
    const selectedInboxDocuments = Object.keys(inboxDocuments).filter(
      (key) => inboxDocuments[key] === true,
    );
    this.sendSelectedInboxDocuments.emit(selectedInboxDocuments);
  }

  setSelectedGroupFieldsValue(copyInformationForFieldKeys: {
    [x: string]: boolean;
  }) {
    const selectedFieldKeys = Object.keys(copyInformationForFieldKeys).filter(
      (key) => copyInformationForFieldKeys[key] === true,
    );
    this.sendSelectedGroupFieldsValues.emit(selectedFieldKeys);
  }

  changeGroupVisibility() {
    const groups = cloneDeep(
      this.businessCase.businessCaseTemplate.template?.groupsOrdered,
    );
    if (!this.dataRoomForm.get('groupVisibility')?.value) {
      for (const group of groups) {
        if (
          group.groupVisibility?.visibility === 'INVITEE_AND_APPLICANT' ||
          group.groupVisibility?.visibility === 'PARTICIPANT'
        ) {
          const customerKeys = this.businessCase?.participants
            .filter((participant) => !participant.lead)
            .map((participant) => participant?.customerKey);

          group.groupVisibility.currentParticipantsWithVisibility = group
            .groupVisibility.visibleForNewParticipants
            ? customerKeys
            : [];
        }
      }
    }
    this.sendGroups.emit(groups);
  }
}
