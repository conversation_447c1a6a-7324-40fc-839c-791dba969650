<div class="collaboration-container" [formGroup]="duplicateCollaborationForm">
  <div class="meine-partner-container">
    <div class="title" i18n="@@duplicateCollaboration.myPartners.header">
      Meine Partner
    </div>
    <ui-expanded-checkbox-select-list
      formControlName="myPartners"
      [options]="allParticipants"
      [hasScrollbar]="false"
      [hasSearch]="false"
      [optionTemplate]="expandedListOptionTemplate"
      (onCheckboxChange)="setParticipantsAndInvitees($event)"
    ></ui-expanded-checkbox-select-list>
  </div>
  @if (criteria?.length) {
    <div class="applicant-criteria-container">
      <div
        class="title"
        i18n="@@duplicateCollaboration.applicantCriteria.header"
      >
        Bewerberkriterien & Teilnehmeranforderungen
      </div>
      <ui-expanded-checkbox-select-list
        formControlName="criteria"
        [options]="criteria"
        [hasScrollbar]="false"
        [hasSearch]="false"
        [optionTemplate]="criteriaListOptionTemplate"
        (onCheckboxChange)="setCriteria($event)"
      ></ui-expanded-checkbox-select-list>
    </div>
  }
</div>

<ng-template #criteriaListOptionTemplate let-item="item">
  @if (item.value?.question) {
    <ui-truncated-text class="criteria">
      {{ item.value?.question }}
    </ui-truncated-text>
  } @else {
    <div class="criteria-container">
      <div class="criteria-title">
        {{ item.label }}
      </div>
      @if (
        item.value?.minParticipationAmount || item.value?.maxParticipationAmount
      ) {
        <div class="amount">
          @if (item.value?.minParticipationAmount) {
            <span>
              Min.
              {{
                item.value?.minParticipationAmount
                  | currency
                  | removeTrailingZeros
              }}
            </span>
          }
          @if (
            item.value?.minParticipationAmount &&
            item.value?.maxParticipationAmount
          ) {
            <span> - </span>
          }
          @if (item.value?.maxParticipationAmount) {
            <span>
              Max.
              {{
                item.value?.maxParticipationAmount
                  | currency
                  | removeTrailingZeros
              }}
            </span>
          }
        </div>
      }
    </div>
  }
</ng-template>

<ng-template #expandedListOptionTemplate let-item="item">
  <div class="customers-container">
    @if (item.value.lead) {
      <ui-toggle-panel expandedStyle="dark-darker" [showToggleIcon]="true">
        <div header class="customer-header">
          <ui-icon name="svgLeaderCustomerAvatar"></ui-icon>
          <div class="customer-title heading4">
            <ui-truncated-text class="customer-name">
              {{
                customerKeyNames[item.value.customerKey]?.name
              }}</ui-truncated-text
            >
            @if (
              item.value?.totalParticipationAmount !== null &&
              item.value?.participationAmountToggle
            ) {
              <span class="customer-amount">
                {{ totalParticipationLabel }}:
                {{
                  item.value?.totalParticipationAmount
                    | currency
                    | removeTrailingZeros
                }}
              </span>
            }
          </div>
          @if (partnersUsers[item?.value.customerKey]?.length) {
            <ui-participating-users-representation
              [showCounter]="false"
              size="medium-l"
              [currentUserId]="currentUserId"
              [title]="
                getTranslatedLabel(
                  customerKeyNames[item.value.customerKey]?.name
                )
              "
              [partnerUsers]="partnersUsers[item.value.customerKey]"
              bckgColor="background"
              [duplicateCase]="true"
              (emitSelectedPartnerUsers)="setSelectedUsers($event)"
            ></ui-participating-users-representation>
          }
        </div>
        <div content class="panel-content">
          <ui-business-case-roles
            [participantPermissionSet]="
              (participantContextEntities$ | async)[item.value.customerKey]
            "
            [userParticipantAmount]="item.value?.totalParticipationAmount"
            [readOnlyAndHidden]="true"
            [isDuplicateCase]="true"
            [readOnly]="true"
            [selectedBusinessCaseType]="selectedBusinessCaseType"
            (updateAmount)="updateParticipantAmount($event)"
          >
          </ui-business-case-roles>
        </div>
      </ui-toggle-panel>
    }
    @if (!item.value.lead) {
      <ui-toggle-panel expandedStyle="dark-darker">
        <div header class="customer-header">
          <ui-icon
            #participatingCustomerAvatar
            name="svgParticipatingCustomerAvatar"
          ></ui-icon>
          <div class="customer-title heading4">
            <ui-truncated-text class="customer-name">
              {{
                customerKeyNames[item.value.customerKey]?.name
              }}</ui-truncated-text
            >
            @if (
              item?.value?.totalParticipationAmount !== null &&
              !isInvitee(item?.value.customerKey) &&
              item?.value.participationAmountToggle
            ) {
              <span class="customer-amount"
                >{{ totalParticipationLabel }}:
                {{
                  item.value?.totalParticipationAmount
                    | currency
                    | removeTrailingZeros
                }}
              </span>
            }
          </div>
          @if (partnersUsers[item?.value.customerKey]?.length) {
            <ui-participating-users-representation
              [showCounter]="false"
              size="medium-l"
              [title]="
                getTranslatedLabel(
                  customerKeyNames[item.value.customerKey]?.name
                )
              "
              [partnerUsers]="partnersUsers[item.value.customerKey]"
              bckgColor="background"
              [duplicateCase]="true"
              (emitSelectedPartnerUsers)="setSelectedUsers($event)"
              [isParticipantAsInvitee]="isParticipantAsInvitee"
              [participantCustomerKey]="
                customerKeyNames[item.value.customerKey]?.key
              "
            >
            </ui-participating-users-representation>
          }
        </div>
        <div content class="panel-content">
          @if (customerKeyNames[item.value.customerKey]) {
            <ui-business-case-roles
              [participantPermissionSet]="
                (participantContextEntities$ | async)[item.value.customerKey]
              "
              [userParticipantAmount]="item.value?.totalParticipationAmount"
              [customerType]="customerType"
              [participantName]="customerKeyNames[item.value.customerKey]?.name"
              [readOnlyAndHidden]="true"
              [isDuplicateCase]="true"
              [readOnly]="true"
              [selectedBusinessCaseType]="selectedBusinessCaseType"
              (updateAmount)="updateParticipantAmount($event)"
              (setParticipantType)="setParticipantType($event)"
            >
            </ui-business-case-roles>
          }
        </div>
      </ui-toggle-panel>
    }
  </div>
</ng-template>
