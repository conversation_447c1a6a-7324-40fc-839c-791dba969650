import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';

import { NgxSliderModule } from '@angular-slider/ngx-slider';
import { DragDropModule } from '@angular/cdk/drag-drop';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { AvatarComponent } from '@fincloud/components/avatar';
import { NsUiButtonsModule } from '@fincloud/components/buttons';
import { NsUiHintModule } from '@fincloud/components/hint';
import { NsUiHorizontalDividerModule } from '@fincloud/components/horizontal-divider';
import { NsUiIconsModule } from '@fincloud/components/icons';
import { NsUiNumberModule } from '@fincloud/components/number';
import { NsUiOrganizationLogoRendererModule } from '@fincloud/components/organization-logo-renderer';
import { NsUiTextModule } from '@fincloud/components/text';
import { NsUiTruncatedTextModule } from '@fincloud/components/truncated-text';
import { NsCoreLayoutModule } from '@fincloud/core/layout';
import { NsCorePipesModule } from '@fincloud/core/pipes';
import { NsBusinessCaseRefactoringModule } from '@fincloud/neoshare/business-case';
import { NsBusinessCaseCollaborationModule } from '@fincloud/neoshare/business-case-collaboration';
import { NsDataRoomModule } from '@fincloud/neoshare/data-room';
import { NsDocumentModule } from '@fincloud/neoshare/document';
import { NsNeogptChatModule } from '@fincloud/neoshare/neogpt-chat';
import { NsTemplateFieldModule } from '@fincloud/neoshare/template-field';
import { FinActionsMenuModule } from '@fincloud/ui/actions-menu';
import { FinAvatarModule } from '@fincloud/ui/avatar-default';
import { FinBadgesModule } from '@fincloud/ui/badges';
import { FinButtonModule } from '@fincloud/ui/button';
import { FinCardLabelModule } from '@fincloud/ui/card-label';
import { FinDropdownModule } from '@fincloud/ui/dropdown';
import { FinEmptyStateModule } from '@fincloud/ui/empty-state';
import { FinIconModule } from '@fincloud/ui/icon';
import { FinLoaderModule } from '@fincloud/ui/loader';
import { FinMenuItemModule } from '@fincloud/ui/menu-item';
import { FinModalModule } from '@fincloud/ui/modal';
import { FinScrollbarModule } from '@fincloud/ui/scrollbar';
import { FinSidePanelModule } from '@fincloud/ui/side-panel';
import { FinTableModule } from '@fincloud/ui/table';
import { FinTabsModule } from '@fincloud/ui/tabs';
import { FinTooltipModule } from '@fincloud/ui/tooltip';
import { FinTruncateTextModule } from '@fincloud/ui/truncate-text';
import { NgxPermissionsModule } from 'ngx-permissions';
import { NgScrollbarModule } from 'ngx-scrollbar';
import { BusinessCaseCadrModule } from '../business-case-cadr/business-case-cadr.module';
import { BusinessCaseCollaborationEffects } from './+state/effects/business-case-collaboration.effects';
import { BusinessCaseCollaborationRoutingModule } from './business-case-collaboration-routing.module';
import { CollaborationApplicationsInvitationsKebabMenuComponent } from './components/collaboration-applications-invitations-kebab-menu/collaboration-applications-invitations-kebab-menu.component';
import { CollaborationApplicationsInvitationsTableComponent } from './components/collaboration-applications-invitations-table/collaboration-applications-invitations-table.component';
import { CollaborationApplicationsInvitationsComponent } from './components/collaboration-applications-invitations/collaboration-applications-invitations.component';
import { CollaborationFilterComponent } from './components/collaboration-filter/collaboration-filter.component';
import { CollaborationInvitationsResultModalComponent } from './components/collaboration-invitations-result-modal/collaboration-invitations-result-modal.component';
import { CollaborationMyPartnersComponent } from './components/collaboration-my-partners/collaboration-my-partners.component';
import { CollaborationUserOptionItemComponent } from './components/collaboration-user-option-item/collaboration-user-option-item.component';
import { BusinessCaseCollaborationComponent } from './components/collaboration/business-case-collaboration.component';
import { CriteriaComponent } from './components/criteria/criteria.component';

@NgModule({
  declarations: [
    BusinessCaseCollaborationComponent,
    CollaborationMyPartnersComponent,
    CollaborationApplicationsInvitationsTableComponent,
    CollaborationFilterComponent,
    CollaborationApplicationsInvitationsComponent,
    CollaborationApplicationsInvitationsKebabMenuComponent,
    CollaborationUserOptionItemComponent,
    CollaborationInvitationsResultModalComponent,
    CriteriaComponent,
  ],
  imports: [
    CommonModule,
    FormsModule,
    NsUiHintModule,
    NsUiOrganizationLogoRendererModule,
    DragDropModule,
    NsUiIconsModule,
    NgScrollbarModule,
    ReactiveFormsModule,
    FinTabsModule,
    NsBusinessCaseRefactoringModule,
    NgxSliderModule,
    AvatarComponent,
    NsDataRoomModule,
    NgScrollbarModule,
    NsDocumentModule,
    NsTemplateFieldModule,
    BusinessCaseCadrModule,
    NgxPermissionsModule.forChild(),
    NsNeogptChatModule,
    BusinessCaseCollaborationRoutingModule,
    NsBusinessCaseRefactoringModule,
    NsBusinessCaseCollaborationModule,
    FinCardLabelModule,
    FinDropdownModule,
    FinSidePanelModule,
    FinIconModule,
    FinButtonModule,
    FinTableModule,
    FinTooltipModule,
    FinAvatarModule,
    FinActionsMenuModule,
    FinLoaderModule,
    FinBadgesModule,
    NsCorePipesModule,
    NsCoreLayoutModule,
    NsUiNumberModule,
    NsUiHorizontalDividerModule,
    NsUiIconsModule,
    NsUiTruncatedTextModule,
    NsUiButtonsModule,
    NsUiTextModule,
    FinEmptyStateModule,
    FinModalModule,
    FinTruncateTextModule,
    FinMenuItemModule,
    FinScrollbarModule,
  ],
  providers: [BusinessCaseCollaborationEffects],
})
export class BusinessCaseCollaborationModule {}
