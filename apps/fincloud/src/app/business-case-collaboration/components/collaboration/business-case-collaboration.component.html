@if (businessCase$ | async; as businessCase) {
  @if (collaborationTab$ | async; as collaborationTab) {
    <fin-tabs
      class="tw-sticky tw-top-0 tw-z-[2]"
      [type]="finTabType.SECONDARY"
      [selectedIndex]="selectedCollaborationsTabIndex"
    >
      @if (businessCase?.businessCaseType === businessCaseType.FINANCING_CASE) {
        <fin-tab
          *ngxPermissionsOnly="[
            businessCasePermission.BCP_00024,
            businessCasePermission.BCP_00027,
            businessCasePermission.BCP_00028,
          ]"
        >
          <ng-template finTabLabel>
            <a
              [routerLink]="[
                '/',
                customerKey,
                'business-case',
                businessCase?.id,
                'collaboration',
                'my-partners',
              ]"
              routerLinkActive="active"
              i18n="@@dashboard.businessCase.collaboration.tab.myPartners"
              (click)="navigationChange('MY_PARTNERS')"
              >Teilnehmer
            </a>
          </ng-template>
        </fin-tab>
      }

      <fin-tab *ngxPermissionsOnly="[businessCasePermission.BCP_00025]">
        <ng-template finTabLabel>
          <a
            [routerLink]="[
              '/',
              customerKey,
              'business-case',
              businessCase?.id,
              'collaboration',
              'invitations-applications',
            ]"
            (click)="navigationChange('INVITATIONS_APPLICATIONS')"
            routerLinkActive="active"
            i18n="
              @@dashboard.businessCase.Collaboration.tab.invitationApplications"
            >Einladungen und Bewerbungen</a
          >
        </ng-template>
      </fin-tab>
    </fin-tabs>

    @if (collaborationTab) {
      @switch (collaborationTab) {
        @case ('MY_PARTNERS') {
          <app-collaboration-my-partners></app-collaboration-my-partners>
        }
        @case ('INVITATIONS_APPLICATIONS') {
          <div>
            <app-collaboration-applications-invitations
              [businessCase]="businessCase"
              [invitations]="invitations"
              [applications]="applications"
              [customerType]="customerType"
            ></app-collaboration-applications-invitations>
          </div>
        }
      }
    }
  }
}
