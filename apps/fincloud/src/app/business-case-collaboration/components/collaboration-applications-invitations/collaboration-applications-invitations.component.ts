import {
  Component,
  Input,
  OnChanges,
  OnInit,
  SimpleChanges,
} from '@angular/core';
import { ModalService } from '@fincloud/core/modal';
import { Toast } from '@fincloud/core/toast';
import { BusinessCaseLayoutService } from '@fincloud/neoshare/business-case';
import {
  ApplicationsInvitationsHelperService,
  CollaborationInvitationModalComponent,
} from '@fincloud/neoshare/business-case-collaboration';
import {
  selectBusinessCaseContentHeightCollaborationInvitations,
  selectBusinessCaseWrapperHeightCollaborationInvitations,
  selectCustomerNamesByKey,
} from '@fincloud/state/business-case';
import {
  Application,
  Invitation,
} from '@fincloud/swagger-generator/application';
import { Customer } from '@fincloud/swagger-generator/authorization-server';
import { ExchangeBusinessCase } from '@fincloud/swagger-generator/exchange';
import { BusinessCasePermission, CustomerType } from '@fincloud/types/enums';
import {
  ApplicationOrInvitationInfo,
  Dictionary,
} from '@fincloud/types/models';
import { FinModalService } from '@fincloud/ui/modal';
import { FinToastService } from '@fincloud/ui/toast';
import { FinSize } from '@fincloud/ui/types';
import { Store } from '@ngrx/store';
import {
  EMPTY,
  catchError,
  filter,
  forkJoin,
  switchMap,
  take,
  tap,
} from 'rxjs';
import { sortByStatusValue } from '../../utils/sort-by-status-value';
import { CollaborationInvitationsResultModalComponent } from '../collaboration-invitations-result-modal/collaboration-invitations-result-modal.component';

@Component({
  selector: 'app-collaboration-applications-invitations',
  templateUrl: './collaboration-applications-invitations.component.html',
  styleUrls: ['./collaboration-applications-invitations.component.scss'],
  providers: [ApplicationsInvitationsHelperService],
})
export class CollaborationApplicationsInvitationsComponent
  implements OnInit, OnChanges
{
  @Input() businessCase: ExchangeBusinessCase;
  @Input() invitations: Invitation[];
  @Input() applications: Application[];
  @Input() customerType: CustomerType;

  public originalApplicationOrInvitationRows: ApplicationOrInvitationInfo[] =
    [];
  public filteredApplicationsInvitationsRows: ApplicationOrInvitationInfo[] =
    [];

  public isLoadingTableRows = true;
  public reapplyFilters = false;

  public customers: Dictionary<Customer> = {};
  public showNoMatchedFilterResults = false;

  readonly businessCasePermission = BusinessCasePermission;

  readonly contentWrapperHeight$ = this.store.select(
    selectBusinessCaseWrapperHeightCollaborationInvitations,
  );

  readonly contentHeight$ = this.store.select(
    selectBusinessCaseContentHeightCollaborationInvitations,
  );

  constructor(
    private modalService: ModalService,
    private applicationsInvitationsHelperService: ApplicationsInvitationsHelperService,
    private finToastService: FinToastService,
    private store: Store,
    private finModalService: FinModalService,
    private businessCaseLayoutService: BusinessCaseLayoutService,
  ) {}

  ngOnInit() {
    this.loadInvitationsAndApplications()
      .pipe(take(1))
      .subscribe(() => {
        this.isLoadingTableRows = false;
      });

    this.store
      .select(selectCustomerNamesByKey)
      .pipe(
        filter((c) => !!Object.keys(c)?.length),
        take(1),
      )
      .subscribe((customers) => {
        this.customers = customers;
      });
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (
      !this.isLoadingTableRows &&
      (changes.invitations || changes.applications) &&
      !changes.invitations.firstChange &&
      !changes.applications.firstChange
    ) {
      this.loadInvitationsAndApplications().subscribe(() => {
        this.isLoadingTableRows = false;
        this.reapplyFilters = true;
      });
    }
  }

  private loadInvitationsAndApplications() {
    const applications$ =
      this.applicationsInvitationsHelperService.setApplicationsInfo(
        this.applications,
        this.originalApplicationOrInvitationRows,
        this.businessCase?.id,
      );
    const invitations$ =
      this.applicationsInvitationsHelperService.setInvitationsInfo(
        this.invitations,
        this.originalApplicationOrInvitationRows,
      );
    return forkJoin([applications$, invitations$]).pipe(
      tap(([applicationsInfo, invitationsInfo]) => {
        const filteredApplications = applicationsInfo.filter(
          (app) => app.canShowApplicationInPreparation,
        );

        this.filteredApplicationsInvitationsRows = [
          ...filteredApplications,
          ...invitationsInfo,
        ]
          .sort(sortByStatusValue)
          .map((row) => {
            return {
              isRegistered: true,
              ...row,
            };
          });
        this.originalApplicationOrInvitationRows = [
          ...filteredApplications,
          ...invitationsInfo,
        ]
          .sort(sortByStatusValue)
          .map((row) => {
            return {
              isRegistered: true,
              ...row,
            };
          });
      }),
    );
  }

  public onOpenInvitationModalViaKebabMenu(
    tableRow?: ApplicationOrInvitationInfo,
  ) {
    if (tableRow) {
      this.openInvitationsModal(true, tableRow);
    } else {
      this.openInvitationsModal(false);
    }
  }

  private addOrUpdateInvitationInApplicationsAndInvitationsTable(
    selectedForInvite: ApplicationOrInvitationInfo,
  ) {
    this.finModalService
      .open<
        ApplicationOrInvitationInfo,
        CollaborationInvitationsResultModalComponent
      >(CollaborationInvitationsResultModalComponent, {
        data: { invitationResult: selectedForInvite },
        disableClose: true,
        size: FinSize.S,
      })
      .afterClosed()
      .subscribe((resp) => {
        const alreadyAddedOrganisation =
          this.originalApplicationOrInvitationRows.findIndex(
            (organisation) => organisation.customerKey === resp.customerKey,
          );

        if (alreadyAddedOrganisation !== -1) {
          this.originalApplicationOrInvitationRows[alreadyAddedOrganisation] =
            resp;
          this.originalApplicationOrInvitationRows = [
            ...this.originalApplicationOrInvitationRows,
          ];
        } else {
          this.originalApplicationOrInvitationRows = [
            ...this.originalApplicationOrInvitationRows,
            resp,
          ];
        }

        this.reapplyFilters = true;
      });
  }

  public applyUpdatedRows(updatedRows: {
    updatedRows: ApplicationOrInvitationInfo[];
    appliedFilters: boolean;
  }) {
    if (!updatedRows.updatedRows.length && updatedRows.appliedFilters) {
      this.showNoMatchedFilterResults = true;
      this.filteredApplicationsInvitationsRows = [];
    } else if (updatedRows.updatedRows.length) {
      this.filteredApplicationsInvitationsRows = [
        ...updatedRows.updatedRows,
      ].map((row) => {
        return {
          isRegistered: true,
          ...row,
        };
      });
      this.showNoMatchedFilterResults = false;
    } else {
      this.filteredApplicationsInvitationsRows = [
        ...this.originalApplicationOrInvitationRows,
      ];
      this.showNoMatchedFilterResults = false;
    }
    this.reapplyFilters = false;
  }

  public clearFilters(hasCleared: boolean) {
    if (hasCleared) {
      this.filteredApplicationsInvitationsRows = [
        ...this.originalApplicationOrInvitationRows,
      ];
    }
  }

  public closeModal() {
    this.modalService.closeActiveModals();
  }

  private openInvitationsModal(
    isOpenViaKebabMenu: boolean,
    row?: ApplicationOrInvitationInfo,
  ) {
    if (this.isLoadingTableRows) {
      return;
    }

    this.finModalService
      .open<Invitation, CollaborationInvitationModalComponent>(
        CollaborationInvitationModalComponent,
        {
          data: {
            applicationOrInvitationInfo: row,
            businessCase: this.businessCase,
            alreadyAddedOrganisation: this.originalApplicationOrInvitationRows,
            isOpenViaKebabMenu: isOpenViaKebabMenu,
          },
          size: FinSize.M,
          disableClose: true,
        },
      )
      .afterClosed()
      .pipe(
        filter(Boolean),
        switchMap((resp) => {
          return this.applicationsInvitationsHelperService
            .processInvitation(resp)
            .pipe(
              catchError(() => {
                this.finModalService.closeAll();
                this.finToastService.show(Toast.error());
                return EMPTY;
              }),
              tap((invitation) =>
                this.addOrUpdateInvitationInApplicationsAndInvitationsTable(
                  invitation as unknown as ApplicationOrInvitationInfo,
                ),
              ),
            );
        }),
      )
      .subscribe();
  }
}
