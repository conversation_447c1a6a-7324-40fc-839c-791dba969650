import { Component, DestroyRef, OnInit } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import {
  selectBusinessCase,
  selectHasBusinessCasePermission,
  selectIsBusinessCaseActive,
} from '@fincloud/state/business-case';
import { selectCustomerType } from '@fincloud/state/customer';
import { ExchangeBusinessCase } from '@fincloud/swagger-generator/exchange';
import { BusinessCasePermission, CustomerType } from '@fincloud/types/enums';
import { AppState } from '@fincloud/types/models';
import { Store } from '@ngrx/store';
import { cloneDeep } from 'lodash-es';
import { combineLatest, distinctUntilChanged, filter } from 'rxjs';

@Component({
  selector: 'app-collaboration-my-partners',
  templateUrl: './collaboration-my-partners.component.html',
  styleUrls: ['./collaboration-my-partners.component.scss'],
})
export class CollaborationMyPartnersComponent implements OnInit {
  businessCase: ExchangeBusinessCase;
  customerType: CustomerType;
  isCaseActive$ = this.store.select(selectIsBusinessCaseActive);

  readonly businessCasePermission = BusinessCasePermission;

  canSeeParticipants$ = this.store
    .select(selectHasBusinessCasePermission(BusinessCasePermission.BCP_00024))
    .pipe(takeUntilDestroyed(this.destroyRef));

  hasIndividualSettingsPermission$ = this.store
    .select(selectHasBusinessCasePermission(BusinessCasePermission.BCP_00028))
    .pipe(takeUntilDestroyed(this.destroyRef));

  constructor(
    private destroyRef: DestroyRef,
    private store: Store<AppState>,
  ) {}

  ngOnInit(): void {
    combineLatest([
      this.store.select(selectBusinessCase).pipe(filter((c) => !!c)),
      this.store
        .select(selectCustomerType)
        .pipe(filter(Boolean), distinctUntilChanged()),
    ])
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(([businessCase, customerType]) => {
        this.businessCase = cloneDeep(businessCase);
        this.customerType = customerType;
      });
  }
}
