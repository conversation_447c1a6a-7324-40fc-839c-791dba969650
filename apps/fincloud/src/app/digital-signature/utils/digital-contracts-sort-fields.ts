import { SortField } from '@fincloud/types/models';

export const DIGITAL_CONTRACTS_SORT_FIELDS: SortField[] = [
  {
    name: 'signingProcess.creationDate',
    displayValue: $localize`:@@contract.table.list.header.createdOn:Erstellt am`,
    show: true,
  },
  {
    name: 'createdBy',
    displayValue: $localize`:@@contract.table.list.header.createdBy:<PERSON><PERSON><PERSON><PERSON> von`,
    show: true,
  },
  {
    name: 'status',
    displayValue: $localize`:@@dashboard.businessCase.collaboration.applications.table.columns.state:Status`,
    show: true,
  },
  {
    name: 'title',
    displayValue: $localize`:@@digitalSignature.sortFields.title:Dateiname`,
    show: true,
  },
  {
    name: 'dueDate',
    displayValue: $localize`:@@accountManagement.usageContract.createUsageContractModal.controlLabel.dueDate:Unterzeichnungstag`,
    show: true,
  },
];
