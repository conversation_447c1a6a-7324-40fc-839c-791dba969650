import { Injectable } from '@angular/core';
import {
  FolderContentBaseServiceDefinition,
  FolderDeleteModalComponent,
  FolderDetailsComponent,
} from '@fincloud/neoshare/folder-structure';
import { StateLibFolderStructureFolderPageActions } from '@fincloud/state/folder-structure';
import { StateLibUsersPageActions } from '@fincloud/state/users';
import {
  FieldDto,
  Folder,
} from '@fincloud/swagger-generator/business-case-manager';
import { FinModalService } from '@fincloud/ui/modal';
import { FinSize } from '@fincloud/ui/types';
import { DEFAULT_USER_NAME } from '@fincloud/utils';
import { Store } from '@ngrx/store';
import { filter } from 'rxjs';

@Injectable()
export class BusinessCaseDataRoomFolderContentBaseService
  implements FolderContentBaseServiceDefinition
{
  constructor(
    private finModalService: FinModalService,
    private store: Store,
  ) {}

  // Folder related methods

  showFolderDetails(folder: Folder): void {
    this.store.dispatch(
      StateLibUsersPageActions.loadUsers({
        payload: {
          userIds: [folder.createdById, folder.updatedById].filter(
            (userId) => userId !== DEFAULT_USER_NAME,
          ),
        },
      }),
    );

    this.finModalService.open(FolderDetailsComponent, {
      data: folder,
      size: FinSize.M,
    });
  }

  renameFolder(folder: Folder, groupKey: string): void {
    this.store.dispatch(
      StateLibFolderStructureFolderPageActions.openRenameFolderModal({
        groupKey,
        name: folder.name,
        id: folder.id,
      }),
    );
  }

  moveFolder(folderId: string, groupKey: string): void {
    this.store.dispatch(
      StateLibFolderStructureFolderPageActions.openMoveFolderModal({
        folderId,
        groupKey,
      }),
    );
  }

  deleteFolder(folderId: string, groupKey: string): void {
    this.finModalService
      .open(FolderDeleteModalComponent)
      .afterClosed()
      .pipe(filter(Boolean))
      .subscribe(() => {
        this.store.dispatch(
          StateLibFolderStructureFolderPageActions.deleteFolder({
            folderId,
            folderGroupKey: groupKey,
          }),
        );
      });
  }

  // Document related methods
  moveDocument(documentField: FieldDto, groupKey: string): void {
    this.store.dispatch(
      StateLibFolderStructureFolderPageActions.openMoveDocumentModal({
        documentField,
        groupKey,
      }),
    );
  }
}
