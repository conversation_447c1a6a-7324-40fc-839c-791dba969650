import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';

import { NgxSliderModule } from '@angular-slider/ngx-slider';
import { DragDropModule } from '@angular/cdk/drag-drop';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { AvatarComponent } from '@fincloud/components/avatar';
import { NsUiButtonsModule } from '@fincloud/components/buttons';
import { NsUiGroupVisibilityStatusInfoModule } from '@fincloud/components/group-visibility-status-info';
import { NsUiHintModule } from '@fincloud/components/hint';
import { NsUiHorizontalDividerModule } from '@fincloud/components/horizontal-divider';
import { NsUiIconsModule } from '@fincloud/components/icons';
import { NsUiLayoutModule } from '@fincloud/components/layout';
import { NsUiModalsModule } from '@fincloud/components/modals';
import { NsUiOrganizationLogoRendererModule } from '@fincloud/components/organization-logo-renderer';
import { NsCoreDateModule } from '@fincloud/core/date';
import { NsCoreDirectivesModule } from '@fincloud/core/directives';
import { NsCoreLayoutModule } from '@fincloud/core/layout';
import { NsCorePipesModule } from '@fincloud/core/pipes';
import { NsCoreScrollModule } from '@fincloud/core/scroll';
import { NsBusinessCaseRefactoringModule } from '@fincloud/neoshare/business-case';
import { NsDataRoomModule } from '@fincloud/neoshare/data-room';
import { NsDocumentModule } from '@fincloud/neoshare/document';
import { NsNeogptChatModule } from '@fincloud/neoshare/neogpt-chat';
import { NsTemplateFieldModule } from '@fincloud/neoshare/template-field';
import { FinActionsMenuModule } from '@fincloud/ui/actions-menu';
import { FinButtonModule } from '@fincloud/ui/button';
import { FinCardLabelModule } from '@fincloud/ui/card-label';
import { FinEmptyStateModule } from '@fincloud/ui/empty-state';
import {
  FinAccordionComponent,
  FinExpansionPanelComponent,
  FinExpansionPanelModule,
} from '@fincloud/ui/expansion-panel';
import { FinFieldMessageModule } from '@fincloud/ui/field-message';
import { FinIconModule } from '@fincloud/ui/icon';
import { FinInputModule } from '@fincloud/ui/input';
import {
  FIN_MODAL_DEFAULT_OPTIONS,
  FIN_MODAL_REF_PROVIDER,
  FinModalModule,
  FinModalService,
} from '@fincloud/ui/modal';
import { FinScrollbarModule } from '@fincloud/ui/scrollbar';
import { FinSearchModule } from '@fincloud/ui/search';
import { FinSeparatorsModule } from '@fincloud/ui/separators';
import { FinSlideToggleModule } from '@fincloud/ui/slide-toggle';
import { FinSwitchToggleModule } from '@fincloud/ui/switch-toggle';
import { FinTabsModule } from '@fincloud/ui/tabs';
import { FinToolbarModule } from '@fincloud/ui/toolbar';
import { FinTreeMenuModule } from '@fincloud/ui/tree-menu';
import { FinTruncateTextModule } from '@fincloud/ui/truncate-text';
import { provideEffects } from '@ngrx/effects';
import { NgxPermissionsModule } from 'ngx-permissions';
import { NgScrollbarModule } from 'ngx-scrollbar';
import { BusinessCaseCadrModule } from '../business-case-cadr/business-case-cadr.module';
import { BusinessCaseTeaserModule } from '../business-case-teaser/business-case-teaser.module';
import { FolderStructureModule } from '../folder-structure/folder-structure.module';
import { BusinessCaseDataRoomEffects } from './+state';
import { BusinessCaseDataRoomRoutingModule } from './business-case-data-room-routing.module';
import { BusinessCaseDataRoomTabsComponent } from './components/business-case-data-room-tabs/business-case-data-room-tabs.component';
import { BusinessCaseDataRoomComponent } from './components/business-case-data-room/business-case-data-room.component';
import { DataRoomDeleteGroupModalComponent } from './components/data-room-delete-group-modal/data-room-delete-group-modal.component';
import { DataRoomGroupsSideBarComponent } from './components/data-room-groups-side-bar/data-room-groups-side-bar.component';
import { DataRoomManageGroupModalComponent } from './components/data-room-manage-group-modal/data-room-manage-group-modal.component';

@NgModule({
  declarations: [
    BusinessCaseDataRoomComponent,
    BusinessCaseDataRoomTabsComponent,
    DataRoomGroupsSideBarComponent,
    DataRoomManageGroupModalComponent,
    DataRoomDeleteGroupModalComponent,
  ],
  imports: [
    CommonModule,
    FormsModule,
    NsUiHintModule,
    NsUiOrganizationLogoRendererModule,
    FinInputModule,
    NsCoreDirectivesModule,
    DragDropModule,
    NsUiIconsModule,
    NgScrollbarModule,
    FinAccordionComponent,
    FinExpansionPanelComponent,
    FinSearchModule,
    FinEmptyStateModule,
    FinExpansionPanelModule,
    FinScrollbarModule,
    ReactiveFormsModule,
    FinSwitchToggleModule,
    NgxSliderModule,
    FinCardLabelModule,
    AvatarComponent,
    FinSlideToggleModule,
    NsUiLayoutModule,
    FinTabsModule,
    NsDataRoomModule,
    FinSeparatorsModule,
    NsDocumentModule,
    NsTemplateFieldModule,
    FinToolbarModule,
    BusinessCaseCadrModule,
    NgxPermissionsModule.forChild(),
    NsNeogptChatModule,
    BusinessCaseDataRoomRoutingModule,
    NsBusinessCaseRefactoringModule,
    NsCoreDateModule,
    FinButtonModule,
    FinIconModule,
    NsUiModalsModule,
    FinActionsMenuModule,
    BusinessCaseTeaserModule,
    FolderStructureModule,
    FinTreeMenuModule,
    FinTruncateTextModule,
    NsCoreDirectivesModule,
    NsCoreLayoutModule,
    NsCoreScrollModule,
    NsCorePipesModule,
    NsUiGroupVisibilityStatusInfoModule,
    NsUiHorizontalDividerModule,
    NsUiIconsModule,
    NsUiButtonsModule,
    FinModalModule,
    FinFieldMessageModule,
    FinEmptyStateModule,
  ],
  providers: [
    { provide: FIN_MODAL_DEFAULT_OPTIONS, useValue: {} },
    FIN_MODAL_REF_PROVIDER,
    FinModalService,
    provideEffects(BusinessCaseDataRoomEffects),
  ],
})
export class BusinessCaseDataRoomModule {}
