import { Injectable } from '@angular/core';
import { DateService } from '@fincloud/core/date';
import { Toast } from '@fincloud/core/toast';
import { BusinessCaseModalService } from '@fincloud/neoshare/business-case-fields';
import {
  ManageFolderModalComponent,
  MoveFileModalComponent,
} from '@fincloud/neoshare/folder-structure';
import { selectAccessRights } from '@fincloud/state/access';
import {
  StateLibBusinessCasePageActions,
  selectBusinessCase,
  selectBusinessCaseInformation,
} from '@fincloud/state/business-case';
import { selectGroupsOrdered } from '@fincloud/state/data-room';
import {
  StateLibFolderStructureDocumentPageActions,
  StateLibFolderStructureFolderPageActions,
} from '@fincloud/state/folder-structure';
import { selectUserCustomerKey } from '@fincloud/state/user';
import {
  Group,
  GroupDto,
} from '@fincloud/swagger-generator/business-case-manager';
import { CadrGroup } from '@fincloud/swagger-generator/company';
import { FinModalService } from '@fincloud/ui/modal';
import { FinToastService } from '@fincloud/ui/toast';
import { FinSize } from '@fincloud/ui/types';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { concatLatestFrom } from '@ngrx/operators';
import { Store } from '@ngrx/store';
import {
  catchError,
  defaultIfEmpty,
  filter,
  map,
  of,
  switchMap,
  takeUntil,
  tap,
} from 'rxjs';
import { DataRoomDeleteGroupModalComponent } from '../../components/data-room-delete-group-modal/data-room-delete-group-modal.component';
import { DataRoomManageGroupModalComponent } from '../../components/data-room-manage-group-modal/data-room-manage-group-modal.component';
import {
  BusinessCaseDataRoomApiActions,
  BusinessCaseDataRoomPageActions,
} from '../actions';

@Injectable()
export class BusinessCaseDataRoomEffects {
  readonly finSize = FinSize;

  showAddGroupDataRoomModal$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(BusinessCaseDataRoomPageActions.dataRoomAddGroupModal),
      concatLatestFrom(() => [
        this.store.select(selectAccessRights),
        this.store.select(selectUserCustomerKey),
      ]),
      switchMap(
        ([
          { businessCase, isRepresentingLeadPartner },
          access,
          customerKey,
        ]) => {
          const modalRef = this.finModalService.open<
            (GroupDto | CadrGroup)[],
            DataRoomManageGroupModalComponent
          >(DataRoomManageGroupModalComponent, {
            data: {
              groupsOrdered:
                businessCase.businessCaseTemplate.template.groupsOrdered || [],
              isBusinessCaseDataRoom: true,
              businessCase,
              isCustomerParticipant:
                access.granular.isRepresentingCurrentUserCustomer &&
                !access.granular.isRepresentingLeadPartner,
              userCustomerKey: customerKey,
              isRepresentingLeadPartner,
            },
            size: this.finSize.S,
            disableClose: true,
          });

          return modalRef.afterClosed().pipe(
            filter((resp) => resp && !!resp.length),
            map((groupsManaged) =>
              BusinessCaseDataRoomPageActions.dataRoomAddGroupModalConfirm({
                groupsManaged,
              }),
            ),
            defaultIfEmpty(
              BusinessCaseDataRoomPageActions.dataRoomAddGroupModalReject(),
            ),
          );
        },
      ),
    );
  });

  showManageGroupDataRoomModalConfirm$ = createEffect(() =>
    this.actions$.pipe(
      ofType(
        BusinessCaseDataRoomPageActions.dataRoomAddGroupModalConfirm,
        BusinessCaseDataRoomPageActions.dataRoomEditGroupModalConfirm,
        BusinessCaseDataRoomPageActions.dataRoomDeleteGroupModalConfirm,
        BusinessCaseDataRoomPageActions.dataRoomReorderGroupsAndFields,
      ),
      concatLatestFrom(() => [
        this.store.select(selectBusinessCase),
        this.store.select(selectAccessRights),
        this.store.select(selectUserCustomerKey),
      ]),
      switchMap(
        ([{ groupsManaged }, businessCase, accessRights, customerKey]) => {
          return this.businessModalService
            .editBusinessCaseGroup(
              businessCase,
              groupsManaged,
              customerKey,
              accessRights.granular.isRepresentingLeadPartner,
            )
            .pipe(
              map((updatedCase) =>
                BusinessCaseDataRoomApiActions.dataRoomManageGroupModalConfirmSuccess(
                  { businessCase: updatedCase },
                ),
              ),
              catchError((error) =>
                of(
                  BusinessCaseDataRoomApiActions.dataRoomManageGroupModalConfirmFailure(
                    { error },
                  ),
                ),
              ),
            );
        },
      ),
    ),
  );

  refreshBusinessCaseGroup$ = createEffect(() =>
    this.actions$.pipe(
      ofType(
        BusinessCaseDataRoomApiActions.dataRoomManageGroupModalConfirmSuccess,
      ),
      map(({ businessCase }) =>
        StateLibBusinessCasePageActions.setBusinessCase({
          payload: {
            ...businessCase,
            lastModifiedDate: this.dateService.dateToISOString(),
          },
        }),
      ),
    ),
  );

  showSuccessToastMessage$ = createEffect(
    () =>
      this.actions$.pipe(
        ofType(
          BusinessCaseDataRoomApiActions.dataRoomManageGroupModalConfirmSuccess,
        ),
        tap(() => this.finToastService.show(Toast.success())),
      ),
    { dispatch: false },
  );

  showFailureToastMessage$ = createEffect(
    () =>
      this.actions$.pipe(
        ofType(
          BusinessCaseDataRoomApiActions.dataRoomManageGroupModalConfirmFailure,
        ),
        tap(() => this.finToastService.show(Toast.error())),
      ),
    { dispatch: false },
  );

  showEditGroupDataRoomModal$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(BusinessCaseDataRoomPageActions.dataRoomEditGroupModal),
      concatLatestFrom(() => [
        this.store.select(selectBusinessCase),
        this.store.select(selectAccessRights),
        this.store.select(selectUserCustomerKey),
      ]),
      switchMap(([{ group }, businessCase, access, customerKey]) => {
        const modalRef = this.finModalService.open<
          (GroupDto | CadrGroup)[],
          DataRoomManageGroupModalComponent
        >(DataRoomManageGroupModalComponent, {
          data: {
            isEdit: true,
            group,
            groupsOrdered:
              businessCase.businessCaseTemplate.template.groupsOrdered || [],
            isBusinessCaseDataRoom: true,
            businessCase,
            isCustomerParticipant:
              access.granular.isRepresentingCurrentUserCustomer &&
              !access.granular.isRepresentingLeadPartner,
            customerKey,
            isRepresentingLeadPartner:
              access.granular.isRepresentingLeadPartner,
          },
          size: this.finSize.S,
          disableClose: true,
        });

        return modalRef.afterClosed().pipe(
          filter((resp) => resp && !!resp.length),
          map((groupsManaged) =>
            BusinessCaseDataRoomPageActions.dataRoomEditGroupModalConfirm({
              groupsManaged,
            }),
          ),
          defaultIfEmpty(
            BusinessCaseDataRoomPageActions.dataRoomEditGroupModalReject(),
          ),
        );
      }),
    );
  });

  showDeleteGroupDataRoomModal$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(BusinessCaseDataRoomPageActions.dataRoomDeleteGroupModal),
      concatLatestFrom(() => [this.store.select(selectBusinessCase)]),
      switchMap(([{ group }, businessCase]) => {
        const modalRef = this.finModalService.open<
          (GroupDto | CadrGroup)[],
          DataRoomDeleteGroupModalComponent
        >(DataRoomDeleteGroupModalComponent, {
          data: {
            group,
            groupsOrdered:
              businessCase.businessCaseTemplate.template.groupsOrdered || [],
          },
          size: this.finSize.S,
          disableClose: true,
        });

        return modalRef.afterClosed().pipe(
          filter((resp) => resp && !!resp.length),
          map((groupsManaged) =>
            BusinessCaseDataRoomPageActions.dataRoomDeleteGroupModalConfirm({
              groupsManaged,
            }),
          ),
          defaultIfEmpty(
            BusinessCaseDataRoomPageActions.dataRoomDeleteGroupModalReject(),
          ),
        );
      }),
    );
  });

  showRenameFolderDataRoomModal$ = createEffect(() =>
    this.actions$.pipe(
      ofType(StateLibFolderStructureFolderPageActions.openRenameFolderModal),
      switchMap(({ groupKey, name, id }) =>
        this.finModalService
          .open<string, ManageFolderModalComponent>(
            ManageFolderModalComponent,
            {
              data: {
                groupKey,
                folderId: id,
                folderName: name,
              },
              size: FinSize.S,
            },
          )
          .afterClosed()
          .pipe(
            filter(Boolean),
            map((folderName) =>
              StateLibFolderStructureFolderPageActions.renameFolder({
                name: folderName,
                id,
              }),
            ),
            defaultIfEmpty(
              BusinessCaseDataRoomPageActions.dataRoomRenameFolderModalReject(),
            ),
          ),
      ),
    ),
  );

  openMoveFolderModal$ = createEffect(() =>
    this.actions$.pipe(
      ofType(StateLibFolderStructureFolderPageActions.openMoveFolderModal),
      concatLatestFrom(() => [
        this.store.select(selectBusinessCaseInformation),
        this.store.select(selectGroupsOrdered),
      ]),
      switchMap(
        ([{ groupKey, folderId }, businessCaseInformation, groupsOrdered]) =>
          this.finModalService
            .open<
              {
                sourceFolderGroup: Group;
                targetFolderId: string;
                targetFolderGroup: Group;
              },
              MoveFileModalComponent
            >(MoveFileModalComponent, {
              data: {
                sourceGroupKey: groupKey,
                businessCaseInformation,
                groupsOrdered,
              },
              size: FinSize.XL,
            })
            .componentInstance.confirmMove.pipe(
              // move folder modal success closes all
              takeUntil(this.finModalService.afterAllClosed),
              map(({ sourceFolderGroup, targetFolderId, targetFolderGroup }) =>
                StateLibFolderStructureFolderPageActions.moveFolder({
                  folderId,
                  targetFolderId,
                  targetFolderGroup,
                  sourceFolderGroup: sourceFolderGroup,
                }),
              ),
            ),
      ),
    ),
  );

  openMoveDocumentModal$ = createEffect(() =>
    this.actions$.pipe(
      ofType(StateLibFolderStructureFolderPageActions.openMoveDocumentModal),
      concatLatestFrom(() => [
        this.store.select(selectBusinessCaseInformation),
        this.store.select(selectGroupsOrdered),
      ]),
      switchMap(
        ([
          { groupKey, documentField },
          businessCaseInformation,
          groupsOrdered,
        ]) => {
          return this.finModalService
            .open<
              {
                sourceFolderGroup: Group;
                targetFolderId: string;
                targetFolderGroup: Group;
              },
              MoveFileModalComponent
            >(MoveFileModalComponent, {
              data: {
                sourceGroupKey: groupKey,
                businessCaseInformation,
                groupsOrdered,
              },
              size: FinSize.XL,
            })
            .componentInstance.confirmMove.pipe(
              // move folder modal success closes all
              takeUntil(this.finModalService.afterAllClosed),
              map(({ sourceFolderGroup, targetFolderId, targetFolderGroup }) =>
                StateLibFolderStructureDocumentPageActions.moveDocument({
                  documentField,
                  targetFolderId,
                  targetFolderGroup,
                  sourceFolderGroup,
                }),
              ),
            );
        },
      ),
    ),
  );

  constructor(
    private actions$: Actions,
    private finModalService: FinModalService,
    private store: Store,
    private dateService: DateService,
    private finToastService: FinToastService,
    private businessModalService: BusinessCaseModalService,
  ) {}
}
