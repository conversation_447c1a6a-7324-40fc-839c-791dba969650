import { OnboardingStep } from '@fincloud/types/models';
import { CUSTOMIZATIONS_ONBOARDING_STEP } from './customizations-onboarding-step';
import { INTRO_ONBOARDING_STEP } from './intro-onboarding-step';

export const ONBOARDING_TIPS_NOTIFICATION_SYSTEM: OnboardingStep[] = [
  {
    stepName: INTRO_ONBOARDING_STEP,
    header: {
      title: $localize`:@@notificationSystem.onboardingTip.title.intro:Verpassen Sie keine Informationen`,
      subTitle: $localize`:@@notificationSystem.onboardingTip.text.intro:Prüfen Sie alle Aktivitäten seit Ihrem letzten Besuch über unser Benachrichtigungssystem.`,
    },
    imageSrc:
      './assets/images/notification-system/tips-notification-system-intro.svg',
    nextStepButtonLabel: $localize`:@@button.label.next:<PERSON>ter`,
    mediaClass: 'tw-w-[27rem]',
  },
  {
    stepName: CUSTOMIZATIONS_ONBOARDING_STEP,
    header: {
      title: $localize`:@@notificationSystem.onboardingTip.title.customizations:Verwalten Sie Ihre Benachrichtigungen`,
      subTitle: $localize`:@@notificationSystem.onboardingTip.text.customizations:Sie entscheiden was für Sie wichtig ist und können Ihre Benachrichtigungseinstellungen jederzeit anpassen.`,
    },
    imageSrc: $localize`:@@notificationSystem.onboardingTip.image.customizations:./assets/images/notification-system/tips-notification-system-customizations.svg`,
    nextStepButtonLabel: $localize`:@@button.label.understood:Verstanden`,
    cssClass: 'second-step',
    mediaClass: 'tw-w-[27rem]',
  },
];
