import { INgxbStepOption } from 'ngx-ui-tour-ngx-bootstrap/lib/step-option.interface';
import { NotificationSteps } from '../enums/notification-steps';
import { CUSTOMIZATIONS_ONBOARDING_STEP } from './customizations-onboarding-step';
import { INTRO_ONBOARDING_STEP } from './intro-onboarding-step';

export const NOTIFICATIONS_TOUR_CONFIG: INgxbStepOption[] = [
  {
    anchorId: NotificationSteps.NOTIFICATION_SYSTEM_INTRO,
    popoverClass: 'tour-onboarding-step-notification-system',
    stepId: INTRO_ONBOARDING_STEP,
  },
  {
    anchorId: NotificationSteps.NOTIFICATION_SYSTEM_INTRO,
    stepId: CUSTOMIZATIONS_ONBOARDING_STEP,
    popoverClass: 'tour-onboarding-step-notification-system',
  },
];
