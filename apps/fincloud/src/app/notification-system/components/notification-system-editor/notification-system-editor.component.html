<div class="onboarding-tip-notifications-panel"></div>
<div [tourAnchor]="notificationSteps.NOTIFICATION_SYSTEM_INTRO">
  <app-notification-bell
    class="notification-bell notifications-context"
    (click)="toggleMode$.next(true)"
    [isExpandedMode]="(mode$ | async) === NotificationEditorMode.EXPANDED"
    cdkOverlayOrigin
    #notificationButton="cdkOverlayOrigin"
  >
  </app-notification-bell>
  <tour-step-template>
    <ng-template #tourStep let-step="step">
      <div class="tips-container">
        <ui-onboarding-tips
          [onboardingSteps]="onboardingTips"
          (markTipsAsRead)="finishOnboarding($event)"
        >
        </ui-onboarding-tips>
      </div>
    </ng-template>
  </tour-step-template>

  @if (mode$ | async; as mode) {
    <ng-template
      cdkConnectedOverlay
      [cdkConnectedOverlayOrigin]="notificationButton"
      [cdkConnectedOverlayOpen]="mode === NotificationEditorMode.EXPANDED"
      [cdkConnectedOverlayPositions]="summaryPanelPositions"
      [cdkConnectedOverlayPush]="true"
      [cdkConnectedOverlayPanelClass]="['tw-pt-[1.2rem]']"
      (detach)="closeNotificationList(mode)"
    >
      <app-notification-system-list
        finContainer
        [boxShadow]="true"
        [borderRadius]="true"
        (closed)="toggleMode$.next(true)"
      ></app-notification-system-list>
    </ng-template>
  }
</div>
