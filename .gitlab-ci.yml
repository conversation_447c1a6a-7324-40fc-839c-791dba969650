# You can override the included template(s) by including variable overrides
# SAST customization: https://docs.gitlab.com/ee/user/application_security/sast/#customizing-the-sast-settings
# Secret Detection customization: https://docs.gitlab.com/ee/user/application_security/secret_detection/#customizing-settings
# Dependency Scanning customization: https://docs.gitlab.com/ee/user/application_security/dependency_scanning/#customizing-the-dependency-scanning-settings
# Container Scanning customization: https://docs.gitlab.com/ee/user/application_security/container_scanning/#customizing-the-container-scanning-settings
# Note that environment variables can be set in several places
# See https://docs.gitlab.com/ee/ci/variables/#cicd-variable-precedence
include:
  - project: fincloud/cfg-ci-cd
    ref: feature/docker-build-cache
    file:
      - .ci-dashboard.yml

variables:
  SERVICE_BASENAME: app-dashboard
  DOCKER_REPO: docker-v2.neo.loan/fincloud
  HELM_CHART_DIR: helm-chart
  HELM_NAMESPACE: default
  HELM_CHART_SET_VALUES: ' --set image.tag=${CI_COMMIT_SHORT_SHA} --set global.baseUrl=${BASE_URL_PORTAL#*://} --set global.instana.trackingKey=${TRACKINGKEY} --set global.posthog.key=${POSTHOG_KEY} --set global.sentry.dsn=${SENTRY_DSN} --set global.sentry.environment=${TARGET}'
